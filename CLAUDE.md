# Claude Code Project Configuration

## Project Overview
Rent Collection Toolkit - A comprehensive solution for managing rent collection processes, tenant management, and case logging.

## Project Structure
- `rct-backend/` - .NET Core Web API backend
- `rct-frontend/` - SvelteKit frontend application

## Development Commands

### Backend (.NET)
```bash
# Navigate to backend
cd rct-backend

# Restore packages
dotnet restore

# Build
dotnet build

# Run development server
dotnet run --project RCT.Api

# Run tests
dotnet test
```

### Frontend (SvelteKit)
```bash
# Navigate to frontend
cd rct-frontend

# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run tests
npm test

# Lint
npm run lint

# Type check
npm run check
```

## Key Technologies
- **Backend**: .NET Core, Entity Framework Core, SQL Server
- **Frontend**: SvelteKit, TypeScript, Tailwind CSS
- **Authentication**: JWT tokens
- **Database**: SQL Server with Entity Framework migrations

## Common Patterns
- Follow existing code conventions in each project
- Use TypeScript for all frontend code
- Follow C# naming conventions for backend
- Use Entity Framework for database operations
- JWT-based authentication system

## Notes
- Project uses multi-tenant architecture
- Database migrations are managed through Entity Framework
- Frontend uses SvelteKit's file-based routing