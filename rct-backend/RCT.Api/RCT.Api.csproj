﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
	  <!-- Database -->
	  <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>

	  <!-- Auth -->
	  <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
	  <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />

	  <!-- Open Api -->
	  <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />

	  <!-- Azure Blob Storage -->
	  <PackageReference Include="Azure.Storage.Blobs" Version="12.24.1" />
  </ItemGroup>

</Project>
