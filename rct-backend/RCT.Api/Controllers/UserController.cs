﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RCT.Api.Database;
using RCT.Api.Database.Enums;
using RCT.Api.DTOs.User;
using System.ComponentModel.DataAnnotations;

namespace RCT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class UserController : Controller
{
    private readonly DatabaseContext _dbContext;

    public UserController(DatabaseContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet]
    public async Task<IActionResult> GetUser()
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var user = await _dbContext.Users
            .Include(u => u.UserTenantLinks)
            .ThenInclude(utl => utl.Tenant)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
            return NotFound("User not found.");

        var userTenantLink = user.UserTenantLinks?.FirstOrDefault();
        
        var userDto = new UserDto
        {
            Id = user.Id,
            Name = $"{user.FirstName} {user.LastName}",
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Role = user.Role,
            UserType = user.UserType,
            JobTitle = user.JobTitle,
            Department = user.Department,
            OrganisationId = userTenantLink?.TenantId.ToString() ?? "",
            OrganisationName = userTenantLink?.Tenant?.Name ?? ""
        };

        return Ok(userDto);
    }

    [HttpPut]
    public async Task<IActionResult> UpdateUser([FromBody] UpdateUserRequestDto request)
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null)
            return NotFound("User not found.");

        // Update user properties
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.JobTitle = request.JobTitle;
        user.Department = request.Department;
        user.UpdatedAtUtc = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        // Return updated user data
        var userTenantLink = user.UserTenantLinks?.FirstOrDefault();
        var userDto = new UserDto
        {
            Id = user.Id,
            Name = $"{user.FirstName} {user.LastName}",
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Role = user.Role,
            UserType = user.UserType,
            JobTitle = user.JobTitle,
            Department = user.Department,
            OrganisationId = userTenantLink?.TenantId.ToString() ?? "",
            OrganisationName = userTenantLink?.Tenant?.Name ?? ""
        };

        return Ok(userDto);
    }
}
