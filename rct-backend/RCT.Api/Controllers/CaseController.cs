using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RCT.Api.Database;
using RCT.Api.Database.Tables;
using RCT.Api.DTOs.Case;

namespace RCT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CaseController : Controller
{
    private readonly DatabaseContext _dbContext;

    public CaseController(DatabaseContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet]
    public async Task<IActionResult> GetCases()
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var cases = await _dbContext.Cases
            .Where(c => c.UserId == userId)
            .OrderByDescending(c => c.UpdatedAtUtc)
            .Select(c => new CaseDto
            {
                Id = c.Id,
                TenantId = c.TenantId,
                UserId = c.UserId,
                Reference = c.Reference,
                Name = c.Name,
                CurrentBalance = c.CurrentBalance,
                WeeklyRent = c.WeeklyRent,
                TenantMonthlyPayment = c.TenantMonthlyPayment,
                ApaHbMonthlyPayment = c.ApaHbMonthlyPayment,
                TpdMonthlyPayment = c.TpdMonthlyPayment,
                TenantWeeklyPayment = c.TenantWeeklyPayment,
                BenefitsHbWeeklyPayment = c.BenefitsHbWeeklyPayment,
                WeeksToNextPay = c.WeeksToNextPay,
                PaymentDue = c.PaymentDue,
                Timeframe = c.Timeframe,
                UseWeeklyFrequency = c.UseWeeklyFrequency,
                GrossWeeklyRent = c.GrossWeeklyRent,
                RentComponent = c.RentComponent,
                NonUcServiceChargeFormula = c.NonUcServiceChargeFormula,
                NonUcServiceChargeTotal = c.NonUcServiceChargeTotal,
                GrossWeeklyRentOverridden = c.GrossWeeklyRentOverridden,
                Notes = c.Notes,
                CreatedAt = c.CreatedAtUtc,
                UpdatedAt = c.UpdatedAtUtc
            })
            .ToListAsync();

        return Ok(cases);
    }

    [HttpGet("SearchByReference/{reference}")]
    public async Task<IActionResult> SearchByReference(string reference)
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var caseEntity = await _dbContext.Cases
            .Where(c => c.Reference == reference && c.UserId == userId)
            .Select(c => new CaseDto
            {
                Id = c.Id,
                TenantId = c.TenantId,
                UserId = c.UserId,
                Reference = c.Reference,
                Name = c.Name,
                CurrentBalance = c.CurrentBalance,
                WeeklyRent = c.WeeklyRent,
                TenantMonthlyPayment = c.TenantMonthlyPayment,
                ApaHbMonthlyPayment = c.ApaHbMonthlyPayment,
                TpdMonthlyPayment = c.TpdMonthlyPayment,
                TenantWeeklyPayment = c.TenantWeeklyPayment,
                BenefitsHbWeeklyPayment = c.BenefitsHbWeeklyPayment,
                WeeksToNextPay = c.WeeksToNextPay,
                PaymentDue = c.PaymentDue,
                Timeframe = c.Timeframe,
                UseWeeklyFrequency = c.UseWeeklyFrequency,
                GrossWeeklyRent = c.GrossWeeklyRent,
                RentComponent = c.RentComponent,
                NonUcServiceChargeFormula = c.NonUcServiceChargeFormula,
                NonUcServiceChargeTotal = c.NonUcServiceChargeTotal,
                GrossWeeklyRentOverridden = c.GrossWeeklyRentOverridden,
                Notes = c.Notes,
                CreatedAt = c.CreatedAtUtc,
                UpdatedAt = c.UpdatedAtUtc
            })
            .FirstOrDefaultAsync();

        if (caseEntity == null)
            return NotFound();

        return Ok(caseEntity);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetCase(Guid id)
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var caseEntity = await _dbContext.Cases
            .Where(c => c.Id == id && c.UserId == userId)
            .Select(c => new CaseDto
            {
                Id = c.Id,
                TenantId = c.TenantId,
                UserId = c.UserId,
                Reference = c.Reference,
                Name = c.Name,
                CurrentBalance = c.CurrentBalance,
                WeeklyRent = c.WeeklyRent,
                TenantMonthlyPayment = c.TenantMonthlyPayment,
                ApaHbMonthlyPayment = c.ApaHbMonthlyPayment,
                TpdMonthlyPayment = c.TpdMonthlyPayment,
                TenantWeeklyPayment = c.TenantWeeklyPayment,
                BenefitsHbWeeklyPayment = c.BenefitsHbWeeklyPayment,
                WeeksToNextPay = c.WeeksToNextPay,
                PaymentDue = c.PaymentDue,
                Timeframe = c.Timeframe,
                UseWeeklyFrequency = c.UseWeeklyFrequency,
                GrossWeeklyRent = c.GrossWeeklyRent,
                RentComponent = c.RentComponent,
                NonUcServiceChargeFormula = c.NonUcServiceChargeFormula,
                NonUcServiceChargeTotal = c.NonUcServiceChargeTotal,
                GrossWeeklyRentOverridden = c.GrossWeeklyRentOverridden,
                Notes = c.Notes,
                CreatedAt = c.CreatedAtUtc,
                UpdatedAt = c.UpdatedAtUtc
            })
            .FirstOrDefaultAsync();

        if (caseEntity == null)
            return NotFound();

        return Ok(caseEntity);
    }

    [HttpPost]
    public async Task<IActionResult> CreateCase([FromBody] CreateCaseRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userIdClaim = User.FindFirst("UserId");
        var tenantIdClaim = User.FindFirst("TenantId");

        if (userIdClaim == null || tenantIdClaim == null)
            return Unauthorized("Required claims not found.");

        var userId = Guid.Parse(userIdClaim.Value);
        var tenantId = Guid.Parse(tenantIdClaim.Value);

        var caseEntity = new Case
        {
            Id = Guid.NewGuid(),
            TenantId = tenantId,
            UserId = userId,
            Reference = request.Reference,
            Name = request.Name,
            CurrentBalance = request.CurrentBalance,
            WeeklyRent = request.WeeklyRent,
            TenantMonthlyPayment = request.TenantMonthlyPayment,
            ApaHbMonthlyPayment = request.ApaHbMonthlyPayment,
            TpdMonthlyPayment = request.TpdMonthlyPayment,
            TenantWeeklyPayment = request.TenantWeeklyPayment,
            BenefitsHbWeeklyPayment = request.BenefitsHbWeeklyPayment,
            WeeksToNextPay = request.WeeksToNextPay,
            PaymentDue = request.PaymentDue,
            Timeframe = request.Timeframe,
            UseWeeklyFrequency = request.UseWeeklyFrequency,
            GrossWeeklyRent = request.GrossWeeklyRent,
            RentComponent = request.RentComponent,
            NonUcServiceChargeFormula = request.NonUcServiceChargeFormula,
            NonUcServiceChargeTotal = request.NonUcServiceChargeTotal,
            GrossWeeklyRentOverridden = request.GrossWeeklyRentOverridden,
            Notes = request.Notes,
            CreatedAtUtc = DateTime.UtcNow,
            UpdatedAtUtc = DateTime.UtcNow
        };

        _dbContext.Cases.Add(caseEntity);
        await _dbContext.SaveChangesAsync();

        var dto = new CaseDto
        {
            Id = caseEntity.Id,
            TenantId = caseEntity.TenantId,
            UserId = caseEntity.UserId,
            Reference = caseEntity.Reference,
            Name = caseEntity.Name,
            CurrentBalance = caseEntity.CurrentBalance,
            WeeklyRent = caseEntity.WeeklyRent,
            TenantMonthlyPayment = caseEntity.TenantMonthlyPayment,
            ApaHbMonthlyPayment = caseEntity.ApaHbMonthlyPayment,
            TpdMonthlyPayment = caseEntity.TpdMonthlyPayment,
            TenantWeeklyPayment = caseEntity.TenantWeeklyPayment,
            BenefitsHbWeeklyPayment = caseEntity.BenefitsHbWeeklyPayment,
            WeeksToNextPay = caseEntity.WeeksToNextPay,
            PaymentDue = caseEntity.PaymentDue,
            Timeframe = caseEntity.Timeframe,
            UseWeeklyFrequency = caseEntity.UseWeeklyFrequency,
            GrossWeeklyRent = caseEntity.GrossWeeklyRent,
            RentComponent = caseEntity.RentComponent,
            NonUcServiceChargeFormula = caseEntity.NonUcServiceChargeFormula,
            NonUcServiceChargeTotal = caseEntity.NonUcServiceChargeTotal,
            GrossWeeklyRentOverridden = caseEntity.GrossWeeklyRentOverridden,
            Notes = caseEntity.Notes,
            CreatedAt = caseEntity.CreatedAtUtc,
            UpdatedAt = caseEntity.UpdatedAtUtc
        };

        return CreatedAtAction(nameof(GetCase), new { id = caseEntity.Id }, dto);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateCase(Guid id, [FromBody] UpdateCaseRequestDto request)
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var caseEntity = await _dbContext.Cases
            .FirstOrDefaultAsync(c => c.Id == id && c.UserId == userId);

        if (caseEntity == null)
            return NotFound();

        // Update only provided fields
        if (request.Reference != null)
            caseEntity.Reference = request.Reference;
        if (request.Name != null)
            caseEntity.Name = request.Name;
        if (request.CurrentBalance.HasValue)
            caseEntity.CurrentBalance = request.CurrentBalance.Value;
        if (request.WeeklyRent.HasValue)
            caseEntity.WeeklyRent = request.WeeklyRent.Value;
        if (request.TenantMonthlyPayment.HasValue)
            caseEntity.TenantMonthlyPayment = request.TenantMonthlyPayment.Value;
        if (request.ApaHbMonthlyPayment.HasValue)
            caseEntity.ApaHbMonthlyPayment = request.ApaHbMonthlyPayment.Value;
        if (request.TpdMonthlyPayment.HasValue)
            caseEntity.TpdMonthlyPayment = request.TpdMonthlyPayment.Value;
        if (request.TenantWeeklyPayment.HasValue)
            caseEntity.TenantWeeklyPayment = request.TenantWeeklyPayment.Value;
        if (request.BenefitsHbWeeklyPayment.HasValue)
            caseEntity.BenefitsHbWeeklyPayment = request.BenefitsHbWeeklyPayment.Value;
        if (request.WeeksToNextPay.HasValue)
            caseEntity.WeeksToNextPay = request.WeeksToNextPay.Value;
        if (request.PaymentDue.HasValue)
            caseEntity.PaymentDue = request.PaymentDue.Value;
        if (request.Timeframe.HasValue)
            caseEntity.Timeframe = request.Timeframe.Value;
        if (request.UseWeeklyFrequency.HasValue)
            caseEntity.UseWeeklyFrequency = request.UseWeeklyFrequency.Value;
        if (request.GrossWeeklyRent.HasValue)
            caseEntity.GrossWeeklyRent = request.GrossWeeklyRent.Value;
        if (request.RentComponent.HasValue)
            caseEntity.RentComponent = request.RentComponent.Value;
        if (request.NonUcServiceChargeFormula != null)
            caseEntity.NonUcServiceChargeFormula = request.NonUcServiceChargeFormula;
        if (request.NonUcServiceChargeTotal.HasValue)
            caseEntity.NonUcServiceChargeTotal = request.NonUcServiceChargeTotal.Value;
        if (request.GrossWeeklyRentOverridden.HasValue)
            caseEntity.GrossWeeklyRentOverridden = request.GrossWeeklyRentOverridden.Value;
        if (request.Notes != null)
            caseEntity.Notes = request.Notes;

        caseEntity.UpdatedAtUtc = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        return NoContent();
    }

    [HttpPut("{id}/notes")]
    public async Task<IActionResult> UpdateCaseNotes(Guid id, [FromBody] UpdateCaseNotesRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var caseEntity = await _dbContext.Cases
            .FirstOrDefaultAsync(c => c.Id == id && c.UserId == userId);

        if (caseEntity == null)
            return NotFound();

        caseEntity.Notes = request.Notes;
        caseEntity.UpdatedAtUtc = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteCase(Guid id)
    {
        var userIdClaim = User.FindFirst("UserId");
        if (userIdClaim == null)
            return Unauthorized("UserId claim not found.");

        var userId = Guid.Parse(userIdClaim.Value);

        var caseEntity = await _dbContext.Cases
            .FirstOrDefaultAsync(c => c.Id == id && c.UserId == userId);

        if (caseEntity == null)
            return NotFound();

        _dbContext.Cases.Remove(caseEntity);
        await _dbContext.SaveChangesAsync();

        return NoContent();
    }
}