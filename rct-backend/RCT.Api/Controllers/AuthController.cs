﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Client;
using RCT.Api.Common.Utils;
using RCT.Api.Database;
using RCT.Api.DTOs.Auth;
using RCT.Api.Services;
using System.Security.Claims;

namespace RCT.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class AuthController : Controller
{
    private readonly IConfiguration _configuration;
    private readonly DatabaseContext _dbContext;
    private readonly AccountService _accountService;

    public AuthController(IConfiguration configuration, DatabaseContext dbContext, AccountService accountService)
    {
        _configuration = configuration;
        _dbContext = dbContext;
        _accountService = accountService;
    }

    [HttpPost]
    public async Task<IActionResult> Login([FromBody] LoginRequestDto loginRequestModel)
    {
        if (string.IsNullOrWhiteSpace(loginRequestModel.Email) || string.IsNullOrWhiteSpace(loginRequestModel.Password))
            return BadRequest(new LoginResponseDto { Success = false, Error = "Email and password are required." });

        var user = await _accountService.LoginAsync(loginRequestModel);
        if (user == null)
            return BadRequest(new LoginResponseDto { Success = false, Error = "Invalid email or password. Please check your credentials and try again." });

        var userTenantLink = await _dbContext.UserTenantLinks.FirstOrDefaultAsync(utl => utl.UserId == user.Id);
        var tenantId = userTenantLink?.TenantId ?? Guid.Empty;

        var response = new LoginResponseDto
        {
            Success = true
        };

        var claims = new[]
        {
            new Claim("UserId", user.Id.ToString()),
            new Claim("TenantId", tenantId.ToString())
        };

        response.Token = JwtTokenHelper.CreateToken(secret: _configuration["Security:Code"]!,
                                                    claims: claims,
                                                    expiryDate: DateTime.UtcNow.AddDays(10000));

        return Ok(response);
    }

    [HttpPost]
    public async Task<IActionResult> Register([FromBody] RegisterRequestDto registerRequestModel)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var (user, tenant, error) = await _accountService.RegisterAsync(registerRequestModel);
        
        if (!string.IsNullOrEmpty(error))
            return BadRequest(new RegisterResponseDto { Success = false, Error = error });

        if (user == null || tenant == null)
            return BadRequest(new RegisterResponseDto { Success = false, Error = "Registration failed." });

        // Generate JWT token for immediate login
        var claims = new[]
        {
            new Claim("UserId", user.Id.ToString()),
            new Claim("TenantId", tenant.Id.ToString())
        };

        var token = JwtTokenHelper.CreateToken(
            secret: _configuration["Security:Code"]!,
            claims: claims,
            expiryDate: DateTime.UtcNow.AddDays(10000)
        );

        var response = new RegisterResponseDto
        {
            Success = true,
            Token = token,
        };

        return Ok(response);
    }
}
