using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RCT.Api.Database;
using RCT.Api.Database.Tables;
using RCT.Api.DTOs.Tenant;

namespace RCT.Api.Controllers;

[ApiController]
[Route("[controller]")]
[Authorize]
public class TenantController : ControllerBase
{
    private readonly DatabaseContext _dbContext;

    public TenantController(DatabaseContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet("summary")]
    public async Task<IActionResult> GetTenantsSummary()
    {
        var tenants = await _dbContext.Tenants
            .Select(t => new TenantSummaryDto
            {
                Id = t.Id,
                Name = t.Name,
                CreatedAt = t.CreatedAtUtc,
                UpdatedAt = t.UpdatedAtUtc,
                UserCount = t.UserTenantLinks!.Count(),
                CaseCount = t.Cases!.Count()
            })
            .OrderBy(t => t.Name)
            .ToListAsync();

        return Ok(tenants);
    }

    [HttpGet]
    public async Task<IActionResult> GetTenants(int page = 1, int pageSize = 20)
    {
        var skip = (page - 1) * pageSize;
        
        var tenants = await _dbContext.Tenants
            .Include(t => t.UserTenantLinks)
            .ThenInclude(utl => utl.User)
            .Select(t => new TenantDto
            {
                Id = t.Id,
                Name = t.Name,
                CreatedAt = t.CreatedAtUtc,
                UpdatedAt = t.UpdatedAtUtc,
                UserCount = t.UserTenantLinks!.Count(),
                CaseCount = t.Cases!.Count(),
                Users = t.UserTenantLinks!.Select(utl => new UserSummaryDto
                {
                    Id = utl.User.Id,
                    Email = utl.User.Email,
                    FirstName = utl.User.FirstName,
                    LastName = utl.User.LastName,
                    UserType = utl.User.UserType,
                    JobTitle = utl.User.JobTitle,
                    Department = utl.User.Department
                }).ToList()
            })
            .OrderBy(t => t.Name)
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();

        var totalCount = await _dbContext.Tenants.CountAsync();

        var result = new PaginatedTenantsDto
        {
            Tenants = tenants,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };

        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetTenant(Guid id)
    {
        var tenant = await _dbContext.Tenants
            .Include(t => t.UserTenantLinks)
            .ThenInclude(utl => utl.User)
            .Include(t => t.Cases)
            .Where(t => t.Id == id)
            .Select(t => new TenantDto
            {
                Id = t.Id,
                Name = t.Name,
                CreatedAt = t.CreatedAtUtc,
                UpdatedAt = t.UpdatedAtUtc,
                UserCount = t.UserTenantLinks != null ? t.UserTenantLinks.Count() : 0,
                CaseCount = t.Cases != null ? t.Cases.Count() : 0,
                Users = t.UserTenantLinks != null ? t.UserTenantLinks.Select(utl => new UserSummaryDto
                {
                    Id = utl.User.Id,
                    Email = utl.User.Email,
                    FirstName = utl.User.FirstName,
                    LastName = utl.User.LastName,
                    UserType = utl.User.UserType,
                    JobTitle = utl.User.JobTitle,
                    Department = utl.User.Department
                }).ToList() : new List<UserSummaryDto>()
            })
            .FirstOrDefaultAsync();

        if (tenant == null)
            return NotFound("Tenant not found.");

        return Ok(tenant);
    }
}

