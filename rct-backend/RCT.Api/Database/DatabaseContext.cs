﻿using Microsoft.EntityFrameworkCore;
using RCT.Api.Database.Tables;
using RCT.Api.Migrations;

namespace RCT.Api.Database;

public class DatabaseContext(DbContextOptions<DatabaseContext> options) : DbContext(options)
{
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        foreach (var relationship in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
        {
            relationship.DeleteBehavior = DeleteBehavior.Restrict;
        }
    }

    public DbSet<Tenant> Tenants { get; set; }

    public DbSet<User> Users { get; set; }

    public DbSet<UserTenantLink> UserTenantLinks { get; set; }

    public DbSet<Case> Cases { get; set; }
}
