﻿using System.ComponentModel.DataAnnotations;

namespace RCT.Api.Database.Tables;

public class Case
{
    public required Guid Id { get; set; }

    public required Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    public required Guid UserId { get; set; }
    public User? User { get; set; }

    [MaxLength(250)]
    public required string Reference { get; set; }

    [MaxLength(250)]
    public string? Name { get; set; }

    // Rent Calculator Data
    public decimal CurrentBalance { get; set; }
    public decimal WeeklyRent { get; set; }
    public decimal TenantMonthlyPayment { get; set; }
    public decimal ApaHbMonthlyPayment { get; set; }
    public decimal TpdMonthlyPayment { get; set; }
    public decimal TenantWeeklyPayment { get; set; }
    public decimal BenefitsHbWeeklyPayment { get; set; }

    // Arrangement Planner Data
    public int WeeksToNextPay { get; set; }
    public decimal PaymentDue { get; set; }
    public int Timeframe { get; set; }
    public bool UseWeeklyFrequency { get; set; }

    // Account Charges Data
    public decimal GrossWeeklyRent { get; set; }
    public decimal RentComponent { get; set; }
    public string? NonUcServiceChargeFormula { get; set; }
    public decimal NonUcServiceChargeTotal { get; set; }
    public bool GrossWeeklyRentOverridden { get; set; }

    // Notes
    public string Notes { get; set; } = string.Empty;

    // Timestamps
    public required DateTime CreatedAtUtc { get; set; }
    public required DateTime UpdatedAtUtc { get; set; }
}