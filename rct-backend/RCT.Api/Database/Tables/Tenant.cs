﻿using System.ComponentModel.DataAnnotations;

namespace RCT.Api.Database.Tables;

public class Tenant
{
    public required Guid Id { get; set; }

    [MaxLength(250)]
    public required string Name { get; set; }

    public ICollection<UserTenantLink>? UserTenantLinks { get; set; }
    public ICollection<Case>? Cases { get; set; }

    public required DateTime CreatedAtUtc { get; set; }
    public required DateTime UpdatedAtUtc { get; set; }
}