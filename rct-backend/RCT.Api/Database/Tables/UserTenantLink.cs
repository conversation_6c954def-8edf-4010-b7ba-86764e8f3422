﻿using System.ComponentModel.DataAnnotations;

namespace RCT.Api.Database.Tables;

public class UserTenantLink
{
    public required Guid Id { get; set; }

    public required Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    public required Guid UserId { get; set; }
    public User? User { get; set; }

    public required DateTime CreatedAtUtc { get; set; }
    public required DateTime UpdatedAtUtc { get; set; }
}