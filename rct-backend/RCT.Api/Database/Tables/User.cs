﻿using System.ComponentModel.DataAnnotations;
using RCT.Api.Database.Enums;

namespace RCT.Api.Database.Tables;

public class User
{
    public required Guid Id { get; set; }

    [MaxLength(250)]
    public required string Email { get; set; }

    [<PERSON>Length(250)]
    public required string FirstName { get; set; }

    [MaxLength(250)]
    public required string LastName { get; set; }

    [MaxLength(250)]
    public required string Role { get; set; }

    [MaxLength(250)]
    public string? JobTitle { get; set; }

    [MaxLength(250)]
    public string? Department { get; set; }

    public required UserType UserType { get; set; }

    public required string PasswordHash { get; set; }

    public ICollection<UserTenantLink>? UserTenantLinks { get; set; }
    public ICollection<Case>? Cases { get; set; }

    public required DateTime CreatedAtUtc { get; set; }
    public required DateTime UpdatedAtUtc { get; set; }
}