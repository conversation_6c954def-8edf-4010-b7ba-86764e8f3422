using System.ComponentModel.DataAnnotations;

namespace RCT.Api.DTOs.Auth;

public class RegisterRequestDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [MinLength(6)]
    public string Password { get; set; } = string.Empty;

    [Required]
    [MaxLength(250)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(250)]
    public string LastName { get; set; } = string.Empty;

}