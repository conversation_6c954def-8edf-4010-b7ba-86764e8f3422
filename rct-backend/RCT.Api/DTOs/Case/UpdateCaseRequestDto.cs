namespace RCT.Api.DTOs.Case;

public class UpdateCaseRequestDto
{
    public string? Reference { get; set; }
    public string? Name { get; set; }
    public decimal? CurrentBalance { get; set; }
    public decimal? WeeklyRent { get; set; }
    public decimal? TenantMonthlyPayment { get; set; }
    public decimal? ApaHbMonthlyPayment { get; set; }
    public decimal? TpdMonthlyPayment { get; set; }
    public decimal? TenantWeeklyPayment { get; set; }
    public decimal? BenefitsHbWeeklyPayment { get; set; }
    public int? WeeksToNextPay { get; set; }
    public decimal? PaymentDue { get; set; }
    public int? Timeframe { get; set; }
    public bool? UseWeeklyFrequency { get; set; }
    public decimal? GrossWeeklyRent { get; set; }
    public decimal? RentComponent { get; set; }
    public string? NonUcServiceChargeFormula { get; set; }
    public decimal? NonUcServiceChargeTotal { get; set; }
    public bool? GrossWeeklyRentOverridden { get; set; }
    public string? Notes { get; set; }
}