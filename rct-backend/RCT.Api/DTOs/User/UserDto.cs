﻿using RCT.Api.Database.Enums;

namespace RCT.Api.DTOs.User;

public class UserDto
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string OrganisationId { get; set; } = string.Empty;
    public string OrganisationName { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public UserType UserType { get; set; }
    public string? JobTitle { get; set; }
    public string? Department { get; set; }
}