using System.ComponentModel.DataAnnotations;

namespace RCT.Api.DTOs.User;

public class UpdateUserRequestDto
{
    [Required]
    [MaxLength(250)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(250)]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(250)]
    public string? JobTitle { get; set; }

    [MaxLength(250)]
    public string? Department { get; set; }
}