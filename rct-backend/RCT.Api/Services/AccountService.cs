﻿using Microsoft.EntityFrameworkCore;
using RCT.Api.Database;
using RCT.Api.Database.Tables;
using RCT.Api.Database.Enums;
using RCT.Api.DTOs.Auth;
using BCrypt.Net;

namespace RCT.Api.Services;

public class AccountService
{
    private readonly DatabaseContext _dbContext;

    public AccountService(DatabaseContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<User?> LoginAsync(LoginRequestDto loginRequestDto)
    {
        // Find user by email
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == loginRequestDto.Email);
        if (user == null)
            return null;

        // Verify password using BCrypt
        if (!string.IsNullOrEmpty(user.PasswordHash) && !BCrypt.Net.BCrypt.Verify(loginRequestDto.Password, user.PasswordHash))
            return null;

        return user;
    }

    public async Task<(User? User, Tenant? Tenant, string? Error)> RegisterAsync(RegisterRequestDto registerRequestDto)
    {
        using var transaction = await _dbContext.Database.BeginTransactionAsync();
        
        try
        {
            // Check if email already exists
            var existingUser = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == registerRequestDto.Email);
            if (existingUser != null)
                return (null, null, "An account with this email already exists.");

            // Hash password
            var passwordHash = BCrypt.Net.BCrypt.HashPassword(registerRequestDto.Password);

            // Create new tenant with default name based on user
            var tenant = new Tenant
            {
                Id = Guid.NewGuid(),
                Name = $"{registerRequestDto.FirstName} {registerRequestDto.LastName}'s Organisation",
                CreatedAtUtc = DateTime.UtcNow,
                UpdatedAtUtc = DateTime.UtcNow
            };

            _dbContext.Tenants.Add(tenant);
            await _dbContext.SaveChangesAsync();

            // Create new user
            var user = new User
            {
                Id = Guid.NewGuid(),
                Email = registerRequestDto.Email,
                PasswordHash = passwordHash,
                FirstName = registerRequestDto.FirstName,
                LastName = registerRequestDto.LastName,
                Role = "agent", // Default role
                UserType = UserType.Admin, // Default to Admin (type 2)
                CreatedAtUtc = DateTime.UtcNow,
                UpdatedAtUtc = DateTime.UtcNow
            };

            _dbContext.Users.Add(user);
            await _dbContext.SaveChangesAsync();

            // Create user-tenant link
            var userTenantLink = new UserTenantLink
            {
                Id = Guid.NewGuid(),
                UserId = user.Id,
                TenantId = tenant.Id,
                CreatedAtUtc = DateTime.UtcNow,
                UpdatedAtUtc = DateTime.UtcNow
            };

            _dbContext.UserTenantLinks.Add(userTenantLink);
            await _dbContext.SaveChangesAsync();

            await transaction.CommitAsync();
            return (user, tenant, null);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            return (null, null, $"Registration failed: {ex.Message}");
        }
    }
}
