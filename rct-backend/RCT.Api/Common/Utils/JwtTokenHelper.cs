﻿using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace RCT.Api.Common.Utils;

public static class JwtTokenHelper
{
    public static string CreateToken(string secret, Claim[] claims, DateTime expiryDate)
    {
        SymmetricSecurityKey securityKey = new(Encoding.UTF8.GetBytes(secret));

        JwtSecurityToken token = new(
            issuer: null,
            audience: null,
            claims: claims,
            expires: expiryDate,
            signingCredentials: new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256));

        JwtSecurityTokenHandler tokenHandler = new();

        return tokenHandler.WriteToken(token);
    }
}