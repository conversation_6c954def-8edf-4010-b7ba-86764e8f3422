{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(cat:*)", "Bash(dotnet ef migrations:*)", "Bash(grep:*)", "Bash(node:*)", "Bash(dotnet run:*)", "Bash(rm:*)", "Bash(dotnet build)", "Bash(npm run check:*)", "Bash(npm install)", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(git checkout:*)", "Bash(cp:*)", "Bash(npm run dev:*)", "Bash(git commit:*)", "Bash(dotnet build:*)", "Bash(dotnet nuget list:*)", "Bash(dotnet nuget remove source:*)", "Bash(dotnet restore:*)", "Bash(sudo lsof:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(git pull:*)", "Bash(git push:*)", "Bash(npx tsc:*)", "Bash(npm install:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "<PERSON><PERSON>(cwebp:*)", "<PERSON><PERSON>(mv:*)", "Bash(ffprobe:*)", "Bash(ffmpeg:*)", "<PERSON><PERSON>(convert:*)", "<PERSON><PERSON>(magick:*)"], "deny": []}}