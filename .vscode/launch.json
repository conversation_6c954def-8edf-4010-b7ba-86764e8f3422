{"version": "0.2.0", "configurations": [{"name": "Backend (Hot Reload)", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["watch", "run"], "cwd": "${workspaceFolder}/rct-backend/RCT.Api", "console": "integratedTerminal", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, {"name": "Frontend (Dev Server)", "type": "node", "request": "launch", "program": "${workspaceFolder}/rct-frontend/node_modules/vite/bin/vite.js", "args": ["dev"], "cwd": "${workspaceFolder}/rct-frontend", "console": "integratedTerminal", "serverReadyAction": {"action": "openExternally", "pattern": "Local:\\s+(https?://\\S+)"}}], "compounds": [{"name": "Full Stack", "configurations": ["Backend (Hot Reload)", "Frontend (Dev Server)"]}]}