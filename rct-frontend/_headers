# Cache fonts for 1 year
/fonts/*
  Cache-Control: public, max-age=31536000, immutable

# Cache screenshots and media for 1 year (immutable content)
/screenshots/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images for 1 year (versioned assets)
/*.png
  Cache-Control: public, max-age=31536000, immutable

/*.webp
  Cache-Control: public, max-age=31536000, immutable

/*.svg
  Cache-Control: public, max-age=31536000, immutable

# Cache favicons for 1 year
/favicon.png
  Cache-Control: public, max-age=31536000, immutable

/Favicon-*.svg
  Cache-Control: public, max-age=31536000, immutable

# Cache videos for 1 year (large immutable files)
/*.mp4
  Cache-Control: public, max-age=31536000, immutable

/*.webm
  Cache-Control: public, max-age=31536000, immutable

# Cache JavaScript and CSS files for 1 year
/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Cache-Control: public, max-age=31536000, immutable

# Cache SvelteKit app files for 1 year
/_app/*
  Cache-Control: public, max-age=31536000, immutable

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()