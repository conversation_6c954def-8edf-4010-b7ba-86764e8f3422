# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The Rent Collection Toolkit is a modern SvelteKit application for UK council and housing association rent collection agents. It implements a dual-instance system supporting both inbound and outbound call management with comprehensive rent calculations.

## Development Commands

```bash
# Development
npm run dev                 # Start development server on localhost:5173
npm run build              # Build for production
npm run preview            # Preview production build
npm run check              # Run TypeScript checks
npm run check:watch        # Watch mode for TypeScript checks
```

## Architecture

### State Management
- **Svelte Stores**: Centralised state management in `src/lib/stores/index.ts`
- **Dual Instance System**: Separate data stores for inbound/outbound call types
- **Data Synchronisation**: Automatic syncing between tabs (Rent Calculator → Arrangement Planner → Account Charges)

### Key Components Structure
- **RentCalculator.svelte**: Primary data entry with 13 calculation formulas, includes compact recommended actions section
- **ArrangementPlanner.svelte**: Payment planning with debt clearance projections, synchronized data section
- **AccountCharges.svelte**: UC/HB entitlement calculations with service charge breakdowns
- **Header.svelte**: Navigation with instance switching and session management
- **NotesSection.svelte**: Fixed-position notes area with intelligent response suggestions
- **StandardResponses.svelte**: Intelligent notes system with relevance scoring and template suggestions
- **TabButtons.svelte**: Tab navigation for compact view (3 tabs: Calculator, Planner, Charges)
- **SessionLog/CaseLog.svelte**: Note management and case history
- **DevMode.svelte**: Real-time design system editor for adjusting CSS variables and color scheme

### Data Flow Pattern
1. Data entered in Rent Calculator updates the active instance store
2. Helper functions (`updateRentCalculatorData`, `updateArrangementPlannerData`, etc.) manage state updates
3. Derived stores (`currentInstanceData`) provide reactive data to components
4. All calculations are reactive and update automatically when input data changes

### Instance Management
- Two separate data stores: `inboundData` and `outboundData`
- Active instance determined by `activeInstance` store ('inbound' | 'outbound')
- Colour-coded UI: Red for inbound (#DB2955), Teal for outbound (#4DA1A9)
- Independent data maintained for each instance type

### View Modes
- **Compact View**: Tab-based interface with 3 tabs (Calculator, Planner, Charges) + fixed NotesSection at bottom
- **Full View**: Grid layout showing all components simultaneously with NotesSection at bottom
- NotesSection is always visible in both view modes but positioned differently

### Mock Data System
Currently uses static/mock data with clear API integration points:
- Authentication: `login()` function in stores
- Data persistence: `saveInstanceData()` and `loadInstanceData()` functions
- All API calls are clearly marked with TODO comments for backend integration

### Calculation System
Implements 28 specific calculations across three modules:
- Rent Calculator (1-13): Rent conversions, payments, arrears
- Arrangement Planner (14-17): Debt clearance, weekly projections
- Account Charges (18-28): UC/HB entitlements, service charges

### Intelligent Notes System
- **Relevance Scoring**: Dynamic scoring algorithm with singular/plural word matching
- **Template Categories**: "Current Suggestions" and "Relevant Suggestions" (30%+ relevance)
- **Word Normalization**: Handles variations like repair/repairs, box/boxes, company/companies
- **Quick Responses**: Horizontal sliding response bar when notes textarea is focused
- **Action Integration**: Suggested actions appear as prioritized response templates

### Styling Approach
- **CSS Variables**: Centralised theming in `src/app.css` and `src/routes/+layout.svelte`
- **LESS Preprocessing**: Component-scoped styles with global utility classes
- **Custom CSS Architecture**: No external CSS frameworks (Tailwind CSS is not used)
- **Responsive Design**: Mobile-first approach with 768px breakpoint
- **Colour System**: Status-based colours (green for positive, red for negative values)
- **Reusable Components**: FormGroup and OutputItem components for consistent UI

## Key Development Patterns

### Store Updates
Always use the provided helper functions rather than direct store updates:
```typescript
updateRentCalculatorData({ weeklyRent: 100 });
updateNotes('New note content');
addSessionNote('Note content', 'tenant-ref');
```

### Component Communication
- Use stores for cross-component data sharing
- Custom events for parent-child communication (e.g., Header component events)
- Derived stores for computed values that need to be reactive
- NotesSection uses fixed positioning and is always rendered in both view modes

### Data Validation
- All calculations include null/undefined checks
- Numeric inputs are validated and converted appropriately
- Error states managed through `errorMessage` store

## Backend Integration Points

The application is designed for easy backend integration with these key areas:
- Replace mock authentication in `login()` function
- Implement API calls in data persistence functions
- Add environment variables for API endpoints
- Connect tenant management to database
- Implement persistent case log storage

## Recent UI Enhancements

### Recommended Actions System
- **Location**: Moved from NotesSection to bottom of RentCalculator component
- **Layout**: Compact 2-per-row grid design with priority-based styling
- **Integration**: Automatic generation based on tenant financial situation
- **Visual**: Color-coded priority system (urgent=red, high=orange, medium=yellow, low=green)

### Notes Interface Improvements
- **Compact View**: NotesSection always visible at bottom, notes tab removed from TabButtons
- **Tab Count**: Reduced from 4 tabs to 3 tabs (Calculator, Planner, Charges)
- **Accessibility**: Notes always accessible without tab switching
- **Positioning**: Fixed positioning ensures notes stay at bottom of viewport

### Intelligent Response System
- **Documentation**: See `/docs/Intelligent-Notes-System.md` for complete guide
- **Word Matching**: Advanced singular/plural detection with stemming
- **Relevance Threshold**: 30%+ relevance moves templates to "Relevant Suggestions"
- **Performance**: Debounced processing for smooth real-time scoring

## Developer Tools

### DevMode Component
The application includes a built-in development tool (`DevMode.svelte`) for real-time design system adjustments:

- **Design System Editor**: Drag-and-drop window for adjusting CSS custom properties
- **Real-time Updates**: Instant visual feedback when modifying values
- **Categorized Controls**: Separate tabs for 'Sizes' and 'Colours'
- **Browser Persistence**: Save/load custom values using localStorage
- **CSS Export**: Copy generated CSS custom properties to clipboard
- **Dual-Instance Support**: Colour variables for both inbound/outbound instances

#### Key Features:
- **Size Controls**: Label weight, font sizes, padding, spacing, and component dimensions
- **Colour Controls**: Primary/secondary colors, status colors (red/orange/yellow/green), instance-specific colours
- **Position Management**: Draggable window with viewport boundary constraints
- **Reset Functionality**: One-click restore to default design system values

## Testing Strategy

When adding tests:
- Focus on calculation accuracy in the store functions
- Test component rendering with different data states
- Verify data synchronisation between instances
- Test responsive design at key breakpoints
- Validate accessibility features
- Test intelligent notes relevance scoring algorithm
- Verify recommended actions positioning and styling
- Test DevMode component functionality and CSS variable updates