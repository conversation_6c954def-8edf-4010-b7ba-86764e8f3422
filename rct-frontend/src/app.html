<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" id="favicon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>Rent Collection Toolkit</title>
		
		<!-- Preload critical fonts to prevent layout shift -->
		<link rel="preload" href="/fonts/MartianGrotesk-NrRg.woff2" as="font" type="font/woff2" crossorigin fetchpriority="high">
		<link rel="preload" href="/fonts/MartianGrotesk-NrMd.woff2" as="font" type="font/woff2" crossorigin fetchpriority="high">
		<link rel="preload" href="/fonts/MartianGrotesk-NrBd.woff2" as="font" type="font/woff2" crossorigin fetchpriority="high">
		
		<!-- Preload critical LCP elements -->
		<link rel="preload" href="/rent-collection-toolkit-phone.webp" as="image" type="image/webp" crossorigin fetchpriority="high">
		<!-- Preload videos with lower priority since they're not LCP on mobile -->
		<link rel="preload" href="/rent-collection-toolkit-video.webm" as="video" type="video/webm" crossorigin fetchpriority="low">
		<link rel="preload" href="/rent-collection-toolkit-video.mp4" as="video" type="video/mp4" crossorigin fetchpriority="low">
		
		<!-- Critical CSS for LCP optimization -->
		<style>
			/* CSS Custom Properties (critical variables) */
			:root {
				--color-primary: #025582;
				--color-secondary: #0369a1;
			}

			/* Critical base styles */
			* {
				margin: 0;
				padding: 0;
				box-sizing: border-box;
			}

			html {
				font-family: 'Martian Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
				font-size: 1rem;
				line-height: 1.5;
				color: #111827;
				background-color: #f9fafb;
			}

			body {
				min-height: 100vh;
				overflow-x: hidden;
			}

			/* Critical typography */
			h1, h2, h3, h4, h5, h6 {
				font-weight: 600;
				line-height: 1.25;
				margin-bottom: 1rem;
			}

			h1 { font-size: 1.875rem; }
			h2 { font-size: 1.5rem; }
			h3 { font-size: 1.25rem; }

			p {
				margin-bottom: 1rem;
			}

			/* Critical layout styles for LCP content */
			.website-main {
				display: block;
			}

			.features {
				padding: 6rem 0;
				background: #f9fafb;
				/* Prevent layout shift */
				min-height: 800px;
			}
			
			/* Prevent feature grid layout shifts */
			.feature-column-pairs {
				/* Reserve space to prevent layout shift */
				min-height: 600px;
				contain: layout;
			}
			
			/* Prevent image layout shifts */
			picture {
				display: block;
				width: 100%;
				height: auto;
				/* Reserve aspect ratio space */
				aspect-ratio: 4/3;
			}
			
			picture img {
				width: 100%;
				height: 100%;
				object-fit: contain;
				display: block;
			}
			
			.container {
				max-width: 1200px;
				margin: 0 auto;
				padding: 0 2rem;
			}
			
			.section-header {
				text-align: center;
				/* margin-bottom: 4rem; */
			}
			
			.section-header h2 {
				font-size: 2.5rem;
				font-weight: 700;
				color: #025582;
				margin-bottom: 1rem;
			}
			
			.section-header p {
				font-size: 1.1rem !important;
				color: #4b5563 !important;
				max-width: 800px !important;
				margin: 0 auto !important;
				line-height: 1.6 !important;
				/* Aggressive LCP optimization - force immediate render */
				will-change: auto !important;
				contain: layout style !important;
				visibility: visible !important;
				opacity: 1 !important;
				display: block !important;
				transform: none !important;
				/* Skip browser layout queuing */
				content-visibility: visible !important;
				contain-intrinsic-size: 800px 200px !important;
				/* Force rendering priority */
				position: relative !important;
				z-index: 1 !important;
				/* Disable any animations */
				animation: none !important;
				transition: none !important;
			}

			/* Hero section critical styles */
			.hero {
				position: relative;
				color: white;
				padding: 4rem 0;
				min-height: 80vh;
				display: flex;
				align-items: center;
				overflow: hidden;
				background: linear-gradient(135deg, #025582 0%, #0369a1 100%);
				/* Optimize hero rendering */
				contain: layout style;
				content-visibility: visible;
			}

			.hero-container {
				max-width: 1200px;
				margin: 0 auto;
				padding: 0 2rem;
				display: grid;
				grid-template-columns: 3fr .5fr;
				gap: 4rem;
				align-items: center;
				position: relative;
				z-index: 3;
			}

			/* Critical feature grid styles to prevent unused CSS warnings */
			.feature-grid {
				display: grid;
				gap: 2rem;
				width: 100%;
			}
			
			.feature-card {
				border-radius: 1rem;
				overflow: hidden;
				transition: all 0.3s ease;
				height: 100%;
				display: flex;
				flex-direction: column;
				background: white;
				box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
				border: 1px solid #e5e7eb;
			}
			
			/* Critical footer styles */
			.website-footer {
				background: #111827;
				color: white;
				padding: 3rem 0 2rem;
			}

			/* Mobile padding adjustments */
			@media (max-width: 768px) {
				.container {
					padding: 0 1rem;
				}
				
				.hero-container {
					padding: 0 1rem;
					grid-template-columns: 1fr;
					gap: 2rem;
				}
			}
		</style>

		<!-- LCP optimization script -->
		<script>
			// Immediately set critical CSS variables if not set
			if (!document.documentElement.style.getPropertyValue('--color-primary')) {
				document.documentElement.style.setProperty('--color-primary', '#025582');
				document.documentElement.style.setProperty('--color-secondary', '#0369a1');
			}
			
			// Force LCP element to be immediately visible when DOM loads
			document.addEventListener('DOMContentLoaded', function() {
				// Find and immediately show LCP content
				const sectionHeaders = document.querySelectorAll('.section-header p');
				sectionHeaders.forEach(function(p) {
					if (p.textContent && p.textContent.includes('The toolkit is designed')) {
						p.style.visibility = 'visible';
						p.style.opacity = '1';
						p.style.transform = 'none';
						p.style.display = 'block';
					}
				});
			});
		</script>

		<!-- External Font Awesome removed - using inline CSS for better performance -->
		<script>
			// Dynamic favicon based on color scheme preference
			function setFavicon() {
				const favicon = document.getElementById('favicon');
				const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
				
				// Use light favicon for dark mode, dark favicon for light mode
				if (isDarkMode) {
					favicon.href = '/Favicon-Light.svg';
				} else {
					favicon.href = '/Favicon-Dark.svg';
				}
			}
			
			// Set initial favicon
			setFavicon();
			
			// Listen for changes in color scheme preference
			window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', setFavicon);
		</script>
		
		<!-- Inline critical Font Awesome icons to reduce unused CSS -->
		<style>
			/* Font Awesome base styles */
			.fas, .fa {
				font-family: "Font Awesome 6 Free";
				font-weight: 900;
				font-style: normal;
				font-variant: normal;
				text-rendering: auto;
				line-height: 1;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
			}
			
			/* Icons used on homepage */
			.fa-clock:before { content: "\f017"; }
			.fa-phone:before { content: "\f095"; }
			.fa-shield-alt:before { content: "\f3ed"; }
			.fa-calculator:before { content: "\f1ec"; }
			.fa-calendar-alt:before { content: "\f073"; }
			.fa-money-bill-wave:before { content: "\f53a"; }
			.fa-clipboard-list:before { content: "\f46d"; }
			.fa-chart-line:before { content: "\f201"; }
			.fa-chart-bar:before { content: "\f080"; }
			.fa-users:before { content: "\f0c0"; }
			.fa-mobile-alt:before { content: "\f3cd"; }
			.fa-graduation-cap:before { content: "\f19d"; }
			.fa-building:before { content: "\f1ad"; }
			.fa-sync-alt:before { content: "\f2f1"; }
			.fa-handshake:before { content: "\f2b5"; }
			.fa-check:before { content: "\f00c"; }
			.fa-info-circle:before { content: "\f05a"; }
			
			/* Icons used in app navigation */
			.fa-times:before { content: "\f00d"; }
			.fa-chevron-left:before { content: "\f053"; }
			.fa-chevron-right:before { content: "\f054"; }
			.fa-plus:before { content: "\f067"; }
			.fa-folder-open:before { content: "\f07c"; }
			.fa-cog:before { content: "\f013"; }
			.fa-tags:before { content: "\f02c"; }
			.fa-expand:before { content: "\f065"; }
			.fa-compress-alt:before { content: "\f422"; }
			.fa-arrow-down:before { content: "\f063"; }
			.fa-sidebar:before { content: "\f0c9"; }
			.fa-code:before { content: "\f121"; }
			.fa-database:before { content: "\f1c0"; }
			.fa-user-cog:before { content: "\f4fe"; }
			.fa-exclamation-triangle:before { content: "\f071"; }
			.fa-file-pdf:before { content: "\f1c1"; }
			.fa-copy:before { content: "\f0c5"; }
			.fa-eye:before { content: "\f06e"; }
			.fa-calendar-plus:before { content: "\f271"; }
		</style>
		
		<!-- Load Font Awesome font file asynchronously -->
		<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
		<style>
			@font-face {
				font-family: "Font Awesome 6 Free";
				font-style: normal;
				font-weight: 900;
				src: url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2") format("woff2");
				font-display: swap;
			}
		</style>
		
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="tap" data-sveltekit-preload-code="viewport">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
