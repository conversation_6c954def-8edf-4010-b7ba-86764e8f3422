<script lang="ts">
  import WebsiteHeader from '$lib/components/WebsiteHeader.svelte';
  import WebsiteFooter from '$lib/components/WebsiteFooter.svelte';
</script>

<svelte:head>
  <title>Accessibility Statement - Rent Collection Toolkit</title>
  <meta name="description" content="Accessibility statement for Rent Collection Toolkit. Learn about our commitment to digital accessibility and how we ensure our software is usable by everyone.">
</svelte:head>

<WebsiteHeader />

<main class="legal-page">
  <div class="container">
    <h1>Accessibility Statement</h1>
    <p class="last-updated">Last updated: {new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}</p>
    
    <section>
      <h2>1. Our Commitment to Accessibility</h2>
      <p>Rent Collection Toolkit is committed to ensuring digital accessibility for people with disabilities. We are continually improving the user experience for everyone and applying the relevant accessibility standards.</p>
      <p>We believe that everyone should be able to access and use our housing management software effectively, regardless of their abilities or disabilities.</p>
    </section>

    <section>
      <h2>2. Accessibility Standards</h2>
      <p>We aim to conform to the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA standards. These guidelines explain how to make web content more accessible to people with disabilities, including:</p>
      <ul>
        <li>Visual impairments and blindness</li>
        <li>Hearing impairments and deafness</li>
        <li>Motor disabilities</li>
        <li>Cognitive disabilities</li>
      </ul>
    </section>

    <section>
      <h2>3. Accessibility Features</h2>
      <p>Our service includes the following accessibility features:</p>
      
      <h3>3.1 Keyboard Navigation</h3>
      <ul>
        <li>Full keyboard navigation support</li>
        <li>Logical tab order through all interactive elements</li>
        <li>Skip links to main content areas</li>
        <li>Keyboard shortcuts for common actions</li>
      </ul>

      <h3>3.2 Visual Design</h3>
      <ul>
        <li>High contrast color schemes</li>
        <li>Scalable text up to 200% without loss of functionality</li>
        <li>Clear visual hierarchy with proper heading structure</li>
        <li>Consistent navigation and layout patterns</li>
        <li>Focus indicators for interactive elements</li>
      </ul>

      <h3>3.3 Screen Reader Support</h3>
      <ul>
        <li>Semantic HTML structure</li>
        <li>Alt text for all meaningful images</li>
        <li>Descriptive labels for form controls</li>
        <li>ARIA labels and descriptions where needed</li>
        <li>Status announcements for dynamic content</li>
      </ul>

      <h3>3.4 Forms and Data Entry</h3>
      <ul>
        <li>Clear error messages and validation feedback</li>
        <li>Required field indicators</li>
        <li>Help text and instructions for complex inputs</li>
        <li>Logical grouping of related form fields</li>
      </ul>
    </section>

    <section>
      <h2>4. Assistive Technology Compatibility</h2>
      <p>Our service has been tested with the following assistive technologies:</p>
      <ul>
        <li>Screen readers (NVDA, JAWS, VoiceOver)</li>
        <li>Voice control software (Dragon NaturallySpeaking)</li>
        <li>Magnification software (ZoomText)</li>
        <li>Switch navigation devices</li>
      </ul>
    </section>

    <section>
      <h2>5. Browser and Platform Support</h2>
      <p>Our accessible features work with:</p>
      
      <h3>5.1 Web Browsers</h3>
      <ul>
        <li>Google Chrome (latest version)</li>
        <li>Mozilla Firefox (latest version)</li>
        <li>Safari (latest version)</li>
        <li>Microsoft Edge (latest version)</li>
      </ul>

      <h3>5.2 Operating Systems</h3>
      <ul>
        <li>Windows 10/11 with built-in accessibility features</li>
        <li>macOS with VoiceOver and other accessibility tools</li>
        <li>iOS/iPadOS with accessibility features enabled</li>
        <li>Android with TalkBack and accessibility services</li>
      </ul>
    </section>

    <section>
      <h2>6. Known Accessibility Issues</h2>
      <p>We are actively working to address the following known issues:</p>
      <ul>
        <li>Some complex data tables may require additional navigation assistance</li>
        <li>Chart and graph data may need text alternatives</li>
        <li>PDF export features are being enhanced for better screen reader support</li>
      </ul>
      <p>We are committed to resolving these issues in future updates.</p>
    </section>

    <section>
      <h2>7. Accessibility Testing</h2>
      <p>We conduct regular accessibility testing through:</p>
      <ul>
        <li>Automated testing tools integrated into our development process</li>
        <li>Manual testing with assistive technologies</li>
        <li>User testing with people who have disabilities</li>
        <li>Third-party accessibility audits</li>
        <li>Ongoing monitoring and improvement</li>
      </ul>
    </section>

    <section>
      <h2>8. Training and Awareness</h2>
      <p>Our development team receives regular training on:</p>
      <ul>
        <li>Accessibility best practices and standards</li>
        <li>Assistive technology usage</li>
        <li>Inclusive design principles</li>
        <li>WCAG 2.1 guidelines and compliance</li>
      </ul>
    </section>

    <section>
      <h2>9. Getting Help</h2>
      <p>If you need assistance using our service or have suggestions for improvement:</p>
      
      <h3>9.1 Documentation and Support</h3>
      <ul>
        <li>Comprehensive user guides with accessibility considerations</li>
        <li>Video tutorials with captions and transcripts</li>
        <li>Email support with accessibility specialists</li>
        <li>Phone support during business hours</li>
      </ul>

      <h3>9.2 Alternative Formats</h3>
      <p>We can provide information in alternative formats upon request:</p>
      <ul>
        <li>Large print documentation</li>
        <li>Audio descriptions of visual content</li>
        <li>Plain language summaries</li>
        <li>Structured data exports</li>
      </ul>
    </section>

    <section>
      <h2>10. Feedback and Reporting Issues</h2>
      <p>Your feedback helps us improve accessibility. Please contact us if you:</p>
      <ul>
        <li>Encounter accessibility barriers</li>
        <li>Have suggestions for improvement</li>
        <li>Need assistance with any feature</li>
        <li>Require content in an alternative format</li>
      </ul>
      
      <p>When reporting accessibility issues, please include:</p>
      <ul>
        <li>Description of the issue</li>
        <li>Your operating system and browser</li>
        <li>Assistive technology being used (if applicable)</li>
        <li>Steps to reproduce the issue</li>
      </ul>
    </section>

    <section>
      <h2>11. Continuous Improvement</h2>
      <p>We are committed to continuous improvement of accessibility:</p>
      <ul>
        <li>Regular accessibility reviews and updates</li>
        <li>User feedback integration into development cycles</li>
        <li>Staying current with accessibility standards and best practices</li>
        <li>Proactive identification and resolution of barriers</li>
      </ul>
    </section>

    <section>
      <h2>12. Legal Compliance</h2>
      <p>We strive to comply with relevant accessibility legislation:</p>
      <ul>
        <li>Equality Act 2010 (UK)</li>
        <li>Public Sector Bodies (Websites and Mobile Applications) Accessibility Regulations 2018</li>
        <li>European Accessibility Act</li>
        <li>Section 508 (US Federal)</li>
      </ul>
    </section>

    <!-- <section>
      <h2>13. Contact Us</h2>
      <div class="contact-info">
        <p><strong>Accessibility Team:</strong> <EMAIL></p>
        <p><strong>General Support:</strong> <EMAIL></p>
        <p><strong>Phone:</strong> 0800 123 4567</p>
        <p><strong>Response Time:</strong> We aim to respond to accessibility inquiries within 2 business days</p>
      </div>
      
      <p>We welcome your feedback and are committed to providing an accessible experience for all users of Rent Collection Toolkit.</p>
    </section> -->
  </div>
</main>

<WebsiteFooter />

<style>
  .legal-page {
    padding: 4rem 0;
    min-height: calc(100vh - 200px);
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h1 {
    font-size: 2.5rem;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }

  .last-updated {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e5e7eb;
  }

  section {
    margin-bottom: 3rem;
  }

  h2 {
    font-size: 1.75rem;
    color: var(--color-primary);
    margin-bottom: 1rem;
    margin-top: 3rem;
  }

  h3 {
    font-size: 1.25rem;
    color: var(--color-primary);
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
  }

  p {
    line-height: 1.7;
    color: #374151;
    margin-bottom: 1rem;
  }

  ul {
    margin-left: 2rem;
    margin-bottom: 1rem;
  }

  li {
    line-height: 1.7;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  strong {
    color: var(--color-primary);
  }

  .contact-info {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
  }

  .contact-info p {
    margin-bottom: 0.5rem;
  }

  @media (max-width: 768px) {
    h1 {
      font-size: 2rem;
    }

    h2 {
      font-size: 1.5rem;
    }

    .container {
      padding: 0 1rem;
    }
  }
</style>