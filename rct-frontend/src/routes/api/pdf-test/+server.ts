import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
  try {
    // Test the PDF service with a simple HTML payload
    const testHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Test PDF</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        h1 { color: #333; }
    </style>
</head>
<body>
    <h1>PDF Service Test</h1>
    <p>This is a test document to verify PDF generation is working.</p>
    <p>Generated at: ${new Date().toISOString()}</p>
</body>
</html>`;

    const endpoints = [
      {
        name: 'HTTPS endpoint',
        url: 'https://139.59.182.115:3000/convert',
        headers: {
          'Content-Type': 'text/html',
          'x-api-key': '5344c64603e02d63ea3f19b1839aeb4b7d1925fc289cad697d3a2e5060ffef3a'
        }
      },
      {
        name: 'HTTP endpoint',
        url: 'http://139.59.182.115:3000/convert',
        headers: {
          'Content-Type': 'text/html',
          'x-api-key': '5344c64603e02d63ea3f19b1839aeb4b7d1925fc289cad697d3a2e5060ffef3a'
        }
      },
      {
        name: 'HTTP endpoint with JSON',
        url: 'http://139.59.182.115:3000/convert',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': '5344c64603e02d63ea3f19b1839aeb4b7d1925fc289cad697d3a2e5060ffef3a'
        }
      }
    ];

    const results = [];

    for (const config of endpoints) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const body = config.headers['Content-Type'] === 'application/json' 
          ? JSON.stringify({ html: testHtml })
          : testHtml;

        const response = await fetch(config.url, {
          method: 'POST',
          headers: config.headers,
          body,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        const result = {
          endpoint: config.name,
          url: config.url,
          status: response.status,
          statusText: response.statusText,
          contentType: response.headers.get('content-type'),
          success: response.ok,
          error: null as string | null
        };

        if (!response.ok) {
          const errorText = await response.text();
          result.error = errorText;
        }

        results.push(result);
      } catch (error) {
        results.push({
          endpoint: config.name,
          url: config.url,
          status: 0,
          statusText: 'Request Failed',
          contentType: null,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return json({
      message: 'PDF service diagnostic test completed',
      timestamp: new Date().toISOString(),
      results
    });

  } catch (error) {
    return json({
      error: 'Diagnostic test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
};