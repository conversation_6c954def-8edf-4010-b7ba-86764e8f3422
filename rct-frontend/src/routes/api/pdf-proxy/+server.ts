import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { env } from '$env/dynamic/private';

export const GET: RequestHandler = async () => {
  return json({
    message: 'PDF Proxy diagnostic endpoint',
    timestamp: new Date().toISOString(),
    environment: 'SvelteKit'
  });
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    console.log('PDF proxy called');
    
    // Get HTML content from request body
    const htmlContent = await request.text();
    console.log('HTML content length:', htmlContent.length);
    console.log('HTML content preview:', htmlContent.substring(0, 200));

    // Validate HTML content
    if (!htmlContent || htmlContent.length < 100) {
      throw new Error(`Invalid HTML content: length ${htmlContent.length}`);
    }

    // Get configuration from environment variables
    const pdfApiKey = env.PDF_API_KEY || env.VITE_PDF_API_KEY || '5344c64603e02d63ea3f19b1839aeb4b7d1925fc289cad697d3a2e5060ffef3a';
    const pdfServiceUrl = env.PDF_SERVICE_URL || env.VITE_PDF_SERVICE_URL || 'http://**************:3000/convert';
    
    console.log(`Using PDF service URL: ${pdfServiceUrl}`);
    console.log(`API Key configured: ${pdfApiKey ? 'Yes' : 'No'}`);

    // Try multiple PDF service endpoints and configurations
    const endpoints = [
      {
        url: pdfServiceUrl.replace('http://', 'https://'), // Try HTTPS first
        apiKey: pdfApiKey,
        headers: {
          'Content-Type': 'text/html',
          'x-api-key': pdfApiKey
        }
      },
      {
        url: pdfServiceUrl, // Original URL
        apiKey: pdfApiKey,
        headers: {
          'Content-Type': 'text/html',
          'x-api-key': pdfApiKey
        }
      },
      {
        url: pdfServiceUrl, // Try with JSON content-type
        apiKey: pdfApiKey,
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': pdfApiKey
        }
      }
    ];
    
    let lastError: Error | null = null;
    
    for (const config of endpoints) {
      try {
        console.log(`Trying PDF endpoint: ${config.url}`);
        
        // Create timeout for PDF service request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
        
        // Prepare body based on content type
        const body = config.headers['Content-Type'] === 'application/json' 
          ? JSON.stringify({ html: htmlContent })
          : htmlContent;
        
        const response = await fetch(config.url, {
          method: 'POST',
          headers: config.headers,
          body,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        console.log(`PDF service response status: ${response.status}`);
        console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const errorText = await response.text();
          console.log(`PDF service error response:`, errorText);
          lastError = new Error(`PDF service returned ${response.status}: ${response.statusText} - ${errorText}`);
          continue; // Try next endpoint
        }

        // Verify we got a PDF response
        const contentType = response.headers.get('content-type');
        console.log(`Response content-type: ${contentType}`);
        
        if (contentType && !contentType.includes('application/pdf')) {
          console.log(`Expected PDF but got ${contentType}`);
          lastError = new Error(`PDF service returned ${contentType} instead of application/pdf`);
          continue; // Try next endpoint
        }

        console.log(`PDF generation successful with ${config.url}`);
        
        // Forward the PDF response
        const pdfBuffer = await response.arrayBuffer();
        console.log(`PDF buffer size: ${pdfBuffer.byteLength}`);
        
        // Verify reasonable PDF size
        if (pdfBuffer.byteLength < 1000) {
          lastError = new Error(`PDF response too small (${pdfBuffer.byteLength} bytes) - likely not a valid PDF`);
          continue; // Try next endpoint
        }
        
        return new Response(pdfBuffer, {
          status: 200,
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename="rent-collection-report.pdf"',
            'Access-Control-Allow-Origin': '*'
          }
        });
        
      } catch (endpointError) {
        console.log(`Endpoint ${config.url} failed:`, endpointError);
        lastError = endpointError as Error;
        continue; // Try next endpoint
      }
    }
    
    // If we get here, all endpoints failed
    throw lastError || new Error('All PDF endpoints failed');

  } catch (error) {
    console.error('PDF proxy error:', error);
    
    return json({
      error: 'PDF generation failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      details: 'Check server logs for more information'
    }, { status: 500 });
  }
};