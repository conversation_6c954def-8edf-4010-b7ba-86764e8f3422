<script>
  import WebsitePage from '$lib/components/WebsitePage.svelte';
  import ContentSection from '$lib/components/ContentSection.svelte';
  import FeatureGrid from '$lib/components/FeatureGrid.svelte';

  const features = [
    {
      title: '13 Built-in Formulas',
      description: 'Comprehensive rent calculation formulas covering weekly to monthly conversions, payment scenarios, and arrears calculations.',
      icon: 'fa-calculator',
      screenshot: '/screenshots/instant-rent-calculations.png',
      benefits: [
        'Weekly to monthly rent conversions',
        'Arrears calculation with projection dates',
        'Payment shortfall and surplus analysis',
        'Tenant contribution breakdowns'
      ]
    },
    {
      title: 'Real-time Calculations',
      description: 'Instant calculations as you type, with automatic updates across all related fields and components.',
      icon: 'fa-bolt',
      screenshot: '/screenshots/real-time-data-sync.png',
      benefits: [
        'Live calculation updates',
        'Cross-component data sync',
        'Error-free mathematical operations',
        'No manual recalculation needed'
      ]
    },
    {
      title: 'Multiple Payment Periods',
      description: 'Support for weekly, monthly, and custom payment periods with accurate conversion calculations.',
      icon: 'fa-calendar-check',
      benefits: [
        'Weekly payment support',
        'Monthly payment support',
        'Custom period calculations',
        'Accurate conversion rates'
      ]
    },
    {
      title: 'Tenant & Benefits Split',
      description: 'Calculate separate tenant contributions and benefits payments including UC, HB, APA, and TPD.',
      icon: 'fa-users',
      benefits: [
        'Tenant payment calculations',
        'Universal Credit integration',
        'Housing Benefit calculations',
        'Third-party deduction support'
      ]
    },
    {
      title: 'Arrears Analysis',
      description: 'Track current balances, project future arrears, and calculate clearance dates based on payment plans.',
      icon: 'fa-chart-line',
      benefits: [
        'Current balance tracking',
        'Arrears projection',
        'Clearance date calculations',
        'Payment impact analysis'
      ]
    },
    {
      title: 'Mathematical Expression Support',
      description: 'Enter complex calculations directly into fields with support for formulas and mathematical expressions.',
      icon: 'fa-function',
      benefits: [
        'Formula input support',
        'Mathematical expressions',
        'Complex calculation handling',
        'Safe evaluation engine'
      ]
    }
  ];

  const calculationTypes = [
    {
      title: 'Basic Rent Calculations',
      description: 'Weekly rent, monthly rent conversions, and rent period analysis.',
      icon: 'fa-home'
    },
    {
      title: 'Payment Analysis',
      description: 'Total payments, shortfalls, surpluses, and contribution breakdowns.',
      icon: 'fa-pound-sign'
    },
    {
      title: 'Arrears Management',
      description: 'Current balance tracking, future projections, and clearance calculations.',
      icon: 'fa-exclamation-triangle'
    },
    {
      title: 'Benefits Integration',
      description: 'UC, HB, APA, and TPD payment calculations and weekly/monthly conversions.',
      icon: 'fa-handshake'
    }
  ];
</script>

<WebsitePage 
  title="Rent Calculations" 
  subtitle="Comprehensive rent calculation tools with 13 built-in formulas for accurate, real-time financial analysis"
>
  <ContentSection title="Powerful Calculation Engine" layout="two-column" padding="large">
    <div>
      <h3>Professional Rent Management</h3>
      <p>
        Our rent calculation system provides everything you need for accurate financial analysis. 
        With 13 specialized formulas, you can handle any rent scenario from basic weekly conversions 
        to complex arrears projections.
      </p>
      <p>
        Built specifically for UK housing providers, the system handles all payment types including 
        tenant contributions, Universal Credit, Housing Benefit, Alternative Payment Arrangements, 
        and Third Party Deductions.
      </p>
      <ul class="feature-list">
        <li><i class="fas fa-check"></i> Real-time calculation updates</li>
        <li><i class="fas fa-check"></i> Error-free mathematical operations</li>
        <li><i class="fas fa-check"></i> Cross-component data synchronization</li>
        <li><i class="fas fa-check"></i> Mathematical expression support</li>
      </ul>
    </div>
    <div>
      <h3>Key Benefits</h3>
      <p>
        Eliminate manual calculation errors and reduce call handling time with our automated system. 
        Agents can focus on tenant support while the system handles complex mathematical operations.
      </p>
      <h4>Time Savings</h4>
      <p>
        Reduce calculation time from minutes to seconds with instant, automated results.
      </p>
      <h4>Accuracy Guarantee</h4>
      <p>
        Mathematical precision ensures reliable calculations for all financial scenarios.
      </p>
      <h4>Comprehensive Coverage</h4>
      <p>
        Handle any rent calculation scenario with our complete formula library.
      </p>
    </div>
  </ContentSection>

  <ContentSection title="Calculation Features" background="light" padding="large">
    <FeatureGrid {features} layout="row" cardStyle="detailed" showBenefits={true} />
  </ContentSection>

  <ContentSection title="Calculation Types" padding="large">
    <FeatureGrid features={calculationTypes} columns={2} cardStyle="minimal" />
  </ContentSection>

  <ContentSection title="How It Works" background="light" layout="single" padding="large">
    <div>
      <h3>Simple Three-Step Process</h3>
      <ol>
        <li>
          <strong>Enter Rent Information:</strong> Input weekly rent and current balance into the system
        </li>
        <li>
          <strong>Add Payment Details:</strong> Enter tenant contributions and benefits payments (UC, HB, APA, TPD)
        </li>
        <li>
          <strong>Instant Results:</strong> View calculated shortfalls, surpluses, and arrears projections in real-time
        </li>
      </ol>
      
      <h3>Advanced Features</h3>
      <p>
        The calculation engine supports mathematical expressions directly in input fields, allowing for 
        complex scenarios like "100 + 50 * 2" or percentage calculations. All results update instantly 
        as you type, with automatic synchronization across the Arrangement Planner and Account Charges components.
      </p>
      
      <h3>Integration Benefits</h3>
      <p>
        Calculations automatically flow to other system components, eliminating duplicate data entry and 
        ensuring consistency across all tenant management functions.
      </p>
    </div>
  </ContentSection>

  <ContentSection title="Ready to Streamline Your Calculations?" background="primary" padding="large">
    <div style="text-align: center;">
      <h3>Start Your Free Trial Today</h3>
      <p>
        Experience the power of automated rent calculations with our comprehensive toolkit 
        designed specifically for UK housing providers.
      </p>
      <a href="/app" class="cta-button">Try Rent Calculations</a>
    </div>
  </ContentSection>
</WebsitePage>

<style>
  .cta-button {
    display: inline-block;
    background: white;
    color: var(--color-primary);
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.2s ease;
    margin-top: 1rem;
  }

  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
</style>