<script>
  import WebsitePage from '$lib/components/WebsitePage.svelte';
  import ContentSection from '$lib/components/ContentSection.svelte';
  import FeatureGrid from '$lib/components/FeatureGrid.svelte';

  const features = [
    {
      title: 'Comprehensive Case Logging',
      description: 'Track all tenant interactions with timestamped notes, action records, and detailed case histories.',
      icon: 'fa-clipboard-list',
      screenshot: '/screenshots/comprehensive-case-logging.png',
      benefits: [
        'Timestamped interaction records',
        'Detailed case histories',
        'Action tracking',
        'Comprehensive documentation'
      ]
    },
    {
      title: 'Dual Instance System',
      description: 'Separate inbound and outbound call management with color-coded interfaces and independent data tracking.',
      icon: 'fa-phone-alt',
      benefits: [
        'Inbound call management',
        'Outbound call tracking',
        'Color-coded interfaces',
        'Independent data streams'
      ]
    },
    {
      title: 'Intelligent Notes System',
      description: 'Smart response suggestions and template management with relevance scoring and quick response options.',
      icon: 'fa-brain',
      benefits: [
        'Smart response suggestions',
        'Template management',
        'Relevance scoring',
        'Quick response options'
      ]
    },
    {
      title: 'Searchable History',
      description: 'Powerful search capabilities across all case notes and interactions with advanced filtering options.',
      icon: 'fa-search',
      benefits: [
        'Advanced search capabilities',
        'Historical data access',
        'Filter and sort options',
        'Quick information retrieval'
      ]
    },
    {
      title: 'Session Management',
      description: 'Organise work sessions with automatic case association and session-based note grouping.',
      icon: 'fa-tasks',
      benefits: [
        'Session-based organisation',
        'Automatic case association',
        'Work session tracking',
        'Grouped note management'
      ]
    },
    {
      title: 'Action Tracking',
      description: 'Track recommended actions, follow-up requirements, and outcome recording with priority management.',
      icon: 'fa-flag',
      benefits: [
        'Action item tracking',
        'Follow-up management',
        'Outcome recording',
        'Priority-based organisation'
      ]
    }
  ];

  const logTypes = [
    {
      title: 'Inbound Call Logs',
      description: 'Document tenant-initiated calls with reason codes, outcomes, and action requirements.',
      icon: 'fa-phone'
    },
    {
      title: 'Outbound Call Logs',
      description: 'Track proactive contact attempts with call outcomes and follow-up scheduling.',
      icon: 'fa-phone-volume'
    },
    {
      title: 'Visit Records',
      description: 'Document property visits, inspections, and face-to-face tenant meetings.',
      icon: 'fa-home'
    },
    {
      title: 'Email Correspondence',
      description: 'Log email exchanges and written communication with tenants and agencies.',
      icon: 'fa-envelope'
    }
  ];
</script>

<WebsitePage 
  title="Case Logging" 
  subtitle="Comprehensive case management with intelligent notes, dual instance system, and searchable interaction history"
>
  <ContentSection title="Professional Case Management" layout="two-column" padding="large">
    <div>
      <h3>Complete Interaction Tracking</h3>
      <p>
        Our case logging system provides comprehensive tracking of all tenant interactions with 
        timestamped notes, detailed case histories, and intelligent response suggestions. The 
        dual instance system allows simultaneous management of inbound and outbound calls.
      </p>
      <p>
        Built-in intelligence helps agents respond more effectively with suggested responses based 
        on case content and historical interactions. The searchable history ensures no information 
        is ever lost and can be quickly retrieved when needed.
      </p>
      <ul class="feature-list">
        <li><i class="fas fa-check"></i> Timestamped interaction records</li>
        <li><i class="fas fa-check"></i> Intelligent response suggestions</li>
        <li><i class="fas fa-check"></i> Dual instance management</li>
        <li><i class="fas fa-check"></i> Searchable case history</li>
      </ul>
    </div>
    <div>
      <h3>Intelligent Documentation</h3>
      <p>
        Move beyond basic note-taking with our intelligent documentation system that provides 
        contextual suggestions, template management, and relevance scoring to help agents 
        document cases more effectively and consistently.
      </p>
      <h4>Smart Suggestions</h4>
      <p>
        AI-powered response suggestions help agents provide consistent, professional responses.
      </p>
      <h4>Template Management</h4>
      <p>
        Standardized response templates ensure consistency across your team.
      </p>
      <h4>Quick Access</h4>
      <p>
        Instant access to historical information and case context for better decision making.
      </p>
    </div>
  </ContentSection>

  <ContentSection title="Case Logging Features" background="light" padding="large">
    <FeatureGrid {features} layout="row" cardStyle="detailed" showBenefits={true} />
  </ContentSection>

  <ContentSection title="Documentation Types" padding="large">
    <FeatureGrid features={logTypes} columns={2} cardStyle="minimal" />
  </ContentSection>

  <ContentSection title="How Case Logging Works" background="light" layout="single" padding="large">
    <div>
      <h3>Streamlined Documentation Process</h3>
      <ol>
        <li>
          <strong>Select Instance Type:</strong> Choose inbound or outbound call management with color-coded interface
        </li>
        <li>
          <strong>Document Interaction:</strong> Record case details with timestamped notes and interaction type
        </li>
        <li>
          <strong>Use Smart Suggestions:</strong> Access intelligent response suggestions and templates
        </li>
        <li>
          <strong>Track Actions:</strong> Record required actions and follow-up items with priority levels
        </li>
        <li>
          <strong>Search and Review:</strong> Use powerful search to find historical information and case context
        </li>
      </ol>
      
      <h3>Advanced Case Features</h3>
      <p>
        The intelligent notes system uses relevance scoring to suggest the most appropriate responses 
        based on case content. Template categories organize responses by context, making it easy 
        to find the right response for any situation.
      </p>
      
      <h3>Team Consistency</h3>
      <p>
        Standardized documentation practices and response templates ensure consistency across your 
        team while maintaining the flexibility to handle unique situations appropriately.
      </p>
    </div>
  </ContentSection>

  <ContentSection title="Ready to Improve Case Management?" background="primary" padding="large">
    <div style="text-align: center;">
      <h3>Start Your Free Trial Today</h3>
      <p>
        Transform your case management with intelligent documentation tools designed 
        specifically for UK housing providers.
      </p>
      <a href="/app" class="cta-button">Try Case Logging</a>
    </div>
  </ContentSection>
</WebsitePage>

<style>
  .cta-button {
    display: inline-block;
    background: white;
    color: var(--color-primary);
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.2s ease;
    margin-top: 1rem;
  }

  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
</style>