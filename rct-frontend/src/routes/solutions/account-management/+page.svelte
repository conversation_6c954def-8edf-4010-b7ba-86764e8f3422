<script>
  import WebsitePage from '$lib/components/WebsitePage.svelte';
  import ContentSection from '$lib/components/ContentSection.svelte';
  import FeatureGrid from '$lib/components/FeatureGrid.svelte';

  const features = [
    {
      title: 'UC/HB Entitlement Analysis',
      description: 'Calculate Universal Credit and Housing Benefit entitlements with detailed service charge breakdowns and eligibility assessments.',
      icon: 'fa-money-bill-wave',
      screenshot: '/screenshots/uc-hb-entitlement.png',
      benefits: [
        'Universal Credit calculations',
        'Housing Benefit entitlements',
        'Service charge breakdowns',
        'Eligibility assessments'
      ]
    },
    {
      title: 'Service Charge Management',
      description: 'Detailed breakdown of service charges with clear explanations for tenants and accurate benefit calculations.',
      icon: 'fa-list-alt',
      benefits: [
        'Detailed service breakdowns',
        'Clear tenant explanations',
        'Benefit-eligible charges',
        'Transparent pricing'
      ]
    },
    {
      title: 'Account Balance Tracking',
      description: 'Real-time account balance monitoring with automatic updates from payments and adjustments.',
      icon: 'fa-chart-area',
      benefits: [
        'Real-time balance updates',
        'Payment tracking',
        'Adjustment monitoring',
        'Historical balance records'
      ]
    },
    {
      title: 'Rent Account History',
      description: 'Comprehensive account history with payment records, balance changes, and charge applications.',
      icon: 'fa-history',
      screenshot: '/screenshots/analytics-and-insights.png',
      benefits: [
        'Complete payment history',
        'Balance change tracking',
        'Charge application records',
        'Audit trail maintenance'
      ]
    },
    {
      title: 'Benefits Coordination',
      description: 'Coordinate with benefit agencies and manage Alternative Payment Arrangements and Third Party Deductions.',
      icon: 'fa-handshake',
      benefits: [
        'APA management',
        'TPD coordination',
        'Benefit agency liaison',
        'Payment routing'
      ]
    },
    {
      title: 'Account Reconciliation',
      description: 'Automatic reconciliation of payments, charges, and benefits with discrepancy identification and resolution.',
      icon: 'fa-balance-scale-right',
      benefits: [
        'Automatic reconciliation',
        'Discrepancy identification',
        'Error resolution tools',
        'Account accuracy assurance'
      ]
    }
  ];

  const accountTypes = [
    {
      title: 'Rent Accounts',
      description: 'Standard rental accounts with rent charges, payments, and balance management.',
      icon: 'fa-home'
    },
    {
      title: 'Service Charge Accounts',
      description: 'Separate service charge accounts with detailed breakdowns and benefit eligibility.',
      icon: 'fa-tools'
    },
    {
      title: 'Benefits-Linked Accounts',
      description: 'Accounts with direct benefit payments and coordination with government agencies.',
      icon: 'fa-university'
    },
    {
      title: 'Temporary Accommodation',
      description: 'Specialized accounts for temporary accommodation with flexible charging structures.',
      icon: 'fa-bed'
    }
  ];
</script>

<WebsitePage 
  title="Account Management" 
  subtitle="Comprehensive account management with UC/HB entitlement analysis and detailed service charge breakdowns"
>
  <ContentSection title="Complete Account Control" layout="two-column" padding="large">
    <div>
      <h3>Comprehensive Account Management</h3>
      <p>
        Our account management system provides complete control over tenant accounts with real-time 
        balance tracking, comprehensive payment history, and detailed charge breakdowns. Handle 
        everything from basic rent accounts to complex benefits coordination.
      </p>
      <p>
        Specialized tools for Universal Credit and Housing Benefit calculations ensure accurate 
        entitlement assessments while detailed service charge breakdowns provide transparency 
        for both tenants and benefit agencies.
      </p>
      <ul class="feature-list">
        <li><i class="fas fa-check"></i> Real-time account balance tracking</li>
        <li><i class="fas fa-check"></i> UC/HB entitlement calculations</li>
        <li><i class="fas fa-check"></i> Detailed service charge breakdowns</li>
        <li><i class="fas fa-check"></i> Automatic reconciliation tools</li>
      </ul>
    </div>
    <div>
      <h3>Benefits Integration</h3>
      <p>
        Seamlessly coordinate with benefit agencies through our integrated benefits management system. 
        Handle Alternative Payment Arrangements, Third Party Deductions, and direct benefit payments 
        with full audit trails and reconciliation capabilities.
      </p>
      <h4>Accurate Calculations</h4>
      <p>
        Ensure correct benefit entitlements with automated calculation tools and eligibility checks.
      </p>
      <h4>Transparent Charging</h4>
      <p>
        Clear service charge breakdowns help tenants understand their charges and support benefit claims.
      </p>
      <h4>Audit Compliance</h4>
      <p>
        Comprehensive audit trails and documentation support regulatory compliance and inspections.
      </p>
    </div>
  </ContentSection>

  <ContentSection title="Account Management Features" background="light" padding="large">
    <FeatureGrid {features} layout="row" cardStyle="detailed" showBenefits={true} />
  </ContentSection>

  <ContentSection title="Account Types" padding="large">
    <FeatureGrid features={accountTypes} columns={2} cardStyle="minimal" />
  </ContentSection>

  <ContentSection title="How Account Management Works" background="light" layout="single" padding="large">
    <div>
      <h3>Streamlined Account Process</h3>
      <ol>
        <li>
          <strong>Account Setup:</strong> Create comprehensive tenant accounts with all relevant charges and payment details
        </li>
        <li>
          <strong>Benefits Assessment:</strong> Calculate UC/HB entitlements and coordinate with benefit agencies
        </li>
        <li>
          <strong>Payment Processing:</strong> Record all payments including tenant contributions and benefit payments
        </li>
        <li>
          <strong>Balance Management:</strong> Monitor account balances with real-time updates and alerts
        </li>
        <li>
          <strong>Reconciliation:</strong> Automatic reconciliation with discrepancy identification and resolution
        </li>
      </ol>
      
      <h3>Advanced Account Features</h3>
      <p>
        The system handles complex account scenarios including split payments, partial benefit eligibility, 
        temporary accommodation charges, and service charge variations. Automatic calculations ensure 
        accuracy while comprehensive reporting provides insights into account performance.
      </p>
      
      <h3>Compliance and Auditing</h3>
      <p>
        Built-in compliance tools ensure adherence to housing regulations and benefit agency requirements. 
        Complete audit trails and documentation support inspections and regulatory reporting.
      </p>
    </div>
  </ContentSection>

  <ContentSection title="Ready to Streamline Account Management?" background="primary" padding="large">
    <div style="text-align: center;">
      <h3>Start Your Free Trial Today</h3>
      <p>
        Take control of your tenant accounts with comprehensive management tools designed 
        specifically for UK housing providers.
      </p>
      <a href="/app" class="cta-button">Try Account Management</a>
    </div>
  </ContentSection>
</WebsitePage>

<style>
  .cta-button {
    display: inline-block;
    background: white;
    color: var(--color-primary);
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.2s ease;
    margin-top: 1rem;
  }

  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
</style>