<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { authToken, currentUser, isAuthenticated, logout } from '$lib/stores';
  import Button from '$lib/components/Button.svelte';

  // Admin navigation items
  const navItems = [
    { path: '/admin', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    { path: '/admin/tenants', label: 'Tenants', icon: 'fas fa-building' },
    { path: '/admin/users', label: 'Users', icon: 'fas fa-users' },
    { path: '/admin/system', label: 'System', icon: 'fas fa-cogs' }
  ];

  // Track initial load to prevent premature redirects
  let hasInitialized = false;
  
  // Check authentication after stores have initialized
  onMount(() => {
    hasInitialized = true;
  });
  
  // Reactive authentication check
  $: if (hasInitialized && !$authToken) {
    goto('/');
  }

  function handleLogout() {
    logout();
    goto('/');
  }

  function isActivePath(path: string): boolean {
    if (path === '/admin') {
      return $page.url.pathname === '/admin';
    }
    return $page.url.pathname.startsWith(path);
  }
</script>

<svelte:head>
  <title>Admin Dashboard | Rent Collection Toolkit</title>
  <meta name="description" content="Administrative dashboard for managing the Rent Collection Toolkit system">
</svelte:head>

{#if $authToken && $currentUser}
  <div class="admin-layout">
    <!-- Admin Header -->
    <header class="admin-header">
      <div class="header-content">
        <div class="header-left">
          <div class="admin-brand">
            <img src="/lambeth-council-logo.svg" alt="Lambeth Council" class="council-logo">
            <div class="brand-text">
              <h1>Admin Dashboard</h1>
              <span class="brand-subtitle">System Management</span>
            </div>
          </div>
        </div>
        
        <div class="header-right">
          <div class="admin-user">
            <div class="user-info">
              <span class="user-name">{$currentUser?.email}</span>
              <span class="user-role">Administrator</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              icon="fas fa-arrow-left"
              on:click={() => goto('/app')}
              title="Back to Application"
            >
              Back to App
            </Button>
            <Button
              variant="danger"
              size="sm"
              icon="fas fa-sign-out-alt"
              on:click={handleLogout}
              title="Logout"
            >
              Logout
            </Button>
          </div>
        </div>
      </div>
    </header>

    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
      <nav class="admin-nav">
        <ul class="nav-list">
          {#each navItems as item}
            <li class="nav-item">
              <a 
                href={item.path} 
                class="nav-link"
                class:active={isActivePath(item.path)}
              >
                <i class="{item.icon}"></i>
                <span>{item.label}</span>
              </a>
            </li>
          {/each}
        </ul>
      </nav>
    </aside>

    <!-- Admin Main Content -->
    <main class="admin-main">
      <div class="admin-content">
        <slot />
      </div>
    </main>
  </div>
{:else}
  <div class="admin-loading">
    <div class="loading-content">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Checking authentication...</p>
    </div>
  </div>
{/if}

<style lang="less">
  .admin-layout {
    display: grid;
    grid-template-areas: 
      "header header"
      "sidebar main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 70px 1fr;
    height: 100vh;
    background: #f8fafc;
  }

  .admin-header {
    grid-area: header;
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 2rem;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .admin-brand {
    display: flex;
    align-items: center;
    gap: 1rem;

    .council-logo {
      height: 40px;
      width: auto;
    }

    .brand-text {
      h1 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1.2;
      }

      .brand-subtitle {
        font-size: 0.75rem;
        opacity: 0.8;
        line-height: 1;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-info {
      text-align: right;
      
      .user-name {
        display: block;
        font-weight: 600;
        font-size: 0.875rem;
      }
      
      .user-role {
        display: block;
        font-size: 0.75rem;
        opacity: 0.8;
      }
    }
  }

  .admin-sidebar {
    grid-area: sidebar;
    background: white;
    border-right: 1px solid #e5e7eb;
    padding: 2rem 0;
  }

  .admin-nav {
    .nav-list {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .nav-item {
      margin-bottom: 0.5rem;
    }

    .nav-link {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.875rem 2rem;
      color: #6b7280;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.2s ease;
      border-right: 3px solid transparent;

      i {
        font-size: 1rem;
        width: 20px;
        text-align: center;
      }

      &:hover {
        background: #f3f4f6;
        color: #374151;
      }

      &.active {
        background: #eff6ff;
        color: #2563eb;
        border-right-color: #2563eb;
        font-weight: 600;
      }
    }
  }

  .admin-main {
    grid-area: main;
    overflow-y: auto;
    background: #f8fafc;
  }

  .admin-content {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .admin-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: #f8fafc;

    .loading-content {
      text-align: center;
      color: #6b7280;

      i {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #3b82f6;
      }

      p {
        margin: 0;
        font-size: 1rem;
      }
    }
  }

  @media (max-width: 1024px) {
    .admin-layout {
      grid-template-areas: 
        "header"
        "main";
      grid-template-columns: 1fr;
      grid-template-rows: 70px 1fr;
    }

    .admin-sidebar {
      display: none;
    }

    .header-content {
      padding: 0 1rem;
    }

    .admin-brand {
      .brand-text h1 {
        font-size: 1rem;
      }
      
      .brand-subtitle {
        display: none;
      }
    }

    .admin-user {
      .user-info {
        display: none;
      }
    }

    .admin-content {
      padding: 1rem;
    }
  }

  @media (max-width: 640px) {
    .header-content {
      gap: 0.5rem;
    }

    .admin-brand {
      gap: 0.5rem;

      .council-logo {
        height: 30px;
      }
    }

    .header-right {
      gap: 0.5rem;
    }
  }
</style>