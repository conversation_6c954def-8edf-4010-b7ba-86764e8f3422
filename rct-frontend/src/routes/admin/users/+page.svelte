<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import { UserType } from '$lib/utils/api';
  
  // Placeholder data - replace with actual API calls
  let users = [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      jobTitle: 'System Administrator',
      department: 'Housing',
      organisationName: 'Lambeth Council',
      userType: UserType.Superuser,
      lastLogin: new Date().toISOString(),
      isActive: true
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Smith',
      jobTitle: 'Housing Manager',
      department: 'Housing Services',
      organisationName: 'Lambeth Council',
      userType: UserType.Admin,
      lastLogin: new Date(Date.now() - 86400000).toISOString(),
      isActive: true
    }
  ];
  
  let isLoading = false;
  let searchTerm = '';
  let filterStatus = 'all';

  // Filtered users
  $: filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.lastName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && user.isActive) ||
                         (filterStatus === 'inactive' && !user.isActive);
    
    return matchesSearch && matchesStatus;
  });

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getFullName(user: any): string {
    return `${user.firstName} ${user.lastName}`.trim();
  }

  function getUserTypeLabel(userType: UserType): string {
    switch (userType) {
      case UserType.Superuser:
        return 'Superuser';
      case UserType.Admin:
        return 'Admin';
      case UserType.TeamMember:
        return 'Team Member';
      default:
        return 'Unknown';
    }
  }

  function getUserTypeColor(userType: UserType): string {
    switch (userType) {
      case UserType.Superuser:
        return '#dc2626'; // Red
      case UserType.Admin:
        return '#3b82f6'; // Blue
      case UserType.TeamMember:
        return '#10b981'; // Green
      default:
        return '#6b7280'; // Gray
    }
  }

  function refreshUsers() {
    // Placeholder - implement actual refresh logic
    console.log('Refreshing users...');
  }

  function addUser() {
    // Placeholder - implement add user modal
    alert('Add user functionality coming soon!');
  }

  function editUser(user: any) {
    // Placeholder - implement edit user modal
    alert(`Edit user functionality for ${user.email} coming soon!`);
  }

  function toggleUserStatus(user: any) {
    // Placeholder - implement user status toggle
    alert(`Toggle status for ${user.email} coming soon!`);
  }
</script>

<svelte:head>
  <title>User Management | Admin Dashboard</title>
</svelte:head>

<div class="user-management">
  <div class="page-header">
    <div class="header-content">
      <h1>
        <i class="fas fa-users"></i>
        User Management
      </h1>
      <p class="page-subtitle">
        Manage user accounts, permissions, and access controls
      </p>
    </div>
    
    <div class="header-actions">
      <Button 
        variant="secondary"
        size="sm"
        icon="fas fa-sync-alt"
        disabled={isLoading}
        on:click={refreshUsers}
      >
        Refresh
      </Button>
      <Button 
        variant="primary"
        size="sm"
        icon="fas fa-plus"
        on:click={addUser}
      >
        Add User
      </Button>
    </div>
  </div>

  <!-- Search and Filter Controls -->
  <div class="controls-bar">
    <div class="search-box">
      <i class="fas fa-search"></i>
      <input 
        type="text" 
        placeholder="Search users by name or email..." 
        bind:value={searchTerm}
      />
      {#if searchTerm}
        <button class="clear-search" on:click={() => searchTerm = ''}>
          <i class="fas fa-times"></i>
        </button>
      {/if}
    </div>
    
    <div class="filter-controls">
      <select bind:value={filterStatus} class="status-filter">
        <option value="all">All Users</option>
        <option value="active">Active Only</option>
        <option value="inactive">Inactive Only</option>
      </select>
    </div>
    
    <div class="filter-info">
      Showing {filteredUsers.length} of {users.length} users
    </div>
  </div>

  <!-- Summary Statistics -->
  <div class="summary-stats">
    <div class="stat-item">
      <div class="stat-value">{users.length}</div>
      <div class="stat-label">Total Users</div>
    </div>
    
    <div class="stat-item">
      <div class="stat-value">{users.filter(u => u.isActive).length}</div>
      <div class="stat-label">Active Users</div>
    </div>
    
    <div class="stat-item">
      <div class="stat-value">{users.filter(u => !u.isActive).length}</div>
      <div class="stat-label">Inactive Users</div>
    </div>
  </div>

  {#if filteredUsers.length === 0}
    <div class="empty-state">
      {#if users.length === 0}
        <i class="fas fa-users"></i>
        <h3>No Users Found</h3>
        <p>There are currently no users in the system.</p>
      {:else}
        <i class="fas fa-search"></i>
        <h3>No Results Found</h3>
        <p>No users match your search criteria. Try adjusting your search terms.</p>
      {/if}
    </div>
  {:else}
    <!-- User Table -->
    <div class="user-table-container">
      <table class="user-table">
        <thead>
          <tr>
            <th>User</th>
            <th>Organisation</th>
            <th>Role</th>
            <th>User Type</th>
            <th>Last Login</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {#each filteredUsers as user (user.id)}
            <tr class="user-row">
              <td class="user-info">
                <div class="user-cell">
                  <div class="user-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="user-content">
                    <div class="user-name">{getFullName(user)}</div>
                    <div class="user-email">{user.email}</div>
                  </div>
                </div>
              </td>
              <td class="organisation">
                <div class="org-name">{user.organisationName}</div>
                <div class="department">{user.department}</div>
              </td>
              <td class="role">
                {user.jobTitle}
              </td>
              <td class="user-type">
                <span class="user-type-badge" style="background-color: {getUserTypeColor(user.userType)}">
                  {getUserTypeLabel(user.userType)}
                </span>
              </td>
              <td class="last-login">
                {formatDate(user.lastLogin)}
              </td>
              <td class="status">
                <span class="status-badge" class:active={user.isActive} class:inactive={!user.isActive}>
                  {user.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td class="actions">
                <div class="action-buttons">
                  <Button
                    variant="secondary"
                    size="xs"
                    icon="fas fa-edit"
                    on:click={() => editUser(user)}
                    title="Edit user"
                  />
                  <Button
                    variant={user.isActive ? 'warning' : 'success'}
                    size="xs"
                    icon={user.isActive ? 'fas fa-user-slash' : 'fas fa-user-check'}
                    on:click={() => toggleUserStatus(user)}
                    title={user.isActive ? 'Deactivate user' : 'Activate user'}
                  />
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}

  <!-- Coming Soon Notice -->
  <div class="coming-soon-notice">
    <div class="notice-content">
      <i class="fas fa-info-circle"></i>
      <div class="notice-text">
        <strong>Coming Soon:</strong> Full user management features including user creation, role assignment, 
        permission management, and bulk operations will be available in the next release.
      </div>
    </div>
  </div>
</div>

<style lang="less">
  .user-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 2rem;
      
      .header-content {
        h1 {
          margin: 0 0 0.5rem 0;
          font-size: 1.875rem;
          font-weight: 700;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 0.75rem;
          
          i {
            color: #3b82f6;
          }
        }
        
        .page-subtitle {
          margin: 0;
          color: #6b7280;
          font-size: 1rem;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 1rem;
      }
    }

    .controls-bar {
      background: white;
      border-radius: 0.75rem;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
      
      .search-box {
        position: relative;
        flex: 1;
        max-width: 400px;
        
        i.fa-search {
          position: absolute;
          left: 1rem;
          top: 50%;
          transform: translateY(-50%);
          color: #9ca3af;
        }
        
        input {
          width: 100%;
          padding: 0.75rem 3rem 0.75rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          
          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
        
        .clear-search {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #9ca3af;
          cursor: pointer;
          padding: 0.25rem;
          
          &:hover {
            color: #6b7280;
          }
        }
      }
      
      .filter-controls {
        .status-filter {
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          background: white;
          
          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }
      
      .filter-info {
        color: #6b7280;
        font-size: 0.875rem;
        white-space: nowrap;
      }
    }

    .summary-stats {
      display: flex;
      gap: 2rem;
      margin-bottom: 1.5rem;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          color: #3b82f6;
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
      }
      
      h3 {
        margin: 0 0 0.5rem 0;
        color: #374151;
        font-size: 1.25rem;
      }
      
      p {
        margin: 0;
        color: #6b7280;
      }
    }

    .user-table-container {
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 2rem;
    }

    .user-table {
      width: 100%;
      border-collapse: collapse;
      
      thead {
        background: #f9fafb;
        
        th {
          padding: 1rem;
          text-align: left;
          font-weight: 600;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
        }
      }
      
      tbody {
        .user-row {
          border-bottom: 1px solid #f3f4f6;
          transition: background-color 0.2s ease;
          
          &:hover {
            background: #f9fafb;
          }
          
          td {
            padding: 1rem;
            vertical-align: middle;
          }
          
          .user-info {
            .user-cell {
              display: flex;
              align-items: center;
              gap: 1rem;
              
              .user-avatar {
                width: 40px;
                height: 40px;
                background: #3b82f6;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
              }
              
              .user-content {
                .user-name {
                  font-weight: 600;
                  color: #1f2937;
                }
                
                .user-email {
                  font-size: 0.875rem;
                  color: #6b7280;
                }
              }
            }
          }
          
          .organisation {
            .org-name {
              font-weight: 500;
              color: #1f2937;
            }
            
            .department {
              font-size: 0.875rem;
              color: #6b7280;
            }
          }
          
          .role {
            color: #6b7280;
            font-size: 0.875rem;
          }
          
          .user-type {
            .user-type-badge {
              display: inline-block;
              padding: 0.25rem 0.5rem;
              border-radius: 0.375rem;
              font-size: 0.75rem;
              font-weight: 600;
              color: white;
              text-align: center;
              white-space: nowrap;
            }
          }
          
          .last-login {
            font-size: 0.875rem;
            color: #6b7280;
          }
          
          .status {
            .status-badge {
              display: inline-flex;
              align-items: center;
              padding: 0.25rem 0.75rem;
              border-radius: 9999px;
              font-size: 0.75rem;
              font-weight: 600;
              
              &.active {
                background: #d1fae5;
                color: #059669;
              }
              
              &.inactive {
                background: #fee2e2;
                color: #dc2626;
              }
            }
          }
          
          .actions {
            .action-buttons {
              display: flex;
              gap: 0.5rem;
            }
          }
        }
      }
    }

    .coming-soon-notice {
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      border-radius: 0.75rem;
      padding: 1.5rem;
      
      .notice-content {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        
        i {
          color: #3b82f6;
          font-size: 1.25rem;
          margin-top: 0.125rem;
        }
        
        .notice-text {
          color: #1e40af;
          line-height: 1.5;
          
          strong {
            font-weight: 600;
          }
        }
      }
    }
  }

  @media (max-width: 1024px) {
    .user-management {
      .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        
        .header-actions {
          justify-content: flex-end;
        }
      }
      
      .controls-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        
        .search-box {
          max-width: none;
        }
        
        .filter-info {
          text-align: center;
        }
      }
      
      .summary-stats {
        justify-content: center;
        gap: 1rem;
      }
    }
  }

  @media (max-width: 768px) {
    .user-management {
      .user-table-container {
        overflow-x: auto;
      }
      
      .user-table {
        min-width: 800px;
      }
      
      .summary-stats {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }
    }
  }
</style>