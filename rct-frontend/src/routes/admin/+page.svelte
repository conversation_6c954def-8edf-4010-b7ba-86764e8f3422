<script lang="ts">
  import { onMount } from 'svelte';
  import { authToken } from '$lib/stores';
  import { getTenantsSummary, type TenantSummary } from '$lib/utils/api';
  import Button from '$lib/components/Button.svelte';

  // Dashboard data
  let isLoading = true;
  let error = '';
  let loadingMessage = 'Loading dashboard data...';
  let dashboardStats = {
    totalTenants: 0,
    totalUsers: 0,
    totalCases: 0,
    recentActivity: []
  };
  
  let recentTenants: TenantSummary[] = [];
  let systemHealth = {
    status: 'healthy',
    uptime: '99.9%',
    lastBackup: new Date().toISOString(),
    activeConnections: 42
  };

  onMount(() => {
    loadDashboardData();
  });

  async function loadDashboardData() {
    isLoading = true;
    error = '';
    loadingMessage = 'Loading tenant data...';

    try {
      const token = $authToken;
      if (!token) {
        error = 'Authentication required';
        return;
      }

      // Load tenant summaries (much faster than full tenant data)
      loadingMessage = 'Fetching tenant summaries...';
      const tenants = await getTenantsSummary(token);

      // Calculate dashboard statistics
      dashboardStats = {
        totalTenants: tenants.length,
        totalUsers: tenants.reduce((sum, t) => sum + t.userCount, 0),
        totalCases: tenants.reduce((sum, t) => sum + t.caseCount, 0),
        recentActivity: []
      };

      // Get most recent tenants (by creation date)
      recentTenants = [...tenants]
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5);

    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      error = err instanceof Error ? err.message : 'Failed to load dashboard data';
    } finally {
      isLoading = false;
    }
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'healthy': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  }
</script>

<svelte:head>
  <title>Admin Dashboard | Rent Collection Toolkit</title>
</svelte:head>

<div class="admin-dashboard">
  <div class="dashboard-header">
    <div class="header-content">
      <h1>
        <i class="fas fa-tachometer-alt"></i>
        System Dashboard
      </h1>
      <p class="dashboard-subtitle">
        Overview of system health, statistics, and recent activity
      </p>
    </div>
    
    <div class="header-actions">
      <Button 
        variant="secondary"
        size="sm"
        icon="fas fa-sync-alt"
        disabled={isLoading}
        on:click={loadDashboardData}
      >
        Refresh
      </Button>
    </div>
  </div>

  {#if error}
    <div class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      {error}
    </div>
  {/if}

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>{loadingMessage}</p>
    </div>
  {:else}
    <!-- System Overview Stats -->
    <div class="stats-grid">
      <div class="stat-card primary">
        <div class="stat-icon">
          <i class="fas fa-building"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{dashboardStats.totalTenants}</div>
          <div class="stat-label">Organisations</div>
        </div>
      </div>
      
      <div class="stat-card secondary">
        <div class="stat-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{dashboardStats.totalUsers}</div>
          <div class="stat-label">Total Users</div>
        </div>
      </div>
      
      <div class="stat-card success">
        <div class="stat-icon">
          <i class="fas fa-folder-open"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{dashboardStats.totalCases}</div>
          <div class="stat-label">Total Cases</div>
        </div>
      </div>
      
      <div class="stat-card info">
        <div class="stat-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{new Date().toLocaleDateString('en-GB')}</div>
          <div class="stat-label">Last Updated</div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content Grid -->
    <div class="dashboard-grid">
      <!-- System Health -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>
            <i class="fas fa-heartbeat"></i>
            System Health
          </h3>
        </div>
        <div class="card-content">
          <div class="health-status">
            <div class="health-indicator" style="color: {getStatusColor(systemHealth.status)}">
              <i class="fas fa-circle"></i>
              <span class="health-text">
                {systemHealth.status.charAt(0).toUpperCase() + systemHealth.status.slice(1)}
              </span>
            </div>
          </div>
          
          <div class="health-metrics">
            <div class="health-metric">
              <span class="metric-label">Uptime</span>
              <span class="metric-value">{systemHealth.uptime}</span>
            </div>
            <div class="health-metric">
              <span class="metric-label">Last Backup</span>
              <span class="metric-value">{formatDate(systemHealth.lastBackup)}</span>
            </div>
            <div class="health-metric">
              <span class="metric-label">Active Connections</span>
              <span class="metric-value">{systemHealth.activeConnections}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Organisations -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>
            <i class="fas fa-building"></i>
            Recent Organisations
          </h3>
          <a href="/admin/tenants" class="card-action">View All</a>
        </div>
        <div class="card-content">
          {#if recentTenants.length === 0}
            <div class="empty-state">
              <i class="fas fa-building"></i>
              <p>No organisations found</p>
            </div>
          {:else}
            <div class="recent-list">
              {#each recentTenants as tenant}
                <div class="recent-item">
                  <div class="item-icon">
                    <i class="fas fa-building"></i>
                  </div>
                  <div class="item-content">
                    <div class="item-title">{tenant.name}</div>
                    <div class="item-meta">
                      {tenant.userCount} users • {tenant.caseCount} cases
                    </div>
                  </div>
                  <div class="item-date">
                    {formatDate(tenant.createdAt)}
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>
            <i class="fas fa-bolt"></i>
            Quick Actions
          </h3>
        </div>
        <div class="card-content">
          <div class="quick-actions">
            <a href="/admin/tenants" class="quick-action">
              <i class="fas fa-building"></i>
              <span>Manage Organisations</span>
            </a>
            <a href="/admin/users" class="quick-action">
              <i class="fas fa-users"></i>
              <span>Manage Users</span>
            </a>
            <a href="/admin/system" class="quick-action">
              <i class="fas fa-cogs"></i>
              <span>System Settings</span>
            </a>
            <a href="/app" class="quick-action">
              <i class="fas fa-arrow-left"></i>
              <span>Back to Application</span>
            </a>
          </div>
        </div>
      </div>

      <!-- System Information -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>
            <i class="fas fa-info-circle"></i>
            System Information
          </h3>
        </div>
        <div class="card-content">
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">Application Version</span>
              <span class="info-value">v2.1.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">Database Status</span>
              <span class="info-value status-healthy">Connected</span>
            </div>
            <div class="info-item">
              <span class="info-label">Environment</span>
              <span class="info-value">Production</span>
            </div>
            <div class="info-item">
              <span class="info-label">Server Time</span>
              <span class="info-value">{new Date().toLocaleString('en-GB')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style lang="less">
  .admin-dashboard {
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 2rem;
      
      .header-content {
        h1 {
          margin: 0 0 0.5rem 0;
          font-size: 2rem;
          font-weight: 700;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 0.75rem;
          
          i {
            color: #3b82f6;
          }
        }
        
        .dashboard-subtitle {
          margin: 0;
          color: #6b7280;
          font-size: 1.1rem;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 1rem;
      }
    }

    .error-message {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .loading-container {
      text-align: center;
      padding: 4rem 2rem;
      
      .loading-spinner {
        font-size: 2rem;
        color: #3b82f6;
        margin-bottom: 1rem;
      }
      
      p {
        color: #6b7280;
        margin: 0;
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      border-radius: 0.75rem;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border-left: 4px solid;
      
      &.primary { border-left-color: #3b82f6; }
      &.secondary { border-left-color: #8b5cf6; }
      &.success { border-left-color: #10b981; }
      &.warning { border-left-color: #f59e0b; }
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
      }
      
      &.primary .stat-icon { background: #3b82f6; }
      &.secondary .stat-icon { background: #8b5cf6; }
      &.success .stat-icon { background: #10b981; }
      &.warning .stat-icon { background: #f59e0b; }
      
      .stat-content {
        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }
      }
    }

    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 1.5rem;
    }

    .dashboard-card {
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .card-header {
        padding: 1.5rem 1.5rem 0 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h3 {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          
          i {
            color: #3b82f6;
          }
        }
        
        .card-action {
          color: #3b82f6;
          text-decoration: none;
          font-size: 0.875rem;
          font-weight: 500;
          
          &:hover {
            color: #2563eb;
          }
        }
      }
      
      .card-content {
        padding: 1.5rem;
      }
    }

    .health-status {
      margin-bottom: 1.5rem;
      
      .health-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        
        .health-text {
          text-transform: capitalize;
        }
      }
    }

    .health-metrics {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .health-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .metric-label {
          color: #6b7280;
          font-size: 0.875rem;
        }
        
        .metric-value {
          font-weight: 600;
          color: #1f2937;
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 2rem;
      color: #6b7280;
      
      i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #d1d5db;
      }
      
      p {
        margin: 0;
      }
    }

    .recent-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .recent-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem;
      background: #f9fafb;
      border-radius: 0.5rem;
      
      .item-icon {
        width: 32px;
        height: 32px;
        background: #3b82f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.875rem;
      }
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-weight: 600;
          color: #1f2937;
        }
        
        .item-meta {
          font-size: 0.75rem;
          color: #6b7280;
        }
      }
      
      .item-date {
        font-size: 0.75rem;
        color: #6b7280;
      }
    }

    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .quick-action {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.875rem;
      background: #f9fafb;
      border-radius: 0.5rem;
      text-decoration: none;
      color: #374151;
      font-weight: 500;
      transition: all 0.2s ease;
      
      i {
        color: #3b82f6;
        width: 20px;
        text-align: center;
      }
      
      &:hover {
        background: #f3f4f6;
        color: #1f2937;
      }
    }

    .system-info {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .info-label {
          color: #6b7280;
          font-size: 0.875rem;
        }
        
        .info-value {
          font-weight: 600;
          color: #1f2937;
          
          &.status-healthy {
            color: #10b981;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .admin-dashboard {
      .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        
        .header-actions {
          justify-content: flex-end;
        }
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .dashboard-grid {
        grid-template-columns: 1fr;
      }
    }
  }
</style>