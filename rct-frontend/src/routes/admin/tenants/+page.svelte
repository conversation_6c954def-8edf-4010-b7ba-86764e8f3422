<script lang="ts">
  import { onMount } from 'svelte';
  import { authToken } from '$lib/stores';
  import { getTenants, type TenantData, type PaginatedTenants } from '$lib/utils/api';
  import Button from '$lib/components/Button.svelte';
  import Modal from '$lib/components/Modal.svelte';
  
  let tenants: TenantData[] = [];
  let totalCount = 0;
  let currentPage = 1;
  let pageSize = 20;
  let totalPages = 0;
  let isLoading = false;
  let error = '';
  let selectedTenant: TenantData | null = null;
  let showTenantModal = false;

  // Search and filter state
  let searchTerm = '';
  let sortBy = 'name';
  let sortOrder = 'asc';

  // Filtered and sorted tenants
  $: filteredTenants = tenants
    .filter(tenant => 
      tenant.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aVal = sortBy === 'name' ? a.name : 
                   sortBy === 'users' ? a.userCount :
                   sortBy === 'cases' ? a.caseCount :
                   sortBy === 'created' ? new Date(a.createdAt).getTime() :
                   a.name;
      const bVal = sortBy === 'name' ? b.name : 
                   sortBy === 'users' ? b.userCount :
                   sortBy === 'cases' ? b.caseCount :
                   sortBy === 'created' ? new Date(b.createdAt).getTime() :
                   b.name;
      
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortOrder === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      }
      
      return sortOrder === 'asc' ? (aVal as number) - (bVal as number) : (bVal as number) - (aVal as number);
    });

  // Load tenants on component mount
  onMount(() => {
    loadTenants();
  });

  async function loadTenants() {
    isLoading = true;
    error = '';
    
    try {
      const token = $authToken;
      if (!token) {
        error = 'Authentication required';
        return;
      }

      const result = await getTenants(token, currentPage, pageSize);
      tenants = result.tenants;
      totalCount = result.totalCount;
      totalPages = result.totalPages;
      console.log('Loaded tenants:', result);
    } catch (err) {
      console.error('Failed to load tenants:', err);
      error = err instanceof Error ? err.message : 'Failed to load tenants';
    } finally {
      isLoading = false;
    }
  }

  async function refreshTenants() {
    await loadTenants();
  }

  function viewTenantDetails(tenant: TenantData) {
    selectedTenant = tenant;
    showTenantModal = true;
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getFullName(user: any): string {
    return `${user.firstName} ${user.lastName}`.trim();
  }

  function handleSort(field: string) {
    if (sortBy === field) {
      sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      sortBy = field;
      sortOrder = 'asc';
    }
  }

  function getSortIcon(field: string): string {
    if (sortBy !== field) return 'fas fa-sort';
    return sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
  }
</script>

<svelte:head>
  <title>Tenant Management | Admin Dashboard</title>
</svelte:head>

<div class="tenant-management">
  <div class="page-header">
    <div class="header-content">
      <h1>
        <i class="fas fa-building"></i>
        Tenant Management
      </h1>
      <p class="page-subtitle">
        Manage all tenant organisations and their associated users
      </p>
    </div>
    
    <div class="header-actions">
      <Button 
        variant="secondary"
        size="sm"
        icon="fas fa-sync-alt"
        disabled={isLoading}
        on:click={refreshTenants}
      >
        Refresh
      </Button>
      <Button 
        variant="primary"
        size="sm"
        icon="fas fa-plus"
        disabled={true}
        title="Coming soon"
      >
        Add Tenant
      </Button>
    </div>
  </div>

  <!-- Search and Filter Controls -->
  <div class="controls-bar">
    <div class="search-box">
      <i class="fas fa-search"></i>
      <input 
        type="text" 
        placeholder="Search tenants..." 
        bind:value={searchTerm}
      />
      {#if searchTerm}
        <button class="clear-search" on:click={() => searchTerm = ''}>
          <i class="fas fa-times"></i>
        </button>
      {/if}
    </div>
    
    <div class="filter-info">
      Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, totalCount)} of {totalCount} organisations
    </div>
  </div>

  {#if error}
    <div class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      {error}
    </div>
  {/if}

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>Loading tenant organisations...</p>
    </div>
  {:else if filteredTenants.length === 0}
    <div class="empty-state">
      {#if tenants.length === 0}
        <i class="fas fa-building"></i>
        <h3>No Tenants Found</h3>
        <p>There are currently no tenant organisations in the system.</p>
      {:else}
        <i class="fas fa-search"></i>
        <h3>No Results Found</h3>
        <p>No tenants match your search criteria. Try adjusting your search terms.</p>
      {/if}
    </div>
  {:else}
    <!-- Summary Statistics -->
    <div class="summary-stats">
      <div class="stat-item">
        <div class="stat-value">{filteredTenants.length}</div>
        <div class="stat-label">Organisations</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-value">{filteredTenants.reduce((sum, t) => sum + t.userCount, 0)}</div>
        <div class="stat-label">Total Users</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-value">{filteredTenants.reduce((sum, t) => sum + t.caseCount, 0)}</div>
        <div class="stat-label">Total Cases</div>
      </div>
    </div>

    <!-- Tenant Table -->
    <div class="tenant-table-container">
      <table class="tenant-table">
        <thead>
          <tr>
            <th>
              <button class="sort-button" on:click={() => handleSort('name')}>
                Organisation Name
                <i class="{getSortIcon('name')}"></i>
              </button>
            </th>
            <th>
              <button class="sort-button" on:click={() => handleSort('users')}>
                Users
                <i class="{getSortIcon('users')}"></i>
              </button>
            </th>
            <th>
              <button class="sort-button" on:click={() => handleSort('cases')}>
                Cases
                <i class="{getSortIcon('cases')}"></i>
              </button>
            </th>
            <th>
              <button class="sort-button" on:click={() => handleSort('created')}>
                Created
                <i class="{getSortIcon('created')}"></i>
              </button>
            </th>
            <th>Last Updated</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {#each filteredTenants as tenant (tenant.id)}
            <tr class="tenant-row">
              <td class="tenant-name">
                <div class="name-cell">
                  <div class="tenant-icon">
                    <i class="fas fa-building"></i>
                  </div>
                  <div class="name-content">
                    <div class="primary-name">{tenant.name}</div>
                    <div class="tenant-id">ID: {tenant.id.substring(0, 8)}...</div>
                  </div>
                </div>
              </td>
              <td class="user-count">
                <span class="count-badge users">{tenant.userCount}</span>
              </td>
              <td class="case-count">
                <span class="count-badge cases">{tenant.caseCount}</span>
              </td>
              <td class="created-date">
                {formatDate(tenant.createdAt)}
              </td>
              <td class="updated-date">
                {formatDate(tenant.updatedAt)}
              </td>
              <td class="actions">
                <Button
                  variant="secondary"
                  size="xs"
                  icon="fas fa-eye"
                  on:click={() => viewTenantDetails(tenant)}
                >
                  View
                </Button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>

<!-- Tenant Details Modal -->
{#if selectedTenant}
  <Modal bind:isOpen={showTenantModal} title="Organisation Details" size="lg">
    <svelte:fragment slot="icon">
      <i class="fas fa-building"></i>
    </svelte:fragment>
    
    <div class="tenant-details">
      <div class="detail-section">
        <h3>Organisation Information</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>Name:</label>
            <span>{selectedTenant.name}</span>
          </div>
          <div class="detail-item">
            <label>ID:</label>
            <span class="code">{selectedTenant.id}</span>
          </div>
          <div class="detail-item">
            <label>Created:</label>
            <span>{formatDate(selectedTenant.createdAt)}</span>
          </div>
          <div class="detail-item">
            <label>Last Updated:</label>
            <span>{formatDate(selectedTenant.updatedAt)}</span>
          </div>
        </div>
      </div>

      <div class="detail-section">
        <h3>Statistics</h3>
        <div class="stats-row">
          <div class="stat-box">
            <div class="stat-number">{selectedTenant.userCount}</div>
            <div class="stat-text">Users</div>
          </div>
          <div class="stat-box">
            <div class="stat-number">{selectedTenant.caseCount}</div>
            <div class="stat-text">Cases</div>
          </div>
        </div>
      </div>

      <div class="detail-section">
        <h3>Users ({selectedTenant.users.length})</h3>
        {#if selectedTenant.users.length === 0}
          <p class="no-users">No users found for this organisation.</p>
        {:else}
          <div class="users-list">
            {#each selectedTenant.users as user (user.id)}
              <div class="user-item">
                <div class="user-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="user-info">
                  <div class="user-name">{getFullName(user)}</div>
                  <div class="user-email">{user.email}</div>
                  {#if user.jobTitle || user.department}
                    <div class="user-role">
                      {user.jobTitle}{user.jobTitle && user.department ? ' - ' : ''}{user.department}
                    </div>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </div>
  </Modal>
{/if}

<style lang="less">
  .tenant-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 2rem;
      
      .header-content {
        h1 {
          margin: 0 0 0.5rem 0;
          font-size: 1.875rem;
          font-weight: 700;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 0.75rem;
          
          i {
            color: #3b82f6;
          }
        }
        
        .page-subtitle {
          margin: 0;
          color: #6b7280;
          font-size: 1rem;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 1rem;
      }
    }

    .controls-bar {
      background: white;
      border-radius: 0.75rem;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
      
      .search-box {
        position: relative;
        flex: 1;
        max-width: 400px;
        
        i.fa-search {
          position: absolute;
          left: 1rem;
          top: 50%;
          transform: translateY(-50%);
          color: #9ca3af;
        }
        
        input {
          width: 100%;
          padding: 0.75rem 3rem 0.75rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          
          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
        
        .clear-search {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #9ca3af;
          cursor: pointer;
          padding: 0.25rem;
          
          &:hover {
            color: #6b7280;
          }
        }
      }
      
      .filter-info {
        color: #6b7280;
        font-size: 0.875rem;
        white-space: nowrap;
      }
    }

    .error-message {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .loading-container {
      text-align: center;
      padding: 4rem 2rem;
      
      .loading-spinner {
        font-size: 2rem;
        color: #3b82f6;
        margin-bottom: 1rem;
      }
      
      p {
        color: #6b7280;
        margin: 0;
      }
    }

    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
      }
      
      h3 {
        margin: 0 0 0.5rem 0;
        color: #374151;
        font-size: 1.25rem;
      }
      
      p {
        margin: 0;
        color: #6b7280;
      }
    }

    .summary-stats {
      display: flex;
      gap: 2rem;
      margin-bottom: 1.5rem;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          color: #3b82f6;
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }
      }
    }

    .tenant-table-container {
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .tenant-table {
      width: 100%;
      border-collapse: collapse;
      
      thead {
        background: #f9fafb;
        
        th {
          padding: 1rem;
          text-align: left;
          font-weight: 600;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
          
          .sort-button {
            background: none;
            border: none;
            color: inherit;
            font: inherit;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            
            &:hover {
              color: #3b82f6;
            }
            
            i {
              font-size: 0.75rem;
              opacity: 0.6;
            }
          }
        }
      }
      
      tbody {
        .tenant-row {
          border-bottom: 1px solid #f3f4f6;
          transition: background-color 0.2s ease;
          
          &:hover {
            background: #f9fafb;
          }
          
          td {
            padding: 1rem;
            vertical-align: middle;
          }
          
          .tenant-name {
            .name-cell {
              display: flex;
              align-items: center;
              gap: 1rem;
              
              .tenant-icon {
                width: 40px;
                height: 40px;
                background: #3b82f6;
                border-radius: 0.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
              }
              
              .name-content {
                .primary-name {
                  font-weight: 600;
                  color: #1f2937;
                }
                
                .tenant-id {
                  font-size: 0.75rem;
                  color: #9ca3af;
                  font-family: monospace;
                }
              }
            }
          }
          
          .count-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 2rem;
            height: 1.5rem;
            padding: 0 0.5rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            
            &.users {
              background: #ddd6fe;
              color: #7c3aed;
            }
            
            &.cases {
              background: #d1fae5;
              color: #059669;
            }
          }
          
          .created-date,
          .updated-date {
            font-size: 0.875rem;
            color: #6b7280;
          }
        }
      }
    }

    // Modal styles (reuse from previous implementation)
    .tenant-details {
      .detail-section {
        margin-bottom: 2rem;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h3 {
          margin: 0 0 1rem 0;
          color: #1f2937;
          font-size: 1.125rem;
          font-weight: 600;
        }
      }
      
      .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
      }
      
      .detail-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        
        label {
          font-weight: 600;
          color: #374151;
          font-size: 0.875rem;
        }
        
        span {
          color: #6b7280;
          
          &.code {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
          }
        }
      }
      
      .stats-row {
        display: flex;
        gap: 2rem;
      }
      
      .stat-box {
        text-align: center;
        padding: 1rem;
        background: #f9fafb;
        border-radius: 0.5rem;
        flex: 1;
        
        .stat-number {
          font-size: 1.5rem;
          font-weight: 700;
          color: #3b82f6;
        }
        
        .stat-text {
          color: #6b7280;
          font-size: 0.875rem;
          margin-top: 0.25rem;
        }
      }
      
      .no-users {
        color: #6b7280;
        font-style: italic;
        text-align: center;
        padding: 2rem;
        background: #f9fafb;
        border-radius: 0.5rem;
      }
      
      .users-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }
      
      .user-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: #f9fafb;
        border-radius: 0.5rem;
        
        .user-avatar {
          width: 40px;
          height: 40px;
          background: #3b82f6;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1rem;
        }
        
        .user-info {
          flex: 1;
          
          .user-name {
            font-weight: 600;
            color: #374151;
          }
          
          .user-email {
            color: #6b7280;
            font-size: 0.875rem;
          }
          
          .user-role {
            color: #6b7280;
            font-size: 0.75rem;
            margin-top: 0.25rem;
          }
        }
      }
    }
  }

  @media (max-width: 1024px) {
    .tenant-management {
      .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        
        .header-actions {
          justify-content: flex-end;
        }
      }
      
      .controls-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        
        .search-box {
          max-width: none;
        }
        
        .filter-info {
          text-align: center;
        }
      }
      
      .summary-stats {
        justify-content: center;
        gap: 1rem;
      }
    }
  }

  @media (max-width: 768px) {
    .tenant-management {
      .tenant-table-container {
        overflow-x: auto;
      }
      
      .tenant-table {
        min-width: 700px;
      }
      
      .summary-stats {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }
    }
  }
</style>