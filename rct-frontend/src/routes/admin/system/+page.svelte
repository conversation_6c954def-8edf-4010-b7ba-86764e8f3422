<script lang="ts">
  import Button from '$lib/components/Button.svelte';
  
  // Placeholder system settings
  let systemSettings = {
    maintenanceMode: false,
    allowRegistration: true,
    maxFileSize: 10,
    sessionTimeout: 30,
    emailNotifications: true,
    backupFrequency: 'daily'
  };
  
  let isLoading = false;
  let showMaintenanceModal = false;

  function saveSettings() {
    isLoading = true;
    // Placeholder - implement actual save logic
    setTimeout(() => {
      isLoading = false;
      alert('Settings saved successfully!');
    }, 1000);
  }

  function toggleMaintenance() {
    if (!systemSettings.maintenanceMode) {
      showMaintenanceModal = true;
    } else {
      systemSettings.maintenanceMode = false;
    }
  }

  function confirmMaintenance() {
    systemSettings.maintenanceMode = true;
    showMaintenanceModal = false;
    alert('Maintenance mode enabled. All users will be notified.');
  }

  function runBackup() {
    alert('Manual backup initiated. This may take several minutes.');
  }

  function exportLogs() {
    alert('Log export initiated. Download will begin shortly.');
  }

  function clearCache() {
    if (confirm('Are you sure you want to clear the system cache? This may temporarily affect performance.')) {
      alert('Cache cleared successfully.');
    }
  }
</script>

<svelte:head>
  <title>System Settings | Admin Dashboard</title>
</svelte:head>

<div class="system-management">
  <div class="page-header">
    <div class="header-content">
      <h1>
        <i class="fas fa-cogs"></i>
        System Management
      </h1>
      <p class="page-subtitle">
        Configure system settings, maintenance, and monitoring
      </p>
    </div>
  </div>

  <!-- System Status Overview -->
  <div class="status-grid">
    <div class="status-card healthy">
      <div class="status-icon">
        <i class="fas fa-heartbeat"></i>
      </div>
      <div class="status-content">
        <div class="status-label">System Status</div>
        <div class="status-value">Healthy</div>
      </div>
    </div>
    
    <div class="status-card info">
      <div class="status-icon">
        <i class="fas fa-clock"></i>
      </div>
      <div class="status-content">
        <div class="status-label">Uptime</div>
        <div class="status-value">99.9%</div>
      </div>
    </div>
    
    <div class="status-card warning">
      <div class="status-icon">
        <i class="fas fa-database"></i>
      </div>
      <div class="status-content">
        <div class="status-label">Database</div>
        <div class="status-value">Connected</div>
      </div>
    </div>
    
    <div class="status-card success">
      <div class="status-icon">
        <i class="fas fa-shield-alt"></i>
      </div>
      <div class="status-content">
        <div class="status-label">Security</div>
        <div class="status-value">Secure</div>
      </div>
    </div>
  </div>

  <!-- Settings Sections -->
  <div class="settings-grid">
    <!-- General Settings -->
    <div class="settings-card">
      <div class="card-header">
        <h3>
          <i class="fas fa-sliders-h"></i>
          General Settings
        </h3>
      </div>
      <div class="card-content">
        <div class="setting-item">
          <div class="setting-info">
            <label class="setting-label">Maintenance Mode</label>
            <p class="setting-description">Enable to prevent user access during system maintenance</p>
          </div>
          <div class="setting-control">
            <label class="toggle-switch">
              <input 
                type="checkbox" 
                bind:checked={systemSettings.maintenanceMode}
                on:change={toggleMaintenance}
              />
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <label class="setting-label">Allow New User Registration</label>
            <p class="setting-description">Allow new users to create accounts</p>
          </div>
          <div class="setting-control">
            <label class="toggle-switch">
              <input type="checkbox" bind:checked={systemSettings.allowRegistration} />
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <label class="setting-label">Session Timeout (minutes)</label>
            <p class="setting-description">Automatic logout after inactivity</p>
          </div>
          <div class="setting-control">
            <input 
              type="number" 
              bind:value={systemSettings.sessionTimeout} 
              min="5" 
              max="480"
              class="setting-input"
            />
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <label class="setting-label">Max File Upload Size (MB)</label>
            <p class="setting-description">Maximum file size for uploads</p>
          </div>
          <div class="setting-control">
            <input 
              type="number" 
              bind:value={systemSettings.maxFileSize} 
              min="1" 
              max="100"
              class="setting-input"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Backup & Maintenance -->
    <div class="settings-card">
      <div class="card-header">
        <h3>
          <i class="fas fa-tools"></i>
          Backup & Maintenance
        </h3>
      </div>
      <div class="card-content">
        <div class="setting-item">
          <div class="setting-info">
            <label class="setting-label">Backup Frequency</label>
            <p class="setting-description">How often to create automatic backups</p>
          </div>
          <div class="setting-control">
            <select bind:value={systemSettings.backupFrequency} class="setting-select">
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
        
        <div class="action-buttons">
          <Button
            variant="secondary"
            size="sm"
            icon="fas fa-download"
            on:click={runBackup}
          >
            Run Manual Backup
          </Button>
          
          <Button
            variant="secondary"
            size="sm"
            icon="fas fa-file-export"
            on:click={exportLogs}
          >
            Export System Logs
          </Button>
          
          <Button
            variant="warning"
            size="sm"
            icon="fas fa-broom"
            on:click={clearCache}
          >
            Clear System Cache
          </Button>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <div class="settings-card">
      <div class="card-header">
        <h3>
          <i class="fas fa-bell"></i>
          Notifications
        </h3>
      </div>
      <div class="card-content">
        <div class="setting-item">
          <div class="setting-info">
            <label class="setting-label">Email Notifications</label>
            <p class="setting-description">Send system notifications via email</p>
          </div>
          <div class="setting-control">
            <label class="toggle-switch">
              <input type="checkbox" bind:checked={systemSettings.emailNotifications} />
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div class="notification-types">
          <h4>Notification Types</h4>
          <div class="notification-list">
            <label class="notification-item">
              <input type="checkbox" checked disabled />
              <span>System Errors</span>
            </label>
            <label class="notification-item">
              <input type="checkbox" checked disabled />
              <span>Backup Completion</span>
            </label>
            <label class="notification-item">
              <input type="checkbox" checked disabled />
              <span>Security Alerts</span>
            </label>
            <label class="notification-item">
              <input type="checkbox" disabled />
              <span>Performance Warnings</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- System Information -->
    <div class="settings-card">
      <div class="card-header">
        <h3>
          <i class="fas fa-info-circle"></i>
          System Information
        </h3>
      </div>
      <div class="card-content">
        <div class="system-info">
          <div class="info-row">
            <span class="info-label">Application Version:</span>
            <span class="info-value">v2.1.0</span>
          </div>
          <div class="info-row">
            <span class="info-label">Database Version:</span>
            <span class="info-value">SQL Server 2022</span>
          </div>
          <div class="info-row">
            <span class="info-label">Server Environment:</span>
            <span class="info-value">Production</span>
          </div>
          <div class="info-row">
            <span class="info-label">Last Backup:</span>
            <span class="info-value">{new Date().toLocaleDateString('en-GB')} 02:00</span>
          </div>
          <div class="info-row">
            <span class="info-label">Total Storage Used:</span>
            <span class="info-value">2.3 GB / 100 GB</span>
          </div>
          <div class="info-row">
            <span class="info-label">Active Sessions:</span>
            <span class="info-value">42 users</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Save Button -->
  <div class="save-section">
    <Button
      variant="primary"
      size="lg"
      icon="fas fa-save"
      disabled={isLoading}
      on:click={saveSettings}
    >
      {isLoading ? 'Saving...' : 'Save Settings'}
    </Button>
  </div>

  <!-- Coming Soon Notice -->
  <div class="coming-soon-notice">
    <div class="notice-content">
      <i class="fas fa-info-circle"></i>
      <div class="notice-text">
        <strong>Coming Soon:</strong> Advanced system monitoring, performance metrics, 
        audit logs, security configuration, and automated maintenance scheduling.
      </div>
    </div>
  </div>
</div>

<!-- Maintenance Mode Confirmation Modal -->
{#if showMaintenanceModal}
  <div class="modal-overlay" on:click={() => showMaintenanceModal = false}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h3>
          <i class="fas fa-exclamation-triangle"></i>
          Enable Maintenance Mode
        </h3>
      </div>
      <div class="modal-body">
        <p>
          Enabling maintenance mode will prevent all users from accessing the application. 
          Only administrators will be able to access the system.
        </p>
        <p>
          <strong>This action will:</strong>
        </p>
        <ul>
          <li>Log out all current users</li>
          <li>Display a maintenance message to visitors</li>
          <li>Prevent new user sessions</li>
        </ul>
      </div>
      <div class="modal-footer">
        <Button
          variant="secondary"
          size="sm"
          on:click={() => showMaintenanceModal = false}
        >
          Cancel
        </Button>
        <Button
          variant="warning"
          size="sm"
          icon="fas fa-tools"
          on:click={confirmMaintenance}
        >
          Enable Maintenance Mode
        </Button>
      </div>
    </div>
  </div>
{/if}

<style lang="less">
  .system-management {
    .page-header {
      margin-bottom: 2rem;
      
      .header-content {
        h1 {
          margin: 0 0 0.5rem 0;
          font-size: 1.875rem;
          font-weight: 700;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 0.75rem;
          
          i {
            color: #3b82f6;
          }
        }
        
        .page-subtitle {
          margin: 0;
          color: #6b7280;
          font-size: 1rem;
        }
      }
    }

    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .status-card {
      background: white;
      border-radius: 0.75rem;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border-left: 4px solid;
      
      &.healthy { border-left-color: #10b981; }
      &.info { border-left-color: #3b82f6; }
      &.warning { border-left-color: #f59e0b; }
      &.success { border-left-color: #059669; }
      
      .status-icon {
        width: 48px;
        height: 48px;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
      }
      
      &.healthy .status-icon { background: #10b981; }
      &.info .status-icon { background: #3b82f6; }
      &.warning .status-icon { background: #f59e0b; }
      &.success .status-icon { background: #059669; }
      
      .status-content {
        .status-label {
          font-size: 0.875rem;
          color: #6b7280;
          margin-bottom: 0.25rem;
        }
        
        .status-value {
          font-size: 1.25rem;
          font-weight: 700;
          color: #1f2937;
        }
      }
    }

    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .settings-card {
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .card-header {
        padding: 1.5rem 1.5rem 0 1.5rem;
        
        h3 {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          
          i {
            color: #3b82f6;
          }
        }
      }
      
      .card-content {
        padding: 1.5rem;
      }
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 1rem;
      margin-bottom: 1.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .setting-info {
        flex: 1;
        
        .setting-label {
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 0.25rem;
          display: block;
        }
        
        .setting-description {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }
      }
      
      .setting-control {
        flex-shrink: 0;
      }
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
      
      input {
        opacity: 0;
        width: 0;
        height: 0;
        
        &:checked + .slider {
          background-color: #3b82f6;
          
          &:before {
            transform: translateX(26px);
          }
        }
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #d1d5db;
        transition: 0.3s;
        border-radius: 24px;
        
        &:before {
          position: absolute;
          content: "";
          height: 18px;
          width: 18px;
          left: 3px;
          bottom: 3px;
          background-color: white;
          transition: 0.3s;
          border-radius: 50%;
        }
      }
    }

    .setting-input,
    .setting-select {
      padding: 0.5rem 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      min-width: 100px;
      
      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      margin-top: 1rem;
    }

    .notification-types {
      margin-top: 1rem;
      
      h4 {
        margin: 0 0 0.75rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
      }
      
      .notification-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      
      .notification-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
        cursor: pointer;
        
        input[type="checkbox"] {
          margin: 0;
        }
        
        &:has(input:disabled) {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .system-info {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .info-label {
          color: #6b7280;
          font-size: 0.875rem;
        }
        
        .info-value {
          font-weight: 600;
          color: #1f2937;
          font-size: 0.875rem;
        }
      }
    }

    .save-section {
      text-align: center;
      margin-bottom: 2rem;
    }

    .coming-soon-notice {
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      border-radius: 0.75rem;
      padding: 1.5rem;
      
      .notice-content {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        
        i {
          color: #3b82f6;
          font-size: 1.25rem;
          margin-top: 0.125rem;
        }
        
        .notice-text {
          color: #1e40af;
          line-height: 1.5;
          
          strong {
            font-weight: 600;
          }
        }
      }
    }
  }

  // Modal styles
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    
    .modal-header {
      padding: 1.5rem 1.5rem 0 1.5rem;
      
      h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        i {
          color: #f59e0b;
        }
      }
    }
    
    .modal-body {
      padding: 1.5rem;
      color: #6b7280;
      line-height: 1.6;
      
      ul {
        margin: 0.5rem 0;
        padding-left: 1.5rem;
      }
      
      li {
        margin-bottom: 0.25rem;
      }
    }
    
    .modal-footer {
      padding: 0 1.5rem 1.5rem 1.5rem;
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
    }
  }

  @media (max-width: 768px) {
    .system-management {
      .status-grid {
        grid-template-columns: 1fr;
      }
      
      .settings-grid {
        grid-template-columns: 1fr;
      }
      
      .setting-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
      }
    }
  }
</style>