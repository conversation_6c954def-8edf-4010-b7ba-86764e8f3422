<script lang="ts">
  import { onMount } from 'svelte';
  import { currentUser, refreshUserData } from '$lib/stores';
  import { 
    userPreferences, 
    updateUserPreferences, 
    updateDesignSystemSettings, 
    resetDesignSystemToDefaults,
    resetAllPreferencesToDefaults,
    calendarPreferenceLabels,
    type CalendarPreference,
    type CalculatorViewPreference
  } from '$lib/stores/userPreferences';
  import Button from '$lib/components/Button.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  import { goto } from '$app/navigation';

  // Local state for form inputs
  let firstName = '';
  let lastName = '';
  let email = '';
  
  // Subscribe to current user data
  $: if ($currentUser) {
    firstName = $currentUser.firstName || '';
    lastName = $currentUser.lastName || '';
    email = $currentUser.email || '';
  }
  
  // Active tab state
  let activeTab: 'personal' | 'preferences' | 'design' = 'personal';
  
  // Design system tab state
  let designTab: 'sizes' | 'colors' = 'sizes';
  
  // Tab configuration
  const mainTabs = [
    {
      id: 'personal',
      label: 'Personal Information',
      icon: 'fas fa-user'
    },
    {
      id: 'preferences',
      label: 'App Preferences',
      icon: 'fas fa-cog'
    },
    {
      id: 'design',
      label: 'Design System',
      icon: 'fas fa-palette'
    }
  ];
  
  const designTabs = [
    {
      id: 'sizes',
      label: 'Sizes',
      icon: 'fas fa-text-height'
    },
    {
      id: 'colors',
      label: 'Colors',
      icon: 'fas fa-palette'
    }
  ];
  
  function handleMainTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId as 'personal' | 'preferences' | 'design';
  }
  
  function handleDesignTabChange(event: CustomEvent<{ tabId: string }>) {
    designTab = event.detail.tabId as 'sizes' | 'colors';
  }
  
  // Handle personal info updates (placeholder for now)
  function updatePersonalInfo() {
    // TODO: Implement API call to update user info
    console.log('Update personal info:', { firstName, lastName, email });
    alert('Personal information updated successfully! (Note: This is currently a placeholder)');
  }
  
  // Navigation is always in header mode now - sidebar system removed
  
  function updateCalculatorView(view: CalculatorViewPreference) {
    updateUserPreferences({ calculatorView: view });
  }
  
  function updateViewMode(mode: 'compact' | 'full') {
    updateUserPreferences({ viewMode: mode });
  }
  
  function updateCalendarPreference(preference: CalendarPreference) {
    updateUserPreferences({ calendarPreference: preference });
  }
  
  function updateNotesPosition(position: 'bottom' | 'sidebar') {
    updateUserPreferences({ notesPosition: position });
  }
  
  // Design system update handlers
  function handleDesignSystemChange(property: string, value: number | string) {
    updateDesignSystemSettings({ [property]: value });
  }

  function resetDesignSystem() {
    resetDesignSystemToDefaults();
  }

  function resetAllSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      resetAllPreferencesToDefaults();
    }
  }

  // Copy current design system values as CSS
  function copyDesignSystemAsCSS() {
    if (typeof navigator === 'undefined') return;
    const ds = $userPreferences.designSystem;
    const css = `:root {
  --label-weight: ${ds.labelWeight};
  --label-color: ${ds.labelColor};
  --label-size: ${ds.labelSize}rem;
  --value-size: ${ds.valueSize}rem;
  --cell-padding: ${ds.cellPadding}rem;
  --subheader-size: ${ds.subheaderSize}rem;
  --button-size: ${ds.buttonSize}rem;
  --section-header-size: ${ds.sectionHeaderSize}rem;
  --color-primary: ${ds.colorPrimary};
  --color-secondary: ${ds.colorSecondary};
  --color-red: ${ds.colorRed};
  --color-orange: ${ds.colorOrange};
  --color-yellow: ${ds.colorYellow};
  --color-green: ${ds.colorGreen};
  --bg: ${ds.bgColor};
  --color-instance-inbound: ${ds.colorInstanceInbound};
  --color-instance-outbound: ${ds.colorInstanceOutbound};
  --gap: ${ds.gap}rem;
}`;

    navigator.clipboard.writeText(css).then(() => {
      alert('CSS copied to clipboard!');
    }).catch(err => {
      console.error('Failed to copy CSS: ', err);
      alert('Failed to copy CSS to clipboard');
    });
  }

  // Save current design system values to separate browser storage (DevMode compatibility)
  function saveDesignSystemToBrowserStorage() {
    if (typeof localStorage === 'undefined') return;
    const ds = $userPreferences.designSystem;
    const values = {
      labelWeight: ds.labelWeight,
      labelColor: ds.labelColor,
      labelSize: ds.labelSize,
      valueSize: ds.valueSize,
      cellPadding: ds.cellPadding,
      subheaderSize: ds.subheaderSize,
      buttonSize: ds.buttonSize,
      sectionHeaderSize: ds.sectionHeaderSize,
      colorPrimary: ds.colorPrimary,
      colorSecondary: ds.colorSecondary,
      colorRed: ds.colorRed,
      colorOrange: ds.colorOrange,
      colorYellow: ds.colorYellow,
      colorGreen: ds.colorGreen,
      bgColor: ds.bgColor,
      colorInstanceInbound: ds.colorInstanceInbound,
      colorInstanceOutbound: ds.colorInstanceOutbound,
      gap: ds.gap,
    };

    localStorage.setItem('devmode-values', JSON.stringify(values));
    alert('Design system values saved to browser storage!');
  }

  // Load design system values from browser storage (DevMode compatibility)
  function loadDesignSystemFromBrowserStorage() {
    if (typeof localStorage === 'undefined') return;
    const stored = localStorage.getItem('devmode-values');
    if (stored) {
      try {
        const values = JSON.parse(stored);
        updateDesignSystemSettings({
          labelWeight: values.labelWeight ?? 400,
          labelColor: values.labelColor ?? '#374151',
          labelSize: values.labelSize ?? 0.8,
          valueSize: values.valueSize ?? 0.9,
          cellPadding: values.cellPadding ?? 0.275,
          subheaderSize: values.subheaderSize ?? 0.9,
          buttonSize: values.buttonSize ?? 0.9,
          sectionHeaderSize: values.sectionHeaderSize ?? 0.825,
          colorPrimary: values.colorPrimary ?? '#025582',
          colorSecondary: values.colorSecondary ?? 'rgb(0, 51, 68)',
          colorRed: values.colorRed ?? '#dc2626',
          colorOrange: values.colorOrange ?? '#ea580c',
          colorYellow: values.colorYellow ?? '#ca8a04',
          colorGreen: values.colorGreen ?? '#059669',
          bgColor: values.bgColor ?? '#0255822d',
          colorInstanceInbound: values.colorInstanceInbound ?? '#DB2955',
          colorInstanceOutbound: values.colorInstanceOutbound ?? '#4DA1A9',
          gap: values.gap ?? 0.5,
        });
        alert('Design system values loaded from browser storage!');
      } catch (err) {
        console.error('Failed to load from browser storage:', err);
        alert('Failed to load design system values from browser storage');
      }
    } else {
      alert('No saved design system values found in browser storage');
    }
  }

  // Clear design system browser storage
  function clearDesignSystemBrowserStorage() {
    if (typeof localStorage === 'undefined') return;
    localStorage.removeItem('devmode-values');
    alert('Design system browser storage cleared!');
  }
  
  // Password change placeholder
  function changePassword() {
    alert('Password change functionality will be implemented in a future update.');
  }
  
  // Go back to main app
  function goBack() {
    goto('/app');
  }
  
  let isLoadingProfile = true;
  let profileLoadError = '';

  onMount(async () => {
    try {
      // Refresh user data from API (no localStorage fallback)
      const success = await refreshUserData();
      if (!success || !$currentUser) {
        // No valid user data, redirect to login
        goto('/');
        return;
      }
    } catch (error) {
      console.error('Failed to load profile data:', error);
      profileLoadError = 'Failed to load profile data. Please try again.';
    } finally {
      isLoadingProfile = false;
    }
  });
</script>

<svelte:head>
  <title>Profile Settings - Rent Collection Toolkit</title>
</svelte:head>

<div class="profile-settings">
  <div class="profile-header">
    <div class="header-content">
      <Button 
        variant="ghost"
        icon="fas fa-arrow-left"
        on:click={goBack}
        className="back-button"
      >
        Back to App
      </Button>
      <div class="header-title">
        <h1>Profile Settings</h1>
        <p>Manage your account and application preferences</p>
      </div>
    </div>
  </div>

  {#if isLoadingProfile}
    <div class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading profile data...</p>
      </div>
    </div>
  {:else if profileLoadError}
    <div class="error-container">
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{profileLoadError}</p>
        <Button variant="primary" on:click={() => window.location.reload()}>
          Reload Page
        </Button>
      </div>
    </div>
  {:else}

  <div class="settings-container">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <Tabs
        tabs={mainTabs}
        bind:activeTab
        on:tabChange={handleMainTabChange}
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      {#if activeTab === 'personal'}
        <!-- Personal Information Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Personal Information</h2>
            <p>Update your basic account information</p>
          </div>
          
          <div class="form-grid">
            <div class="form-group">
              <label for="firstName">First Name</label>
              <input 
                id="firstName"
                type="text" 
                bind:value={firstName}
                placeholder="Enter your first name"
                class="form-input"
              />
            </div>
            
            <div class="form-group">
              <label for="lastName">Last Name</label>
              <input 
                id="lastName"
                type="text" 
                bind:value={lastName}
                placeholder="Enter your last name"
                class="form-input"
              />
            </div>
            
            <div class="form-group full-width">
              <label for="email">Email Address</label>
              <input 
                id="email"
                type="email" 
                bind:value={email}
                placeholder="Enter your email"
                class="form-input"
              />
            </div>
          </div>
          
          <div class="form-actions">
            <Button 
              variant="primary"
              icon="fas fa-save"
              on:click={updatePersonalInfo}
            >
              Save Changes
            </Button>
            <Button 
              variant="secondary"
              icon="fas fa-key"
              on:click={changePassword}
            >
              Change Password
            </Button>
          </div>
        </div>
      {/if}

      {#if activeTab === 'preferences'}
        <!-- App Preferences Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Application Preferences</h2>
            <p>Customize how the application behaves and looks</p>
          </div>
          
          <!-- Navigation Style setting removed - header navigation is now the only option -->
          
          <!-- Calculator View -->
          <div class="preference-group">
            <h3>Calculator View</h3>
            <p class="preference-description">Default view for the rent calculator</p>
            <div class="toggle-group">
              <label class="toggle-option {$userPreferences.calculatorView === 'view1' ? 'active' : ''}">
                <input 
                  type="radio" 
                  name="calculatorView" 
                  value="view1"
                  checked={$userPreferences.calculatorView === 'view1'}
                  on:change={() => updateCalculatorView('view1')}
                />
                <div class="option-content">
                  <i class="fas fa-list"></i>
                  <span>View 1 (Compact)</span>
                </div>
              </label>
              <label class="toggle-option {$userPreferences.calculatorView === 'view2' ? 'active' : ''}">
                <input 
                  type="radio" 
                  name="calculatorView" 
                  value="view2"
                  checked={$userPreferences.calculatorView === 'view2'}
                  on:change={() => updateCalculatorView('view2')}
                />
                <div class="option-content">
                  <i class="fas fa-th-large"></i>
                  <span>View 2 (Full)</span>
                </div>
              </label>
            </div>
          </div>
          
          <!-- App View Mode -->
          <div class="preference-group">
            <h3>Application Layout</h3>
            <p class="preference-description">Choose between tabbed or full layout view</p>
            <div class="toggle-group">
              <label class="toggle-option {$userPreferences.viewMode === 'compact' ? 'active' : ''}">
                <input 
                  type="radio" 
                  name="viewMode" 
                  value="compact"
                  checked={$userPreferences.viewMode === 'compact'}
                  on:change={() => updateViewMode('compact')}
                />
                <div class="option-content">
                  <i class="fas fa-window-minimize"></i>
                  <span>Tab View</span>
                </div>
              </label>
              <label class="toggle-option {$userPreferences.viewMode === 'full' ? 'active' : ''}">
                <input 
                  type="radio" 
                  name="viewMode" 
                  value="full"
                  checked={$userPreferences.viewMode === 'full'}
                  on:change={() => updateViewMode('full')}
                />
                <div class="option-content">
                  <i class="fas fa-window-restore"></i>
                  <span>Full View</span>
                </div>
              </label>
            </div>
          </div>
          
          <!-- Notes Position -->
          <div class="preference-group">
            <h3>Notes Position</h3>
            <p class="preference-description">Choose where to display the notes section</p>
            <div class="toggle-group">
              <label class="toggle-option {$userPreferences.notesPosition === 'bottom' ? 'active' : ''}">
                <input 
                  type="radio" 
                  name="notesPosition" 
                  value="bottom"
                  checked={$userPreferences.notesPosition === 'bottom'}
                  on:change={() => updateNotesPosition('bottom')}
                />
                <div class="option-content">
                  <i class="fas fa-window-minimize"></i>
                  <span>Bottom</span>
                </div>
              </label>
              <label class="toggle-option {$userPreferences.notesPosition === 'sidebar' ? 'active' : ''}">
                <input 
                  type="radio" 
                  name="notesPosition" 
                  value="sidebar"
                  checked={$userPreferences.notesPosition === 'sidebar'}
                  on:change={() => updateNotesPosition('sidebar')}
                />
                <div class="option-content">
                  <i class="fas fa-sidebar"></i>
                  <span>Right Sidebar</span>
                </div>
              </label>
            </div>
          </div>
          
          <!-- Calendar Preference -->
          <div class="preference-group">
            <h3>Default Calendar Application</h3>
            <p class="preference-description">Choose your preferred calendar app for adding events</p>
            <div class="radio-group">
              {#each Object.entries(calendarPreferenceLabels) as [value, label]}
                <label class="radio-option">
                  <input 
                    type="radio" 
                    name="calendarPreference" 
                    {value}
                    checked={$userPreferences.calendarPreference === value}
                    on:change={() => updateCalendarPreference(value)}
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-label">{label}</span>
                </label>
              {/each}
            </div>
          </div>
        </div>
      {/if}

      {#if activeTab === 'design'}
        <!-- Design System Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Design System</h2>
            <p>Customize the visual appearance of the application</p>
          </div>
          
          <!-- Design Sub-tabs -->
          <div class="design-tabs">
            <Tabs
              tabs={designTabs}
              bind:activeTab={designTab}
              on:tabChange={handleDesignTabChange}
            />
          </div>
          
          <div class="design-content">
            {#if designTab === 'sizes'}
              <!-- Size Controls -->
              <div class="design-controls">
                <div class="control-group">
                  <label for="section-header-size">Section Header Size: {$userPreferences.designSystem.sectionHeaderSize}rem</label>
                  <input 
                    id="section-header-size"
                    type="range" 
                    min="0.5" 
                    max="1.5" 
                    step="0.025"
                    value={$userPreferences.designSystem.sectionHeaderSize}
                    on:input={(e) => handleDesignSystemChange('sectionHeaderSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="subheader-size">Subheader Size: {$userPreferences.designSystem.subheaderSize}rem</label>
                  <input 
                    id="subheader-size"
                    type="range" 
                    min="0.5" 
                    max="2" 
                    step="0.025"
                    value={$userPreferences.designSystem.subheaderSize}
                    on:input={(e) => handleDesignSystemChange('subheaderSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="label-weight">Label Weight: {$userPreferences.designSystem.labelWeight}</label>
                  <input 
                    id="label-weight"
                    type="range" 
                    min="100" 
                    max="900" 
                    step="100"
                    value={$userPreferences.designSystem.labelWeight}
                    on:input={(e) => handleDesignSystemChange('labelWeight', parseInt(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>100</span>
                    <span>900</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="label-size">Label Size: {$userPreferences.designSystem.labelSize}rem</label>
                  <input 
                    id="label-size"
                    type="range" 
                    min="0.5" 
                    max="1.5" 
                    step="0.025"
                    value={$userPreferences.designSystem.labelSize}
                    on:input={(e) => handleDesignSystemChange('labelSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="value-size">Value Size: {$userPreferences.designSystem.valueSize}rem</label>
                  <input 
                    id="value-size"
                    type="range" 
                    min="0.5" 
                    max="2" 
                    step="0.025"
                    value={$userPreferences.designSystem.valueSize}
                    on:input={(e) => handleDesignSystemChange('valueSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="button-size">Button Size: {$userPreferences.designSystem.buttonSize}rem</label>
                  <input 
                    id="button-size"
                    type="range" 
                    min="0.5" 
                    max="1.5" 
                    step="0.025"
                    value={$userPreferences.designSystem.buttonSize}
                    on:input={(e) => handleDesignSystemChange('buttonSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="cell-padding">Cell Padding: {$userPreferences.designSystem.cellPadding}rem</label>
                  <input 
                    id="cell-padding"
                    type="range" 
                    min="0" 
                    max="2" 
                    step="0.025"
                    value={$userPreferences.designSystem.cellPadding}
                    on:input={(e) => handleDesignSystemChange('cellPadding', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="gap">Gap (Spacing): {$userPreferences.designSystem.gap}rem</label>
                  <input 
                    id="gap"
                    type="range" 
                    min="0" 
                    max="2" 
                    step="0.05"
                    value={$userPreferences.designSystem.gap}
                    on:input={(e) => handleDesignSystemChange('gap', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0rem</span>
                    <span>2rem</span>
                  </div>
                </div>
              </div>
            {/if}

            {#if designTab === 'colors'}
              <!-- Color Controls -->
              <div class="design-controls">
                <div class="color-control-group">
                  <label for="color-primary">Primary Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-primary"
                      type="color" 
                      value={$userPreferences.designSystem.colorPrimary}
                      on:input={(e) => handleDesignSystemChange('colorPrimary', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorPrimary}
                      on:input={(e) => handleDesignSystemChange('colorPrimary', e.target.value)}
                      class="color-text"
                      placeholder="#025582"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-secondary">Secondary Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-secondary"
                      type="color" 
                      value={$userPreferences.designSystem.colorSecondary}
                      on:input={(e) => handleDesignSystemChange('colorSecondary', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorSecondary}
                      on:input={(e) => handleDesignSystemChange('colorSecondary', e.target.value)}
                      class="color-text"
                      placeholder="rgb(0, 51, 68)"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="label-color">Label Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="label-color"
                      type="color" 
                      value={$userPreferences.designSystem.labelColor}
                      on:input={(e) => handleDesignSystemChange('labelColor', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.labelColor}
                      on:input={(e) => handleDesignSystemChange('labelColor', e.target.value)}
                      class="color-text"
                      placeholder="#374151"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-red">Red Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-red"
                      type="color" 
                      value={$userPreferences.designSystem.colorRed}
                      on:input={(e) => handleDesignSystemChange('colorRed', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorRed}
                      on:input={(e) => handleDesignSystemChange('colorRed', e.target.value)}
                      class="color-text"
                      placeholder="#dc2626"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-orange">Orange Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-orange"
                      type="color" 
                      value={$userPreferences.designSystem.colorOrange}
                      on:input={(e) => handleDesignSystemChange('colorOrange', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorOrange}
                      on:input={(e) => handleDesignSystemChange('colorOrange', e.target.value)}
                      class="color-text"
                      placeholder="#ea580c"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-yellow">Yellow Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-yellow"
                      type="color" 
                      value={$userPreferences.designSystem.colorYellow}
                      on:input={(e) => handleDesignSystemChange('colorYellow', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorYellow}
                      on:input={(e) => handleDesignSystemChange('colorYellow', e.target.value)}
                      class="color-text"
                      placeholder="#ca8a04"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-green">Green Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-green"
                      type="color" 
                      value={$userPreferences.designSystem.colorGreen}
                      on:input={(e) => handleDesignSystemChange('colorGreen', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorGreen}
                      on:input={(e) => handleDesignSystemChange('colorGreen', e.target.value)}
                      class="color-text"
                      placeholder="#059669"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="bg-color">Background Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="bg-color"
                      type="color" 
                      value={$userPreferences.designSystem.bgColor}
                      on:input={(e) => handleDesignSystemChange('bgColor', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.bgColor}
                      on:input={(e) => handleDesignSystemChange('bgColor', e.target.value)}
                      class="color-text"
                      placeholder="#0255822d"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-instance-inbound">Instance Inbound Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-instance-inbound"
                      type="color" 
                      value={$userPreferences.designSystem.colorInstanceInbound}
                      on:input={(e) => handleDesignSystemChange('colorInstanceInbound', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorInstanceInbound}
                      on:input={(e) => handleDesignSystemChange('colorInstanceInbound', e.target.value)}
                      class="color-text"
                      placeholder="#DB2955"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-instance-outbound">Instance Outbound Color</label>
                  <div class="color-picker-container">
                    <input 
                      id="color-instance-outbound"
                      type="color" 
                      value={$userPreferences.designSystem.colorInstanceOutbound}
                      on:input={(e) => handleDesignSystemChange('colorInstanceOutbound', e.target.value)}
                      class="color-picker"
                    />
                    <input 
                      type="text" 
                      value={$userPreferences.designSystem.colorInstanceOutbound}
                      on:input={(e) => handleDesignSystemChange('colorInstanceOutbound', e.target.value)}
                      class="color-text"
                      placeholder="#4DA1A9"
                    />
                  </div>
                </div>
              </div>
            {/if}
          </div>
          
          <!-- Design Actions -->
          <div class="design-actions">
            <div class="action-group">
              <h4>Quick Actions</h4>
              <div class="action-buttons">
                <Button
                  variant="success"
                  icon="fas fa-copy"
                  size="sm"
                  on:click={copyDesignSystemAsCSS}
                >
                  Copy CSS
                </Button>
                <Button
                  variant="danger"
                  icon="fas fa-undo"
                  size="sm"
                  on:click={resetDesignSystem}
                >
                  Reset Design System
                </Button>
                <Button
                  variant="warning"
                  icon="fas fa-refresh"
                  size="sm"
                  on:click={resetAllSettings}
                >
                  Reset All Settings
                </Button>
              </div>
            </div>

            <div class="action-group">
              <h4>DevMode Storage</h4>
              <p class="action-description">Import/export design system values from DevMode component</p>
              <div class="action-buttons">
                <Button
                  variant="primary"
                  icon="fas fa-save"
                  size="sm"
                  on:click={saveDesignSystemToBrowserStorage}
                >
                  Save to DevMode
                </Button>
                <Button
                  variant="secondary"
                  icon="fas fa-download"
                  size="sm"
                  on:click={loadDesignSystemFromBrowserStorage}
                >
                  Load from DevMode
                </Button>
                <Button
                  variant="ghost"
                  icon="fas fa-trash"
                  size="sm"
                  on:click={clearDesignSystemBrowserStorage}
                >
                  Clear DevMode Storage
                </Button>
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
  {/if}
</div>

<style>
  .profile-settings {
    min-height: 100vh;
    /* background: #f8fafc; */
    /* padding: 2rem; */
  }

  .profile-header {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--gap);
  }

  .header-content {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .header-title h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
  }

  .header-title p {
    margin: 0;
    color: #6b7280;
    font-size: 1rem;
  }

  .settings-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .tab-navigation {
    padding: 2rem 2rem 0 2rem;
    background: white;
  }

  .tab-content {
    padding: 2rem;
  }

  .settings-section {
    max-width: 800px;
  }

  .section-header {
    margin-bottom: 2rem;
  }

  .section-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
  }

  .section-header p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
  }

  /* Personal Information Form */
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  .form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .form-input {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
  }

  /* Preferences */
  .preference-group {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .preference-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .preference-group h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }

  .preference-description {
    margin: 0 0 1rem 0;
    color: #6b7280;
    font-size: 0.875rem;
  }

  .toggle-group {
    display: flex;
    gap: 1rem;
  }

  .toggle-option {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
  }

  .toggle-option:hover {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .toggle-option.active {
    border-color: var(--color-primary);
    background: rgba(2, 85, 130, 0.05);
  }

  .toggle-option input {
    display: none;
  }

  .option-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
  }

  .option-content i {
    color: var(--color-primary);
    font-size: 1.25rem;
  }

  .option-content span {
    font-weight: 500;
    color: #374151;
  }

  /* Radio Groups */
  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .radio-option:hover {
    background: #f9fafb;
    border-color: #d1d5db;
  }

  .radio-option input {
    display: none;
  }

  .radio-custom {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
  }

  .radio-option input:checked + .radio-custom {
    border-color: var(--color-primary);
  }

  .radio-option input:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--color-primary);
    border-radius: 50%;
  }

  .radio-label {
    font-weight: 500;
    color: #374151;
  }

  /* Design System */
  .design-tabs {
    margin-bottom: 2rem;
  }

  .design-content {
    margin-bottom: 2rem;
  }

  .design-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .control-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .range-slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #e5e7eb;
    outline: none;
    border: none;
    -webkit-appearance: none;
    padding: 5px;
  }

  .range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    transition: background 0.2s ease;
  }

  .range-slider::-webkit-slider-thumb:hover {
    background: color-mix(in srgb, var(--color-primary) 85%, black 15%);
  }

  .range-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: none;
    transition: background 0.2s ease;
  }

  .range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6b7280;
  }

  .color-control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .color-control-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .color-picker-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .color-picker {
    width: 48px;
    height: 40px;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    cursor: pointer;
    background: none;
    padding: 0;
  }

  .color-text {
    flex: 1;
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    color: #374151;
  }

  .color-text:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .design-actions {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
  }

  .action-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .action-group h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
  }

  .action-description {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  /* Loading and Error States */
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 2rem;
  }

  .loading-spinner,
  .error-message {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .loading-spinner i {
    font-size: 2rem;
    color: var(--color-primary);
  }

  .error-message i {
    font-size: 2rem;
    color: var(--color-red);
  }

  .loading-spinner p,
  .error-message p {
    margin: 0;
    color: #6b7280;
    font-size: 1rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .profile-settings {
      padding: 1rem;
    }

    .header-content {
      padding: 1.5rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .tab-content {
      padding: 1.5rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .toggle-group {
      flex-direction: column;
    }

    .form-actions {
      flex-direction: column;
    }

    .design-actions {
      gap: 1.5rem;
    }

    .action-buttons {
      flex-direction: column;
    }
  }
</style>