<script lang="ts">
  import { onMount } from 'svelte';
  import { isAuthenticated, isAuthLoading, saveCaseData, loadUserCases, activeTabId, activeAnalyticsTabId, errorMessage } from '$lib/stores';
  import CaseLog from '$lib/components/CaseLog.svelte';
  import HeaderNav from '$lib/components/HeaderNav.svelte';
  import DevMode from '$lib/components/DevMode.svelte';
  import LoginForm from '$lib/components/LoginForm.svelte';
  import '$lib/app-fonts.css';
  
  // Global state for session tracking
  let sessionStartTime = new Date();
  let showCaseLog = false;
  let devModeEnabled = false;
  
  onMount(() => {
    sessionStartTime = new Date();
    
    // Listen for case log events from child components
    window.addEventListener('openCaseLog', () => {
      showCaseLog = true;
    });
    
  });
  
  
  // Event handlers for header events
  
  function handleCaseLog() {
    showCaseLog = true;
  }

  function handleDevMode() {
    devModeEnabled = !devModeEnabled;
  }

  // Footer menu handlers
  async function handleSave() {
    const currentTabId = $activeTabId;
    if (!currentTabId) {
      errorMessage.set('No active case to save.');
      return;
    }
    
    try {
      const success = await saveCaseData(currentTabId);
      if (success) {
        // Show success message briefly
        const originalError = $errorMessage;
        errorMessage.set('Case saved successfully!');
        setTimeout(() => errorMessage.set(originalError), 3000);
      }
    } catch (error) {
      console.error('Save failed:', error);
      errorMessage.set('Failed to save case. Please try again.');
    }
  }
  
  async function handleLoad() {
    try {
      await loadUserCases();
      // Show case log to allow user to select a case to load
      showCaseLog = true;
    } catch (error) {
      console.error('Load failed:', error);
      errorMessage.set('Failed to load cases. Please try again.');
    }
  }
  
  function handleUserGuide() {
    console.log('User guide - to be implemented');
    alert('User guide will be implemented in a future update.');
  }
  
  function handleAbout() {
    alert(`Rent Collection Toolkit v0.1.0\n\nA modern web application for UK council and housing association rent collection agents.\n\nBuilt with SvelteKit and designed for efficiency and ease of use.`);
  }

  // Reactive statement to add/remove global classes
  $: if (typeof document !== 'undefined') {
    // Always use header navigation mode
    document.body.classList.add('header-nav-mode');
    
    // Add analytics-page class when analytics tab is active
    if ($activeAnalyticsTabId) {
      document.body.classList.add('analytics-page');
    } else {
      document.body.classList.remove('analytics-page');
    }
  }
  
</script>

{#if $isAuthLoading}
  <!-- Loading state - checking authentication -->
  <div class="auth-loading-container">
    <div class="auth-loading-content">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>Loading...</p>
    </div>
  </div>
{:else if !$isAuthenticated}
  <!-- Not authenticated - show login form -->
  <LoginForm />
{:else}
  <!-- Authenticated - show app content -->
  <div class="app-container">
    <!-- Header Navigation -->
    <HeaderNav on:caseLog={handleCaseLog} on:devMode={handleDevMode} {devModeEnabled} />

    <!-- Main Content Area -->
    <main class="app-main header-nav">
      <slot />
    </main>

    <!-- Footer -->
    <!-- <footer class="app-footer">
    <div class="footer-content">
      <div class="footer-left">
        <nav class="footer-nav">
          <div class="nav-menu">
            <span class="nav-label">File</span>
            <div class="nav-items">
              <button class="nav-item" on:click={handleSave}><i class="fas fa-save"></i> Save</button>
              <button class="nav-item" on:click={handleLoad}><i class="fas fa-folder-open"></i> Load</button>
            </div>
          </div>
          
          <div class="nav-menu">
            <span class="nav-label">View</span>
            <div class="nav-items">
              <button class="nav-item" on:click={handleCaseLog}><i class="fas fa-folder-open"></i> Case Log</button>
            </div>
          </div>
          
          <div class="nav-menu">
            <span class="nav-label">Help</span>
            <div class="nav-items">
              <button class="nav-item" on:click={handleUserGuide}><i class="fas fa-question-circle"></i> User Guide</button>
              <button class="nav-item" on:click={handleAbout}><i class="fas fa-info-circle"></i> About</button>
            </div>
          </div>
        </nav>
      </div>
      
      <div class="footer-center">
        <span class="session-info">
          Session: {formatSessionTime(sessionStartTime)}
        </span>
      </div>
      
      <div class="footer-right">
        <span class="version-info">v0.1.0</span>
      </div>
    </div>
  </footer> -->
  </div>
{/if}


<!-- Case Log Modal -->
<CaseLog bind:isOpen={showCaseLog} />

<!-- Dev Mode Window -->
<DevMode bind:isVisible={devModeEnabled} />

<style lang="less">
  :global {
    /* Form Elements */
    input, textarea, select {
      font-family: 'Martian Mono', 'Courier New', monospace;
      font-size: var(--value-size);
      line-height: 1.5;
      color: #111827;
      background-color: #ffffff;
      border: 1px solid var(--border);
      border-radius: 0.375rem;
      padding: calc(var(--cell-padding) * 1.5) var(--cell-padding);
      transition: border-color 150ms ease-in-out, box-shadow 150ms ease-in-out;
      box-sizing: border-box;
      &::placeholder {
        color: black;
      }
    }

    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    input:disabled, textarea:disabled, select:disabled {
      background-color: #f3f4f6;
      color: #6b7280;
      cursor: not-allowed;
    }
    .output-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--gap);
      .output-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;
        padding: 0.75rem;
        background-color: #f9fafb;
        border-radius: 0.375rem;
        border: 3px solid #d1d5db;
        label {
          font-weight: 500;
          color: #374151;
          font-size: 0.875rem;
        }
        .output-value {
          font-weight: 600;
          color: #111827;
        }
      }
    }

    @media (max-width: 768px) {
      .output-grid {
        grid-template-columns: 1fr 1fr;
        .sync-item,
        .output-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;
        }
      }

      .tab-buttons {
        gap: 0.25rem;
      }
    }

    h4 {
      font-size: var(--subheader-size);
    }

    .info-section {
      background-color: var(--color-primary-light);
      border: 1px solid var(--border);
      border-radius: 0.5rem;
      padding: 1.5rem;
      p {
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  /* Auth Loading Styles */
  .auth-loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f9fafb;
  }

  .auth-loading-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .loading-spinner i {
    font-size: 2rem;
    color: var(--color-primary);
  }

  .auth-loading-content p {
    margin: 0;
    color: #6b7280;
    font-size: 1rem;
    font-weight: 500;
  }

  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f9fafb;
  }

  /* Main Content */
  .app-main {
    background: var(--bg);
    flex: 1;
    padding: 1.5rem 1.5rem 14rem 1.5rem;
    margin-left: 0;
    width: 100%;
    padding-top: 95px; /* Account for header height */
    transition: margin-right 300ms ease-in-out;
  }

  /* Notes sidebar active - adjust main content */
  :global(.notes-sidebar-active) .app-main {
    // padding-right: 420px; /* Account for notes sidebar width (400px + 20px margin) */
    width: calc(100% - 400px);
  }

  /* Analytics page - full width when in header nav mode */
  :global(.analytics-page.header-nav-mode) .app-main {
    padding-right: 1.5rem !important;
    width: 100% !important;
  }

  :global(.analytics-page.notes-sidebar-active.header-nav-mode) .app-main {
    padding-right: 1.5rem !important;
    width: 100% !important;
  }

  /* Footer Styles */
  .app-footer {
    background-color: #ffffff;
    border-top: 1px solid var(--border);
    padding: 0.75rem 1.5rem;
    margin-left: 0;
    width: 100%;
  }

  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    font-size: 0.875rem;
    color: #4b5563;
  }

  .footer-nav {
    display: flex;
    gap: 2rem;
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .nav-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .nav-items {
    display: flex;
    gap: 0.5rem;
  }

  .nav-item {
    background: none;
    border: none;
    padding: 0.25rem 0.5rem;
    color: #6b7280;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 150ms ease-in-out;
  }

  .nav-item:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .footer-center {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .session-info {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.75rem;
  }

  .version-info {
    color: #6b7280;
    font-size: 0.75rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .app-main {
      padding: 1rem;
      margin-left: 0;
      width: 100%;
    }

    /* Notes sidebar is forced to bottom on mobile, so no right margin adjustment needed */
    :global(.notes-sidebar-active) .app-main {
      margin-right: 0;
      width: 100%;
    }

    .footer-content {
      flex-direction: column;
      gap: 0.75rem;
      text-align: center;
    }

    .footer-nav {
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }

    .nav-menu {
      flex-direction: column;
      gap: 0.25rem;
      align-items: center;
    }

    .nav-items {
      flex-direction: column;
      gap: 0.25rem;
    }
  }

  /* Force bottom mode on screens too narrow for sidebar (900px and below) */
  @media (max-width: 900px) {
    /* Notes sidebar is forced to bottom, so no right margin adjustment needed */
    :global(.notes-sidebar-active) .app-main {
      margin-right: 0;
      width: 100%;
    }
  }

  /* Tablet responsive adjustments (901px to 1024px) */
  @media (max-width: 1024px) and (min-width: 901px) {
    /* Notes sidebar is smaller on tablets (300px + 20px margin = 320px) */
    :global(.notes-sidebar-active) .app-main {
      margin-right: 320px;
      width: calc(100% - 320px);
    }
  }
</style>