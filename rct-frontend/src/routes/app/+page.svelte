<script lang="ts">
  import { onMount } from 'svelte';
  import {
    activeTabId,
    currentInstanceData,
    currentTab,
    isAuthenticated,
    currentUser,
    viewMode,
    activeCaseDetailTabId,
    caseDetailTabs,
    activeSettingsTabId,
    settingsTabs,
    activeProfileTabId,
    profileTabs,
    activeAnalyticsTabId,
    analyticsTabs,
    activeTab as globalActiveTab,
    highestPriorityAction,
    suggestedActions
  } from '$lib/stores';
  import { userPreferences } from '$lib/stores/userPreferences';
  import RentCalculator from '$lib/components/RentCalculator.svelte';
  import ArrangementPlanner from '$lib/components/ArrangementPlanner.svelte';
  import AccountCharges from '$lib/components/AccountCharges.svelte';
  import TabButtons from '$lib/components/TabButtons.svelte';
  import NotesSection from '$lib/components/NotesSection.svelte';
  import CaseDetailTab from '$lib/components/CaseDetailTab.svelte';
  import SettingsTab from '$lib/components/SettingsTab.svelte';
  import ProfileTab from '$lib/components/ProfileTab.svelte';
  import AnalyticsDashboard from '$lib/components/AnalyticsDashboard.svelte';
  
  type TabType = 'rent-calculator' | 'arrangement-planner' | 'account-charges';
  
  let activeTab: TabType = 'rent-calculator';
  
  onMount(() => {
    // Listen for tab switching events from header navigation
    window.addEventListener('switchTab', (event: CustomEvent) => {
      activeTab = event.detail;
    });
    
  });
  
  function setActiveTab(tab: TabType) {
    activeTab = tab;
  }
  
  function handleTabChange(event: CustomEvent) {
    activeTab = event.detail;
  }
  
  function getInstanceClass(): string {
    const rentCalculatorData = $currentInstanceData?.rentCalculator;
    const actionRequired = rentCalculatorData?.currentBalance > 0;
    
    if (actionRequired && $suggestedActions.length > 0 && $highestPriorityAction !== 'low') {
      return `priority-${$highestPriorityAction}`;
    } else if (actionRequired) {
      return 'contact-required';
    }
    return 'default';
  }

  
  
</script>

<div class="dashboard {$viewMode === 'full' ? 'full-mode' : ''} {$userPreferences.notesPosition === 'sidebar' ? 'notes-sidebar-mode' : ''}">
  
  
  <!-- Tab Navigation - Only show in compact view -->
  {#if $viewMode === 'compact'}
    <TabButtons 
      {activeTab} 
      on:tabChange={handleTabChange} 
    />
  {/if}

  <!-- Content Area -->
  {#if $activeAnalyticsTabId}
    <!-- Analytics Tab View -->
    {#each $analyticsTabs as tab}
      {#if tab.id === $activeAnalyticsTabId}
        <div class="analytics-content">
          <AnalyticsDashboard isFullView={$viewMode === 'full'} />
        </div>
      {/if}
    {/each}
  {:else if $activeProfileTabId}
    <!-- Profile Tab View -->
    {#each $profileTabs as tab}
      {#if tab.id === $activeProfileTabId}
        <div class="settings-content">
          <ProfileTab {tab} />
        </div>
      {/if}
    {/each}
  {:else if $activeSettingsTabId}
    <!-- Settings Tab View -->
    {#each $settingsTabs as tab}
      {#if tab.id === $activeSettingsTabId}
        <div class="settings-content">
          <SettingsTab {tab} />
        </div>
      {/if}
    {/each}
  {:else if $activeCaseDetailTabId}
    <!-- Case Detail Tab View -->
    {#each $caseDetailTabs as tab}
      {#if tab.id === $activeCaseDetailTabId}
        <div class="case-detail-content">
          <CaseDetailTab {tab} />
        </div>
      {/if}
    {/each}
  {:else if $viewMode === 'compact'}
    <!-- Tab View: Single tab content -->
    <div class="tab-content {getInstanceClass()}">
      {#if activeTab === 'rent-calculator'}
        <div id="calculations-analysis">
          <RentCalculator />
        </div>
      {:else if activeTab === 'arrangement-planner'}
        <div id="arrangement-planner">
          <ArrangementPlanner />
        </div>
      {:else if activeTab === 'account-charges'}
        <div id="service-charges-benefits">
          <AccountCharges />
        </div>
      {/if}
    </div>
    
    <!-- Always show NotesSection at the bottom in tab view -->
    <NotesSection tenantReference={$currentInstanceData?.rentCalculator?.reference || ''} />
  {:else}
    <!-- Full View: 2x2 Grid Layout -->
    <div class="full-view-grid {getInstanceClass()}">
      <div id="calculations-analysis" class="grid-item calculator-section {getInstanceClass()}">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-calculator"></i>
            <h3>Calculations and Analysis</h3>
          </div>
        </div>
        <RentCalculator isFullView={true} />
      </div>
      
      <div id="arrangement-planner" class="grid-item arrangements-section {getInstanceClass()}">
        <div class="section-header">
          <div class="section-title"> 
            <div class="section-title-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <h3>Arrangement Planner</h3>
          </div>

        </div>
        <ArrangementPlanner isFullView={true} />
      </div>
      
      <div id="service-charges-benefits" class="grid-item charges-section {getInstanceClass()}">
        <div class="section-header">
          <div class="section-title">
            <div class="section-title-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3>Service Charges & Benefits</h3>
          </div>
        </div>
        <AccountCharges isFullView={true} />
      </div>
    </div>

    <NotesSection />
  {/if}
</div>

<style>
  .dashboard {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .dashboard.full-mode {
    max-width: none;
  }

  /* Adjust layout when notes are in sidebar mode */
  .dashboard.notes-sidebar-mode {
    width: calc(100% - 0px); /* Reduce width by sidebar width + margin */
    max-width: calc(100% - 0px);
    min-width: 400px; /* Ensure minimum usable width */
    transition: width 0.3s ease, max-width 0.3s ease;
  }

  .dashboard.notes-sidebar-mode.full-mode {
    max-width: calc(100% - 0px);
    min-width: 400px;
  }

  /* Tab Content for Tab View */
  .tab-content {
    background-color: #ffffff;
    border-radius: 0 0 0.5rem 0.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    min-height: 600px;
    margin-top: -1px;
    position: relative;
    z-index: 0;
  }

  .grid-item.priority-urgent {
    border: var(--thick-border-width) solid var(--color-red);
  }

  .grid-item.priority-high {
    border: var(--thick-border-width) solid var(--color-orange);
  }

  .grid-item.priority-medium {
    border: var(--thick-border-width) solid var(--color-yellow);
  }

  .grid-item.priority-low {
    border: var(--thick-border-width) solid var(--color-green);
  }

  .grid-item.contact-required {
    border: var(--thick-border-width) solid var(--color-secondary);
  }

  .grid-item.default {
    border: var(--thick-border-width) solid var(--color-primary);
  }

  .tab-content.priority-urgent {
    border-left: var(--thick-border-width) solid var(--color-red);
    border-bottom: var(--thick-border-width) solid var(--color-red);
    border-right: var(--thick-border-width) solid var(--color-red);
  }

  .tab-content.priority-high {
    border-left: var(--thick-border-width) solid var(--color-orange);
    border-bottom: var(--thick-border-width) solid var(--color-orange);
    border-right: var(--thick-border-width) solid var(--color-orange);
  }

  .tab-content.priority-medium {
    border-left: var(--thick-border-width) solid var(--color-yellow);
    border-bottom: var(--thick-border-width) solid var(--color-yellow);
    border-right: var(--thick-border-width) solid var(--color-yellow);
  }

  .tab-content.priority-low {
    border-left: var(--thick-border-width) solid var(--color-green);
    border-bottom: var(--thick-border-width) solid var(--color-green);
    border-right: var(--thick-border-width) solid var(--color-green);
  }

  .tab-content.contact-required {
    border-left: var(--thick-border-width) solid var(--color-secondary);
    border-bottom: var(--thick-border-width) solid var(--color-secondary);
    border-right: var(--thick-border-width) solid var(--color-secondary);
  }

  .tab-content.default {
    border-left: var(--thick-border-width) solid var(--color-primary);
    border-bottom: var(--thick-border-width) solid var(--color-primary);
    border-right: var(--thick-border-width) solid var(--color-primary);
  }

  /* Full View Grid Layout */
  .full-view-grid {
    display: grid;
    grid-template-columns: 1fr;
    /* grid-template-rows: 1fr 1fr; */
    gap: 1rem;
    min-height: 700px;
    max-width: 990px;
    margin: 0 auto;
  }

  /* Adjust grid for sidebar mode */
  .notes-sidebar-mode .full-view-grid {
    max-width: 100%; /* Let parent container control the width */
  }


  .grid-item {
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--gap);
    background-color: #f9fafb;
    border-bottom: 1px solid var(--border);
    font-weight: 600;
    color: #374151;
    font-size: var(--section-header-size);
    position: relative;
    h3 {
      font-size: var(--section-header-size);
    }
  }
  

  .section-title {
    display: flex;
    align-items: center;
    gap: calc(var(--gap) / 2);
  }


  .section-header i {
    color: var(--color-primary);
  }

  .section-header h3 {
    margin: 0;
    font-weight: 600;
  }

  /* Case Detail Content */
  .case-detail-content {
    width: 100%;
    height: calc(100vh - 120px);
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  /* Settings Content */
  .settings-content {
    width: 100%;
    height: calc(100vh - 120px);
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  /* Analytics Content */
  .analytics-content {
    width: 100%;
    min-height: calc(100vh - 120px);
    background: #f8fafc;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: visible;
  }

  /* Responsive Design */
  @media (max-width: 900px) {
    /* Force bottom mode on screens too narrow for sidebar */
    .dashboard.notes-sidebar-mode {
      width: 100%;
      max-width: none;
      min-width: auto;
    }

    .dashboard.notes-sidebar-mode.full-mode {
      max-width: none;
      min-width: auto;
    }
  }

  @media (max-width: 768px) {
    .dashboard {
      margin: 0;
    }

    /* Disable sidebar mode on mobile */
    .dashboard.notes-sidebar-mode {
      width: 100%;
      max-width: none;
      min-width: auto;
    }

    .full-view-grid {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto auto;
      gap: 0.5rem;
      min-height: auto;
    }

    .calculator-section,
    .notes-section,
    .arrangements-section,
    .charges-section {
      grid-area: auto;
    }
  }

  @media (max-width: 1024px) and (min-width: 769px) {
    .dashboard {
      max-width: 95%;
    }

    /* Adjust sidebar mode for tablets */
    .dashboard.notes-sidebar-mode {
      width: calc(100% - 320px); /* Smaller sidebar on tablets */
      max-width: calc(100% - 320px);
      min-width: 300px; /* Smaller minimum on tablets */
    }

    .dashboard.notes-sidebar-mode.full-mode {
      max-width: calc(100% - 320px);
      min-width: 300px;
    }
  }

  @media (min-width: 1024px) {
    .dashboard {
      max-width: 1200px;
    }

    /* Ensure proper width calculation for larger screens */
    .dashboard.notes-sidebar-mode {
      width: calc(100% - 0px);
      max-width: calc(100% - 0px);
    }

    .dashboard.notes-sidebar-mode.full-mode {
      max-width: calc(100% - 0px);
    }
  }

  @media (min-width: 1440px) {
    .dashboard {
      max-width: 1400px;
    }

    /* For very large screens, maintain sidebar proportions */
    .dashboard.notes-sidebar-mode {
      width: calc(100% - 0px);
      max-width: calc(100% - 0px);
    }

    .dashboard.notes-sidebar-mode.full-mode {
      max-width: calc(100% - 0px);
    }
  }
</style>
