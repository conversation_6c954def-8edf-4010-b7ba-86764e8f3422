<script lang="ts">
  import { settings, addTag, updateTag, deleteTag, addResponseTemplate, updateResponseTemplate, deleteResponseTemplate, addTalkingPoint, updateTalkingPoint, deleteTalkingPoint, resetToDefaults, type Tag, type ResponseTemplate, type TalkingPoint } from '$lib/stores/settings';
  import Button from '$lib/components/Button.svelte';
  import FormGroup from '$lib/components/FormGroup.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  
  let activeTab: 'tags' | 'templates' | 'talking-points' = 'tags';

  $: tabsData = [
    {
      id: 'tags',
      label: 'Tags',
      icon: 'fas fa-tags',
      count: $settings.tags.length
    },
    {
      id: 'templates',
      label: 'Response Templates',
      icon: 'fas fa-comment-dots',
      count: $settings.responseTemplates.length
    },
    {
      id: 'talking-points',
      label: 'Talking Points',
      icon: 'fas fa-comments',
      count: $settings.talkingPoints.length
    }
  ];

  function handleTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId as 'tags' | 'templates' | 'talking-points';
  }
  
  // Tag editing state
  let editingTag: Tag | null = null;
  let newTag = { name: '', color: '#059669', description: '' };
  let showAddTag = false;
  
  // Template editing state
  let editingTemplate: ResponseTemplate | null = null;
  let newTemplate = { category: '', title: '', text: '', tags: [], keywords: [] };
  let showAddTemplate = false;
  
  // Talking Point editing state
  let editingTalkingPoint: TalkingPoint | null = null;
  let newTalkingPoint = { category: '', title: '', content: '', keywords: [] };
  let showAddTalkingPoint = false;
  
  // Get unique categories for template dropdown
  $: categories = [...new Set($settings.responseTemplates.map(t => t.category))].sort();
  
  // Get unique categories for talking points dropdown
  $: talkingPointCategories = [...new Set($settings.talkingPoints.map(t => t.category))].sort();
  
  function startAddingTag() {
    showAddTag = true;
    newTag = { name: '', color: '#059669', description: '' };
  }
  
  function cancelAddTag() {
    showAddTag = false;
    newTag = { name: '', color: '#059669', description: '' };
  }
  
  function saveNewTag() {
    if (newTag.name.trim()) {
      addTag({
        name: newTag.name.trim(),
        color: newTag.color,
        description: newTag.description.trim() || undefined
      });
      cancelAddTag();
    }
  }
  
  function startEditingTag(tag: Tag) {
    editingTag = { ...tag };
  }
  
  function cancelEditTag() {
    editingTag = null;
  }
  
  function saveEditedTag() {
    if (editingTag && editingTag.name.trim()) {
      updateTag(editingTag.id, {
        name: editingTag.name.trim(),
        color: editingTag.color,
        description: editingTag.description?.trim() || undefined
      });
      editingTag = null;
    }
  }
  
  function confirmDeleteTag(tag: Tag) {
    if (confirm(`Delete tag "${tag.name}"?\n\nThis action cannot be undone.`)) {
      deleteTag(tag.id);
    }
  }
  
  function startAddingTemplate() {
    showAddTemplate = true;
    newTemplate = { category: '', title: '', text: '', tags: [], keywords: [] };
  }
  
  function cancelAddTemplate() {
    showAddTemplate = false;
    newTemplate = { category: '', title: '', text: '', tags: [], keywords: [] };
  }
  
  function saveNewTemplate() {
    if (newTemplate.title.trim() && newTemplate.text.trim() && newTemplate.category.trim()) {
      addResponseTemplate({
        category: newTemplate.category.trim(),
        title: newTemplate.title.trim(),
        text: newTemplate.text.trim(),
        tags: newTemplate.tags,
        keywords: newTemplate.keywords
      });
      cancelAddTemplate();
    }
  }
  
  function startEditingTemplate(template: ResponseTemplate) {
    editingTemplate = { 
      ...template, 
      keywords: template.keywords ? [...template.keywords] : []
    };
  }
  
  function cancelEditTemplate() {
    editingTemplate = null;
  }
  
  function saveEditedTemplate() {
    if (editingTemplate && editingTemplate.title.trim() && editingTemplate.text.trim() && editingTemplate.category.trim()) {
      updateResponseTemplate(editingTemplate.id, {
        category: editingTemplate.category.trim(),
        title: editingTemplate.title.trim(),
        text: editingTemplate.text.trim(),
        tags: editingTemplate.tags,
        keywords: editingTemplate.keywords
      });
      editingTemplate = null;
    }
  }
  
  function confirmDeleteTemplate(template: ResponseTemplate) {
    if (confirm(`Delete template "${template.title}"?\n\nThis action cannot be undone.`)) {
      deleteResponseTemplate(template.id);
    }
  }
  
  // Talking Point management functions
  function startAddingTalkingPoint() {
    showAddTalkingPoint = true;
    newTalkingPoint = { category: '', title: '', content: '', keywords: [] };
  }
  
  function cancelAddTalkingPoint() {
    showAddTalkingPoint = false;
    newTalkingPoint = { category: '', title: '', content: '', keywords: [] };
  }
  
  function saveNewTalkingPoint() {
    if (newTalkingPoint.title.trim() && newTalkingPoint.content.trim() && newTalkingPoint.category.trim()) {
      addTalkingPoint({
        category: newTalkingPoint.category.trim(),
        title: newTalkingPoint.title.trim(),
        content: newTalkingPoint.content.trim(),
        keywords: newTalkingPoint.keywords
      });
      cancelAddTalkingPoint();
    }
  }
  
  function startEditingTalkingPoint(talkingPoint: TalkingPoint) {
    editingTalkingPoint = { 
      ...talkingPoint, 
      keywords: talkingPoint.keywords ? [...talkingPoint.keywords] : []
    };
  }
  
  function cancelEditTalkingPoint() {
    editingTalkingPoint = null;
  }
  
  function saveEditedTalkingPoint() {
    if (editingTalkingPoint && editingTalkingPoint.title.trim() && editingTalkingPoint.content.trim() && editingTalkingPoint.category.trim()) {
      updateTalkingPoint(editingTalkingPoint.id, {
        category: editingTalkingPoint.category.trim(),
        title: editingTalkingPoint.title.trim(),
        content: editingTalkingPoint.content.trim(),
        keywords: editingTalkingPoint.keywords
      });
      editingTalkingPoint = null;
    }
  }
  
  function confirmDeleteTalkingPoint(talkingPoint: TalkingPoint) {
    if (confirm(`Delete talking point "${talkingPoint.title}"?\n\nThis action cannot be undone.`)) {
      deleteTalkingPoint(talkingPoint.id);
    }
  }
  
  function confirmResetToDefaults() {
    if (confirm('Reset all settings to defaults?\n\nThis will delete all custom tags and templates. This action cannot be undone.')) {
      resetToDefaults();
    }
  }

  // Keywords management functions for templates
  function addKeywordToNew(keyword: string) {
    if (keyword.trim() && !newTemplate.keywords.includes(keyword.trim())) {
      newTemplate.keywords = [...newTemplate.keywords, keyword.trim()];
    }
  }

  function removeKeywordFromNew(index: number) {
    newTemplate.keywords = newTemplate.keywords.filter((_, i) => i !== index);
  }

  function addKeywordToEdit(keyword: string) {
    if (editingTemplate && keyword.trim() && !editingTemplate.keywords?.includes(keyword.trim())) {
      editingTemplate.keywords = [...(editingTemplate.keywords || []), keyword.trim()];
    }
  }

  function removeKeywordFromEdit(index: number) {
    if (editingTemplate) {
      editingTemplate.keywords = editingTemplate.keywords?.filter((_, i) => i !== index) || [];
    }
  }
  
  // Keywords management functions for talking points
  function addKeywordToNewTalkingPoint(keyword: string) {
    if (keyword.trim() && !newTalkingPoint.keywords.includes(keyword.trim())) {
      newTalkingPoint.keywords = [...newTalkingPoint.keywords, keyword.trim()];
    }
  }

  function removeKeywordFromNewTalkingPoint(index: number) {
    newTalkingPoint.keywords = newTalkingPoint.keywords.filter((_, i) => i !== index);
  }

  function addKeywordToEditTalkingPoint(keyword: string) {
    if (editingTalkingPoint && keyword.trim() && !editingTalkingPoint.keywords?.includes(keyword.trim())) {
      editingTalkingPoint.keywords = [...(editingTalkingPoint.keywords || []), keyword.trim()];
    }
  }

  function removeKeywordFromEditTalkingPoint(index: number) {
    if (editingTalkingPoint) {
      editingTalkingPoint.keywords = editingTalkingPoint.keywords?.filter((_, i) => i !== index) || [];
    }
  }
  
  // Color picker predefined colors
  const predefinedColors = [
    '#059669', '#0078d4', '#dc2626', '#7c3aed', '#ea580c', 
    '#ca8a04', '#0d9488', '#8b5cf6', '#e11d48', '#0891b2'
  ];
</script>

<div class="settings-page">
  <div class="settings-header">
    <div class="header-content">
      <Button 
        variant="ghost"
        icon="fas fa-arrow-left"
        href="/app"
        className="back-button"
      >
        Back to App
      </Button>
      <div class="header-title">
        <h1>Tags & Templates</h1>
        <p>Manage your tags, response templates, and talking points</p>
      </div>
      <div class="header-actions">
        <Button
          variant="outline"
          size="sm"
          icon="fas fa-undo"
          on:click={confirmResetToDefaults}
        >
          Reset to Defaults
        </Button>
      </div>
    </div>
  </div>

  <div class="settings-container">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <Tabs
        tabs={tabsData}
        bind:activeTab
        on:tabChange={handleTabChange}
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
  
      {#if activeTab === 'tags'}
        <!-- Tags Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Manage Tags</h2>
            <p>Create and manage tags for organizing your data</p>
          </div>
          <div class="section-action-header">
            <Button
              variant="primary"
              size="sm"
              icon="fas fa-plus"
              on:click={startAddingTag}
            >
              Add Tag
            </Button>
          </div>
      
      <!-- Add New Tag Form -->
      {#if showAddTag}
        <div class="add-form">
          <h4>Add New Tag</h4>
          <div class="form-grid">
            <FormGroup
              id="new-tag-name"
              label="Tag Name"
              bind:value={newTag.name}
              placeholder="e.g., Follow-up Required"
              type="text"
            />
            <div class="color-picker-group">
              <label>Color</label>
              <div class="color-picker">
                <input 
                  type="color" 
                  bind:value={newTag.color}
                  class="color-input"
                />
                <div class="color-preview" style="background-color: {newTag.color};"></div>
                <div class="predefined-colors">
                  {#each predefinedColors as color}
                    <button
                      class="color-option"
                      style="background-color: {color};"
                      on:click={() => newTag.color = color}
                      title="Select {color}"
                    ></button>
                  {/each}
                </div>
              </div>
            </div>
            <div class="full-width">
              <FormGroup
                id="new-tag-description"
                label="Description (Optional)"
                bind:value={newTag.description}
                placeholder="Brief description of when to use this tag"
                type="text"
              />
            </div>
          </div>
          <div class="form-actions">
            <Button variant="primary" on:click={saveNewTag}>Save Tag</Button>
            <Button variant="outline" on:click={cancelAddTag}>Cancel</Button>
          </div>
        </div>
      {/if}
      
      <!-- Tags List -->
      <div class="tags-list">
        {#each $settings.tags as tag}
          <div class="tag-item">
            {#if editingTag && editingTag.id === tag.id}
              <!-- Edit Mode -->
              <div class="tag-edit">
                <div class="form-grid">
                  <FormGroup
                    id="edit-tag-name"
                    label="Tag Name"
                    bind:value={editingTag.name}
                    type="text"
                  />
                  <div class="color-picker-group">
                    <label>Color</label>
                    <div class="color-picker">
                      <input 
                        type="color" 
                        bind:value={editingTag.color}
                        class="color-input"
                      />
                      <div class="color-preview" style="background-color: {editingTag.color};"></div>
                      <div class="predefined-colors">
                        {#each predefinedColors as color}
                          <button
                            class="color-option"
                            style="background-color: {color};"
                            on:click={() => editingTag.color = color}
                          ></button>
                        {/each}
                      </div>
                    </div>
                  </div>
                  <div class="full-width">
                    <FormGroup
                      id="edit-tag-description"
                      label="Description"
                      bind:value={editingTag.description}
                      type="text"
                    />
                  </div>
                </div>
                <div class="form-actions">
                  <Button variant="primary" size="sm" on:click={saveEditedTag}>Save</Button>
                  <Button variant="outline" size="sm" on:click={cancelEditTag}>Cancel</Button>
                </div>
              </div>
            {:else}
              <!-- View Mode -->
              <div class="tag-display">
                <div class="tag-info">
                  <span class="tag-chip" style="background-color: {tag.color};">
                    {tag.name}
                  </span>
                  {#if tag.description}
                    <span class="tag-description">{tag.description}</span>
                  {/if}
                </div>
                <div class="tag-actions">
                  <Button variant="outline" size="sm" icon="fas fa-edit" on:click={() => startEditingTag(tag)}>
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" icon="fas fa-trash" on:click={() => confirmDeleteTag(tag)}>
                    Delete
                  </Button>
                </div>
              </div>
            {/if}
          </div>
        {/each}
          </div>
        </div>
      {/if}
      
      {#if activeTab === 'templates'}
        <!-- Response Templates Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Manage Response Templates</h2>
            <p>Create and manage response templates for consistent communication</p>
          </div>
          <div class="section-action-header">
            <Button
              variant="primary"
              size="sm"
              icon="fas fa-plus"
              on:click={startAddingTemplate}
            >
              Add Template
            </Button>
          </div>
      
      <!-- Add New Template Form -->
      {#if showAddTemplate}
        <div class="add-form">
          <h4>Add New Response Template</h4>
          <div class="form-grid">
            <FormGroup
              id="new-template-category"
              label="Category"
              bind:value={newTemplate.category}
              placeholder="e.g., Payment Plans"
              type="text"
            />
            <FormGroup
              id="new-template-title"
              label="Title"
              bind:value={newTemplate.title}
              placeholder="e.g., Payment Arrangement Offer"
              type="text"
            />
            <div class="full-width">
              <label>Template Text</label>
              <textarea
                bind:value={newTemplate.text}
                placeholder="Enter the response template text. Use [placeholders] for variable content."
                rows="4"
                class="template-textarea"
              ></textarea>
            </div>
            <div class="full-width">
              <label>Keywords (for search)</label>
              <div class="keywords-input">
                <input
                  type="text"
                  placeholder="Add keywords separated by commas (e.g., job, work, employment)"
                  on:keydown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                      keywords.forEach(keyword => addKeywordToNew(keyword));
                      e.target.value = '';
                    }
                  }}
                  class="keyword-input"
                />
                <div class="keywords-list">
                  {#each newTemplate.keywords as keyword, index}
                    <span class="keyword-tag">
                      {keyword}
                      <button type="button" on:click={() => removeKeywordFromNew(index)} class="remove-keyword">×</button>
                    </span>
                  {/each}
                </div>
              </div>
            </div>
          </div>
          <div class="form-actions">
            <Button variant="primary" on:click={saveNewTemplate}>Save Template</Button>
            <Button variant="outline" on:click={cancelAddTemplate}>Cancel</Button>
          </div>
        </div>
      {/if}
      
      <!-- Templates List -->
      <div class="templates-list">
        {#each $settings.responseTemplates as template}
          <div class="template-item">
            {#if editingTemplate && editingTemplate.id === template.id}
              <!-- Edit Mode -->
              <div class="template-edit">
                <div class="form-grid">
                  <FormGroup
                    id="edit-template-category"
                    label="Category"
                    bind:value={editingTemplate.category}
                    type="text"
                  />
                  <FormGroup
                    id="edit-template-title"
                    label="Title"
                    bind:value={editingTemplate.title}
                    type="text"
                  />
                  <div class="full-width">
                    <label>Template Text</label>
                    <textarea
                      bind:value={editingTemplate.text}
                      rows="4"
                      class="template-textarea"
                    ></textarea>
                  </div>
                  <div class="full-width">
                    <label>Keywords (for search)</label>
                    <div class="keywords-input">
                      <input
                        type="text"
                        placeholder="Add keywords separated by commas (e.g., job, work, employment)"
                        on:keydown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                            keywords.forEach(keyword => addKeywordToEdit(keyword));
                            e.target.value = '';
                          }
                        }}
                        class="keyword-input"
                      />
                      <div class="keywords-list">
                        {#each editingTemplate.keywords || [] as keyword, index}
                          <span class="keyword-tag">
                            {keyword}
                            <button type="button" on:click={() => removeKeywordFromEdit(index)} class="remove-keyword">×</button>
                          </span>
                        {/each}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-actions">
                  <Button variant="primary" size="sm" on:click={saveEditedTemplate}>Save</Button>
                  <Button variant="outline" size="sm" on:click={cancelEditTemplate}>Cancel</Button>
                </div>
              </div>
            {:else}
              <!-- View Mode -->
              <div class="template-display">
                <div class="template-info">
                  <div class="template-header">
                    <span class="template-category">{template.category}</span>
                    <h5 class="template-title">{template.title}</h5>
                  </div>
                  <div class="template-text">{template.text}</div>
                </div>
                <div class="template-actions">
                  <Button variant="outline" size="sm" icon="fas fa-edit" on:click={() => startEditingTemplate(template)}>
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" icon="fas fa-trash" on:click={() => confirmDeleteTemplate(template)}>
                    Delete
                  </Button>
                </div>
              </div>
            {/if}
          </div>
        {/each}
          </div>
        </div>
      {/if}
      
      {#if activeTab === 'talking-points'}
        <!-- Talking Points Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Manage Talking Points</h2>
            <p>Create and manage talking points for phone calls and interactions</p>
          </div>
          <div class="section-action-header">
            <Button
              variant="primary"
              size="sm"
              icon="fas fa-plus"
              on:click={startAddingTalkingPoint}
            >
              Add Talking Point
            </Button>
          </div>
      
      <!-- Add New Talking Point Form -->
      {#if showAddTalkingPoint}
        <div class="add-form">
          <h4>Add New Talking Point</h4>
          <div class="form-grid">
            <FormGroup
              id="new-talking-point-category"
              label="Category"
              bind:value={newTalkingPoint.category}
              placeholder="e.g., Support Services"
              type="text"
            />
            <FormGroup
              id="new-talking-point-title"
              label="Title"
              bind:value={newTalkingPoint.title}
              placeholder="e.g., Offer employment support service"
              type="text"
            />
            <div class="full-width">
              <label>Content</label>
              <textarea
                bind:value={newTalkingPoint.content}
                placeholder="Enter the talking point content. This is what you'll refer to during phone calls."
                rows="3"
                class="template-textarea"
              ></textarea>
            </div>
            <div class="full-width">
              <label>Keywords (for relevance matching)</label>
              <div class="keywords-input">
                <input
                  type="text"
                  placeholder="Add keywords separated by commas (e.g., job, work, employment)"
                  on:keydown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                      keywords.forEach(keyword => addKeywordToNewTalkingPoint(keyword));
                      e.target.value = '';
                    }
                  }}
                  class="keyword-input"
                />
                <div class="keywords-list">
                  {#each newTalkingPoint.keywords as keyword, index}
                    <span class="keyword-tag">
                      {keyword}
                      <button type="button" on:click={() => removeKeywordFromNewTalkingPoint(index)} class="remove-keyword">×</button>
                    </span>
                  {/each}
                </div>
              </div>
            </div>
          </div>
          <div class="form-actions">
            <Button variant="primary" on:click={saveNewTalkingPoint}>Save Talking Point</Button>
            <Button variant="outline" on:click={cancelAddTalkingPoint}>Cancel</Button>
          </div>
        </div>
      {/if}
      
      <!-- Talking Points List -->
      <div class="templates-list">
        {#each $settings.talkingPoints as talkingPoint}
          <div class="template-item">
            {#if editingTalkingPoint && editingTalkingPoint.id === talkingPoint.id}
              <!-- Edit Mode -->
              <div class="template-edit">
                <div class="form-grid">
                  <FormGroup
                    id="edit-talking-point-category"
                    label="Category"
                    bind:value={editingTalkingPoint.category}
                    type="text"
                  />
                  <FormGroup
                    id="edit-talking-point-title"
                    label="Title"
                    bind:value={editingTalkingPoint.title}
                    type="text"
                  />
                  <div class="full-width">
                    <label>Content</label>
                    <textarea
                      bind:value={editingTalkingPoint.content}
                      rows="3"
                      class="template-textarea"
                    ></textarea>
                  </div>
                  <div class="full-width">
                    <label>Keywords (for relevance matching)</label>
                    <div class="keywords-input">
                      <input
                        type="text"
                        placeholder="Add keywords separated by commas (e.g., job, work, employment)"
                        on:keydown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                            keywords.forEach(keyword => addKeywordToEditTalkingPoint(keyword));
                            e.target.value = '';
                          }
                        }}
                        class="keyword-input"
                      />
                      <div class="keywords-list">
                        {#each editingTalkingPoint.keywords || [] as keyword, index}
                          <span class="keyword-tag">
                            {keyword}
                            <button type="button" on:click={() => removeKeywordFromEditTalkingPoint(index)} class="remove-keyword">×</button>
                          </span>
                        {/each}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-actions">
                  <Button variant="primary" size="sm" on:click={saveEditedTalkingPoint}>Save</Button>
                  <Button variant="outline" size="sm" on:click={cancelEditTalkingPoint}>Cancel</Button>
                </div>
              </div>
            {:else}
              <!-- View Mode -->
              <div class="template-display">
                <div class="template-info">
                  <div class="template-header">
                    <span class="template-category">{talkingPoint.category}</span>
                    <h5 class="template-title">{talkingPoint.title}</h5>
                  </div>
                  <div class="template-text">{talkingPoint.content}</div>
                </div>
                <div class="template-actions">
                  <Button variant="outline" size="sm" icon="fas fa-edit" on:click={() => startEditingTalkingPoint(talkingPoint)}>
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" icon="fas fa-trash" on:click={() => confirmDeleteTalkingPoint(talkingPoint)}>
                    Delete
                  </Button>
                </div>
              </div>
            {/if}
          </div>
        {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .settings-page {
    min-height: 100vh;
    /* background: #f8fafc; */
    /* padding: 2rem; */
  }

  .settings-header {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--gap);
  }

  .header-content {
    padding: calc(var(--cell-padding) * 4);
    display: flex;
    align-items: center;
    gap: calc(var(--gap) * 3.75);
  }

  .header-title h1 {
    margin: 0 0 var(--gap) 0;
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
  }

  .header-title p {
    margin: 0;
    color: #6b7280;
    font-size: var(--value-size);
  }

  .header-actions {
    margin-left: auto;
  }

  .settings-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .tab-navigation {
    padding: calc(var(--cell-padding) * 4) calc(var(--cell-padding) * 4) 0 calc(var(--cell-padding) * 4);
    background: white;
  }

  .tab-content {
    padding: calc(var(--cell-padding) * 4);
  }

  .settings-section {
    max-width: 800px;
  }


  .section-header {
    margin-bottom: calc(var(--gap) * 5);
  }

  .section-header h2 {
    margin: 0 0 var(--gap) 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
  }

  .section-header p {
    margin: 0;
    color: #6b7280;
    font-size: var(--label-size);
  }

  .section-action-header {
    margin-bottom: calc(var(--gap) * 3.75);
  }

  .add-form {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: calc(var(--cell-padding) * 3);
    margin-bottom: calc(var(--gap) * 5);
  }

  .add-form h4 {
    margin: 0 0 calc(var(--gap) * 2.5) 0;
    color: var(--label-color);
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(var(--gap) * 2.5);
    margin-bottom: calc(var(--gap) * 2.5);
  }

  .full-width {
    grid-column: 1 / -1;
  }

  .color-picker-group {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .color-picker-group label {
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--label-size);
  }

  .color-picker {
    display: flex;
    align-items: center;
    gap: var(--gap);
    flex-wrap: wrap;
  }

  .color-input {
    width: 40px;
    height: 40px;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    cursor: pointer;
  }

  .color-preview {
    width: 40px;
    height: 40px;
    border-radius: 0.375rem;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .predefined-colors {
    display: flex;
    gap: calc(var(--gap) * 0.625);
    flex-wrap: wrap;
  }

  .color-option {
    width: 24px;
    height: 24px;
    border-radius: 0.25rem;
    border: 2px solid white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .color-option:hover {
    transform: scale(1.1);
  }

  .form-actions {
    display: flex;
    gap: calc(var(--gap) * 1.875);
  }

  .tags-list, .templates-list {
    display: flex;
    flex-direction: column;
    gap: calc(var(--gap) * 2.5);
  }

  .tag-item, .template-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: calc(var(--cell-padding) * 2);
  }

  .tag-display, .template-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tag-info {
    display: flex;
    align-items: center;
    gap: calc(var(--gap) * 2.5);
  }

  .tag-chip {
    display: inline-flex;
    align-items: center;
    padding: calc(var(--cell-padding) * 0.5) calc(var(--cell-padding) * 1.5);
    border-radius: 1rem;
    color: white;
    font-size: var(--label-size);
    font-weight: 500;
  }

  .tag-description {
    color: #6b7280;
    font-size: var(--label-size);
  }

  .tag-actions, .template-actions {
    display: flex;
    gap: var(--gap);
  }

  .template-info {
    flex: 1;
  }

  .template-header {
    margin-bottom: var(--gap);
  }

  .template-category {
    display: inline-block;
    background: var(--color-primary-light);
    color: var(--color-primary);
    padding: calc(var(--cell-padding) * 0.5) var(--cell-padding);
    border-radius: 0.25rem;
    font-size: calc(var(--label-size) * 0.9375);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .template-title {
    margin: var(--gap) 0 0 0;
    color: var(--label-color);
    font-size: var(--value-size);
    font-weight: 600;
  }

  .template-text {
    color: #6b7280;
    font-size: var(--label-size);
    line-height: 1.5;
    max-width: 600px;
  }

  .template-textarea {
    width: 100%;
    padding: calc(var(--cell-padding) * 1.5);
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-family: inherit;
    font-size: var(--label-size);
    line-height: 1.5;
    resize: vertical;
    min-height: 100px;
  }

  .template-textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  /* Keywords Input Styles */
  .keywords-input {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .keyword-input {
    width: 100%;
    padding: var(--cell-padding);
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: var(--label-size);
    transition: border-color 0.15s;
  }

  .keyword-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--gap);
    min-height: 1.5rem;
  }

  .keyword-tag {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--gap) * 0.625);
    background: var(--color-primary-light);
    color: var(--color-primary);
    padding: calc(var(--cell-padding) * 0.5) var(--cell-padding);
    border-radius: 0.375rem;
    font-size: calc(var(--label-size) * 0.9375);
    font-weight: 500;
    border: 1px solid var(--color-primary);
  }

  .remove-keyword {
    background: none;
    border: none;
    color: var(--color-primary);
    cursor: pointer;
    font-size: var(--label-size);
    line-height: 1;
    padding: 0;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.15s;
  }

  .remove-keyword:hover {
    background: var(--color-primary);
    color: white;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .settings-page {
      padding: 1rem;
    }

    .header-content {
      padding: calc(var(--cell-padding) * 3);
      flex-direction: column;
      align-items: flex-start;
      gap: calc(var(--gap) * 2.5);
    }

    .header-actions {
      margin-left: 0;
    }

    .tab-content {
      padding: calc(var(--cell-padding) * 3);
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .tag-display, .template-display {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .tag-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
</style>