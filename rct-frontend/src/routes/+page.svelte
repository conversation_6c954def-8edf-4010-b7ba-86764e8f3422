<script lang="ts">
  import WebsiteHeader from '$lib/components/WebsiteHeader.svelte';
  import WebsiteFooter from '$lib/components/WebsiteFooter.svelte';
  import FAQItem from '$lib/components/FAQItem.svelte';
  import FeatureGrid from '$lib/components/FeatureGrid.svelte';
  import { onMount } from 'svelte';
  import { base } from '$app/paths';
  
  let heroVideo: HTMLVideoElement;
  
  let isAnnual = false;
  let teamSize = 10;
  
  // Features data
  const features = [
    {
      title: 'Instant Rent Calculations',
      description: 'Calculate outstanding rents, arrears, and payment scenarios with 13 built-in formulas. Support for weekly, monthly, and custom payment periods.',
      icon: 'fa-calculator',
      screenshot: '/screenshots/instant-rent-calculations.webp',
      screenshotFallback: '/screenshots/instant-rent-calculations.png'
    },
    {
      title: 'Payment Arrangement Planning',
      description: 'Create realistic payment plans with debt clearance projections. Visual charts help tenants understand their repayment journey.',
      icon: 'fa-calendar-alt',
      screenshot: '/screenshots/payment-arrangement.webp',
      screenshotFallback: '/screenshots/payment-arrangement.png'
    },
    {
      title: 'UC/HB Entitlement Analysis',
      description: 'Calculate Universal Credit and Housing Benefit entitlements with detailed service charge breakdowns and eligibility assessments.',
      icon: 'fa-money-bill-wave',
      screenshot: '/screenshots/uc-hb-entitlement.webp',
      screenshotFallback: '/screenshots/uc-hb-entitlement.png'
    },
    {
      title: 'Comprehensive Case Logging',
      description: 'Track all tenant interactions with timestamped notes. Separate inbound and outbound call management with searchable history.',
      icon: 'fa-clipboard-list',
      screenshot: '/screenshots/analytics-and-insights.webp',
      screenshotFallback: '/screenshots/analytics-and-insights.png'
    },
    {
      title: 'Real-time Data Sync',
      description: 'Automatic synchronization between rent calculations, payment arrangements, and account charges. No double data entry required.',
      icon: 'fa-chart-line',
      video: '/screenshots/real-time-data-sync.mp4',
      videoWebM: '/screenshots/real-time-data-sync.webm'
    },
    {
      title: 'Advanced Analytics & Insights',
      description: 'Transform your rent collection data into actionable insights with comprehensive performance metrics, agent productivity tracking, and automated reporting.',
      icon: 'fa-chart-bar',
      screenshot: '/screenshots/comprehensive-case-logging.webp',
      screenshotFallback: '/screenshots/comprehensive-case-logging.png'
    }
  ];
  
  // FAQ data
  const faqItems = [
    {
      question: "What is Rent Collection ToolKit and how does it help social landlords and housing associations?",
      answer: "Rent Collection ToolKit is an innovative software solution engineered specifically for social landlords and housing associations. It's designed to automate repetitive calculations, consolidate essential debt collection tools into a single platform, and provide insightful analytics. Our aim is to simplify complex debt management, improve recovery rates, and empower your team to focus on effective tenant engagement, rather than manual administration."
    },
    {
      question: "What's the main benefit of using Rent Collection ToolKit over traditional methods?",
      answer: "The primary benefit is transforming a manual, error-prone process into an efficient, data-driven operation. You'll no longer need to rely on desktop calculators and notepads for complex debt calculations. Our software automates these, reducing errors and freeing up valuable time for your collection agents. Additionally, our unique tracking and analytics tools provide management with crucial insights to optimize caseloads and strategies."
    },
    {
      question: "What types of housing debt can Rent Collection ToolKit manage?",
      answer: "Our software is built to manage all forms of housing-related debt common in social housing, including rent arrears, service charges, historical debts, utility charges, and other associated fees."
    },
    {
      question: "How does Rent Collection ToolKit make complex calculations easy?",
      answer: "We've designed Rent Collection ToolKit with an intuitive interface that automates repetitive and often challenging calculations. With minimal user input, the system instantly calculates outstanding balances, potential payment plans, and income projections. This eliminates the need for manual calculations, significantly reducing errors and freeing your agents to have more productive conversations."
    },
    {
      question: "What \"multiple tools\" are collated into one place within the software?",
      answer: "Rent Collection ToolKit brings together key functionalities that are often spread across different systems or manual processes. This includes:\n• Automated Arrears Calculators: For current and projected debt.\n• Payment Plan Modeller: To quickly generate and visualize repayment options during a tenant call.\n• Communication Templates: For semi-automated and personalized outreach.\n• Case Tracking & Workflow Management: To monitor individual tenant debt journeys.\n• Performance Analytics & Reporting: For management oversight and strategic decision-making."
    },
    {
      question: "Can collection agents easily use the software during a call with a tenant?",
      answer: "Absolutely. Our software is designed for ease of use. Collection agents can quickly input details and generate projections, helping them to collaboratively work out sustainable payment plans with tenants in real-time, directly during a phone call. This fosters better engagement and faster resolution."
    },
    {
      question: "What kind of unique visuals and projections does the software offer?",
      answer: "We provide clear, visual representations of debt trajectories and repayment schedules. This includes charts and graphs that illustrate how different payment plan scenarios impact a tenant's debt over time, making it easier for both agents and tenants to understand the path to financial stability."
    },
    {
      question: "What kind of analytics are available for management?",
      answer: "Our powerful analytics tools provide management with crucial insights into:\n• Caseload performance and agent productivity.\n• Overall debt recovery rates and trends.\n• Identification of recurring debt patterns.\n• Insights into tenant repayment behaviour.\nThese tools help you make data-driven decisions to optimize your debt collection processes."
    },
    {
      question: "Is Rent Collection ToolKit specifically designed for social landlords and housing associations?",
      answer: "Yes, our software has been developed by industry experts with a practical understanding of the unique challenges and regulatory environment faced by social landlords and housing associations in the UK. This ensures the features, terminology, and workflows are perfectly aligned with your operational needs."
    },
    {
      question: "Can local councils also benefit from Rent Collection ToolKit?",
      answer: "Absolutely. Our software's robust features for debt calculation, tenant communication, workflow management, and analytics are highly beneficial for local councils managing housing debt. It helps streamline processes and improve outcomes across diverse public housing portfolios."
    },
    {
      question: "How does Rent Collection ToolKit ensure compliance with UK GDPR, especially since no personal information is requested directly?",
      answer: "While our software does not request personal information directly from external users for calculation purposes (e.g., for a public calculator tool), it is designed to process and manage tenant data securely within the agency's environment. We ensure GDPR compliance through:\n• Secure Data Handling: All data processed within the software adheres to strict security protocols.\n• Access Controls: Role-based permissions to ensure only authorized agency staff can view and manage sensitive data.\n• Audit Trails: Comprehensive logging of all actions within the system for accountability and compliance auditing.\n• Data Minimisation & Purpose Limitation: Our tools are built to support your compliance by facilitating data use that is proportionate and for specified purposes."
    },
    {
      question: "What is your pricing model for Rent Collection ToolKit?",
      answer: "Our pricing is based on a per-user model, offering excellent value. We also provide competitive bulk buy discounts for agencies requiring licenses for multiple collection agents or teams. Please contact us for a tailored quote."
    },
    {
      question: "What kind of support is there for onboarding and ongoing use?",
      answer: "We believe in providing comprehensive support to ensure your success:\n• Dedicated Account Manager: You'll have a dedicated point of contact to assist with your strategic needs.\n• Thorough Onboarding: Our team provides in-depth training to get your staff fully proficient with the software.\n• Ongoing Support: Accessible support channels for any questions or issues that arise."
    },
    {
      question: "How do you ensure the software remains up-to-date to support the strategy suggestions and calculations?",
      answer: "As developers with industry expertise in social housing, staying current is fundamental to our product. Our team actively monitors changes in best practices and UK legislation, such as GDPR updates, evolving pre-action protocols, and guidance from housing regulators. We then proactively integrate these changes into the Rent Collection ToolKit through regular updates. This ensures your agency always operates with tools that are compliant and aligned with the latest industry best practices, without you needing to track every regulatory shift."
    },
    {
      question: "How does your pricing compare to competitors, and what makes your solution more cost-effective?",
      answer: "Rent Collection ToolKit offers a unique combination of ease of use, integrated tools, and advanced analytics, all at a highly competitive price point. Our cost-effectiveness stems from:\n• Designed by Industry Experts: Meaning less customization needed and faster time-to-value.\n• Efficiency Gains: Significant reductions in manual effort and errors lead to operational cost savings.\n• Improved Recovery Rates: Our intelligent tools help optimize collection strategies, boosting your income.\n• Consolidated Functionality: By offering multiple tools in one place, we potentially reduce the need for separate software licenses."
    }
  ];
  
  // Tiered pricing logic
  $: monthlyPrice = teamSize <= 5 ? 15 : teamSize <= 10 ? 12 : 10;
  $: annualPrice = Math.round(monthlyPrice * 0.8); // 20% discount for annual
  $: currentPrice = isAnnual ? annualPrice : monthlyPrice;
  $: savings = monthlyPrice - annualPrice;
  $: totalMonthlyPrice = teamSize * currentPrice;
  $: totalAnnualSavings = isAnnual ? teamSize * savings * 12 : 0;
  
  onMount(() => {
    // Immediate intersection observer setup for critical elements
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -10% 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);
    
    // Observe all sections and elements for scroll animation
    const elementsToAnimate = document.querySelectorAll('.animate-on-scroll');
    elementsToAnimate.forEach((el) => observer.observe(el));

    // Check if we're on mobile
    const isMobile = window.innerWidth <= 768;
    
    // Only load video on desktop
    if (heroVideo && !isMobile) {
      // Remove initial opacity style to ensure video is visible
      heroVideo.style.opacity = '0';
      
      // Load and play video
      heroVideo.load();
      
      // Handle video ready state
      heroVideo.addEventListener('canplay', () => {
        // Fade in the video
        heroVideo.style.opacity = '0.7';
        
        // Try to play
        heroVideo.play().catch((error) => {
          console.log('Video autoplay failed:', error);
          // Still show the video even if autoplay fails
          heroVideo.style.opacity = '0.7';
        });
      }, { once: true });
    }
    
    return () => {
      observer.disconnect();
    };
  });
</script>

<svelte:head>
  <title>Rent Collection Toolkit - Streamline Housing Management for UK Councils</title>
  <meta name="description" content="Professional rent collection software designed for UK councils, local authorities and housing associations. Streamline calculations, arrangements and case management.">
  <meta name="keywords" content="rent collection software, housing management, UK councils, housing associations, rent calculations, payment arrangements, case management, GDPR compliant, housing providers">
  <meta name="author" content="Rent Collection Toolkit">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="https://rentcollectiontoolkit.com/">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://rentcollectiontoolkit.com/">
  <meta property="og:title" content="Rent Collection Toolkit - Streamline Housing Management for UK Councils">
  <meta property="og:description" content="Professional rent collection software designed for UK councils, local authorities and housing associations. Streamline calculations, arrangements and case management.">
  <meta property="og:image" content="https://rentcollectiontoolkit.com/Rent-Collection-Toolkit-Logo.svg">
  <meta property="og:site_name" content="Rent Collection Toolkit">
  <meta property="og:locale" content="en_GB">
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://rentcollectiontoolkit.com/">
  <meta property="twitter:title" content="Rent Collection Toolkit - Streamline Housing Management for UK Councils">
  <meta property="twitter:description" content="Professional rent collection software designed for UK councils, local authorities and housing associations. Streamline calculations, arrangements and case management.">
  <meta property="twitter:image" content="https://rentcollectiontoolkit.com/Rent-Collection-Toolkit-Logo.svg">
  
  <!-- Video SEO Meta Tags -->
  <meta property="og:video" content="/rent-collection-toolkit-video.mp4">
  <meta property="og:video:type" content="video/mp4">
  <meta property="og:video:width" content="1920">
  <meta property="og:video:height" content="1080">
  <meta name="twitter:player" content="/rent-collection-toolkit-video.mp4">
  <meta name="twitter:player:width" content="1920">
  <meta name="twitter:player:height" content="1080">
  
  <!-- Schema.org Structured Data -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Organization",
          "@id": "https://rentcollectiontoolkit.com/#organization",
          "name": "Rent Collection Toolkit",
          "url": "https://rentcollectiontoolkit.com/",
          "logo": {
            "@type": "ImageObject",
            "url": "https://rentcollectiontoolkit.com/Rent-Collection-Toolkit-Logo.svg"
          },
          "description": "Professional rent collection software designed for UK councils, local authorities and housing associations.",
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "GB"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            // "telephone": "0800 123 4567",
            // "email": "<EMAIL>",
            "contactType": "customer service"
          }
        },
        {
          "@type": "WebSite",
          "@id": "https://rentcollectiontoolkit.com/#website",
          "url": "https://rentcollectiontoolkit.com/",
          "name": "Rent Collection Toolkit",
          "description": "Professional rent collection software designed for UK councils, local authorities and housing associations.",
          "publisher": {
            "@id": "https://rentcollectiontoolkit.com/#organization"
          },
          "inLanguage": "en-GB"
        },
        {
          "@type": "SoftwareApplication",
          "@id": "https://rentcollectiontoolkit.com/#software",
          "name": "Rent Collection Toolkit",
          "description": "Professional rent collection software designed for UK councils, local authorities and housing associations. Streamline calculations, arrangements and case management.",
          "url": "https://rentcollectiontoolkit.com/",
          "applicationCategory": "BusinessApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "10-15",
            "priceCurrency": "GBP",
            "priceSpecification": [
              {
                "@type": "UnitPriceSpecification",
                "price": "15",
                "priceCurrency": "GBP",
                "billingDuration": "P1M",
                "unitText": "per user per month (1-5 users)"
              },
              {
                "@type": "UnitPriceSpecification",
                "price": "12",
                "priceCurrency": "GBP",
                "billingDuration": "P1M",
                "unitText": "per user per month (6-10 users)"
              },
              {
                "@type": "UnitPriceSpecification",
                "price": "10",
                "priceCurrency": "GBP",
                "billingDuration": "P1M",
                "unitText": "per user per month (10+ users)"
              }
            ]
          },
          "provider": {
            "@id": "https://rentcollectiontoolkit.com/#organization"
          },
          "featureList": [
            "Instant rent calculations",
            "Payment arrangement planning", 
            "UC/HB entitlement analysis",
            "Comprehensive case logging",
            "Dual instance system",
            "Real-time data sync",
            "Advanced analytics"
          ],
          "audience": {
            "@type": "Audience",
            "audienceType": "UK councils, local authorities, housing associations"
          }
        },
        {
          "@type": "VideoObject",
          "name": "Rent Collection Toolkit - Housing Management Software Demo",
          "description": "See how UK housing providers use Rent Collection Toolkit to streamline rent calculations, payment arrangements, and case management for efficient housing operations.",
          "thumbnailUrl": "https://rentcollectiontoolkit.com/Rent-Collection-Toolkit-Logo.svg",
          "contentUrl": "https://rentcollectiontoolkit.com/rent-collection-toolkit-video.mp4",
          "embedUrl": "https://rentcollectiontoolkit.com/rent-collection-toolkit-video.mp4",
          "uploadDate": "2024-01-01",
          "duration": "PT30S",
          "publisher": {
            "@id": "https://rentcollectiontoolkit.com/#organization"
          }
        }
      ]
    }
  </script>
</svelte:head>

<WebsiteHeader />

<main class="website-main">
  <!-- Hero Section -->
  <section class="hero">
    <!-- Optimized Background Image for fast LCP -->
    <div class="hero-background">
      <!-- Hero background will be handled by CSS gradient -->
    </div>
    
    <!-- Optimized video for better LCP - load sources directly for faster loading -->
    <video 
      class="hero-video desktop-only"
      muted 
      loop 
      playsinline
      preload="auto"
      style="transition: opacity 0.3s ease-in-out;"
      aria-label="Background video showing housing management professionals using digital tools"
      bind:this={heroVideo}
    >
      <source src="/rent-collection-toolkit-video.webm" type="video/webm">
      <source src="/rent-collection-toolkit-video.mp4" type="video/mp4">
    </video>
    
    <div class="hero-container">
      <div class="hero-content">
        <h1>Streamline Rent Arrears Recovery</h1>
        <div class="hero-features">
          <div class="hero-feature">
            <i class="fas fa-clock hero-icon"></i>
            <p>Say goodbye to tedious tasks</p>
          </div>
          <div class="hero-feature">
            <i class="fas fa-phone hero-icon"></i>
            <p>Stop using calculators and notepads with tenants on the phone</p>
          </div>
          <div class="hero-feature">
            <i class="fas fa-shield-alt hero-icon"></i>
            <p>Eliminate simple mistakes</p>
          </div>
        </div>
        <h2>Rent Collection Toolkit will streamline your workflow by bringing essential tools together into one intuitive app.</h2>
        <div class="hero-buttons">
          <a href="/app?mode=register" class="btn-primary-large">Start Free Trial</a>
          <a href="#features" class="btn-secondary-large">View Features</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="features">
    <div class="container">
      <div class="section-header">
        <h2>Comprehensive Rent Management Tools</h2>
        <p>
          The toolkit is designed for use alongside your Housing CRM, to assist with rent collection and arrangement planning. Rent Collection Toolkit can be used by any UK social housing provider. It's been designed by Income Officers, for Income Officers.
          <br/><br/>With minimal input, the ToolKit provides a comprehensive array of calculations and forecasts, far beyond your typical CRM system. Empower your income team with fast, accurate rent calculations, arrangement planning tools and case management.
        </p>
      </div>
      
      <FeatureGrid {features} layout="column-pairs" cardStyle="detailed" />
    </div>
  </section>


  <!-- Benefits Section -->
  <section id="benefits" class="benefits">
    <div class="container">
      <div class="section-header">
        <h2>Why Choose Rent Collection Toolkit?</h2>
        <p>Built specifically for UK housing providers by housing professionals who understand your challenges.</p>
      </div>
      
      <div class="benefits-content">
        <div class="benefits-list">
          
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="benefit-text">
              <h3>Improve Tenant Relations</h3>
              <p>Clear visual payment plans and transparent calculations help build trust and improve payment compliance.</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="benefit-text">
              <h3>Works Anywhere</h3>
              <p>Responsive web application works on any device. No installation required - just log in and start working.</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="benefit-text">
              <h3>Quick Setup & Training</h3>
              <p>Get your team up and running in minutes with our intuitive interface and comprehensive onboarding support.</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-building"></i>
            </div>
            <div class="benefit-text">
              <h3>Multi-Organisation Support</h3>
              <p>Manage multiple housing associations or council departments with separate data and user permissions.</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <div class="benefit-text">
              <h3>Regular Updates & Features</h3>
              <p>Continuous improvements and new features based on user feedback, ensuring you always have the latest tools.</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-handshake"></i>
            </div>
            <div class="benefit-text">
              <h3>Dedicated Account Management</h3>
              <p>Every client gets a dedicated account manager for personalised support, training, and ongoing success partnership.</p>
            </div>
          </div>
          
        </div>
        
   
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section id="pricing" class="pricing">
    <div class="container">
      <div class="section-header">
        <h2>Simple, Transparent Pricing</h2>
        <p>One comprehensive package that scales with your team. No hidden fees, no long-term contracts.</p>
      </div>
      
      <!-- Billing Toggle -->
      <div class="billing-toggle">
        <span class="toggle-label" class:active={!isAnnual}>Monthly</span>
        <label class="toggle-switch">
          <input type="checkbox" bind:checked={isAnnual} aria-label="Toggle between monthly and annual pricing">
          <span class="toggle-slider"></span>
        </label>
        <span class="toggle-label" class:active={isAnnual}>
          Annual
          <span class="savings-badge">Save 20%</span>
        </span>
      </div>
      
      <!-- Pricing Content Grid -->
      <div class="pricing-content-grid">
        <!-- Single Pricing Card -->
        <div class="pricing-single">
          <div class="pricing-card-large">
            <div class="pricing-header">
              <h3>Rent Collection Toolkit</h3>
              <div class="price-display">
                <div class="price">
                  <span class="currency">£</span>
                  <span class="amount">{currentPrice}</span>
                  <span class="period">per user / {isAnnual ? 'month (billed annually)' : 'month'}</span>
                </div>
                {#if isAnnual}
                  <div class="savings-text">
                    You save £{savings} per user per month
                  </div>
                {/if}
              </div>
            </div>
            
            <div class="pricing-features-large">
              <h3>Everything you need included:</h3>
              <div class="features-columns">
                <ul>
                  <li><i class="fas fa-check"></i> Instant rent calculations</li>
                  <li><i class="fas fa-check"></i> Payment arrangement planning</li>
                  <li><i class="fas fa-check"></i> UC/HB entitlement analysis</li>
                  <li><i class="fas fa-check"></i> Comprehensive case logging</li>
                  <li><i class="fas fa-check"></i> Dual instance system</li>
                  <li><i class="fas fa-check"></i> Real-time data sync</li>
                </ul>
                <ul>
                  <li><i class="fas fa-check"></i> Unlimited properties</li>
                  <li><i class="fas fa-check"></i> Advanced reporting</li>
                  <li><i class="fas fa-check"></i> Priority email support</li>
                  <li><i class="fas fa-check"></i> Regular updates</li>
                  <li><i class="fas fa-check"></i> GDPR compliant</li>
                  <li><i class="fas fa-check"></i> 99.9% uptime SLA</li>
                </ul>
              </div>
            </div>
            
            <div class="pricing-cta">
              <a href="/app?mode=register" class="pricing-button-large">Start 14-Day Free Trial</a>
              <p class="pricing-note">No credit card required. Cancel anytime.</p>
            </div>
          </div>
        </div>
        
        <!-- Team Size Calculator -->
        <div class="team-calculator">
          <h3>Calculate pricing for your team:</h3>
          
          <!-- Pricing Tiers Info -->
          <div class="pricing-tiers-info">
            <div class="tier-item {teamSize <= 5 ? 'active' : ''}">
              <span class="tier-users">1-5 users</span>
              <span class="tier-price">£15/user</span>
            </div>
            <div class="tier-item {teamSize >= 6 && teamSize <= 10 ? 'active' : ''}">
              <span class="tier-users">6-10 users</span>
              <span class="tier-price">£12/user</span>
            </div>
            <div class="tier-item {teamSize > 10 ? 'active' : ''}">
              <span class="tier-users">10+ users</span>
              <span class="tier-price">£10/user</span>
            </div>
          </div>
          
          <div class="calculator-card">
            <div class="team-size-input">
              <label for="teamSize" class="team-label">Number of users:</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  id="teamSize" 
                  class="team-slider" 
                  min="1" 
                  max="100" 
                  bind:value={teamSize}
                >
                <div class="slider-value">{teamSize} {teamSize === 1 ? 'user' : 'users'}</div>
              </div>
            </div>
            
            <div class="calculator-results">
              <div class="result-row">
                <span class="result-label">Monthly cost:</span>
                <span class="result-value">£{totalMonthlyPrice.toLocaleString()}</span>
              </div>
              {#if isAnnual}
                <div class="result-row savings-row">
                  <span class="result-label">Annual savings:</span>
                  <span class="result-value savings">£{totalAnnualSavings.toLocaleString()}</span>
                </div>
              {/if}
              <div class="result-row total-row">
                <span class="result-label">{isAnnual ? 'Annual' : 'Monthly'} total:</span>
                <span class="result-value total">£{isAnnual ? (totalMonthlyPrice * 12).toLocaleString() : totalMonthlyPrice.toLocaleString()}</span>
              </div>
            </div>
            
            {#if teamSize >= 50}
              <div class="volume-discount">
                <i class="fas fa-info-circle"></i>
                <span>Contact us for volume discounts on teams over 50 users</span>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section id="faq" class="faq-section">
    <div class="container">
      <div class="section-header">
        <h2>Frequently Asked Questions</h2>
        <p>Get answers to common questions about Rent Collection Toolkit and how it can transform your housing management operations.</p>
      </div>
      
      <div class="faq-container">
        {#each faqItems as faq, index}
          <FAQItem
            question={faq.question}
            answer={faq.answer}
            isOpen={false}
          />
        {/each}
      </div>
    </div>
  </section>

</main>

<WebsiteFooter />

<style>
  /* Global H1 styling to prevent deprecated API warning */
  h1 {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
  }

  /* Explicit font-size for H1 within semantic elements to prevent deprecation warning */
  section h1, article h1, aside h1, nav h1 {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
  }

  /* Hero Section */
  .hero {
    position: relative;
    color: white;
    padding: 4rem 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    /* Fast-loading gradient background for immediate LCP */
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    
    .hero-logo {
      margin-bottom: 3rem;
      width: 300px;
    }
  }
  
  /* Mobile hero background with placeholder image */
  @media (max-width: 768px) {
    .hero {
      background: linear-gradient(135deg, rgba(2, 85, 130, 0.85) 0%, rgba(77, 161, 169, 0.85) 100%),
                  url('/rent-collection-toolkit-phone.webp') center/cover no-repeat;
    }
  }

  /* Video Background - loads after initial paint */
  .hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }
  
  /* Hide video on mobile */
  @media (max-width: 768px) {
    .hero-video.desktop-only {
      display: none;
    }
  }

  /* Simplified background for better LCP - removed complex radial gradients */
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(2, 85, 130, 0.5) 0%, rgba(77, 161, 169, 0.5) 100%);
    z-index: 2;
    pointer-events: none; /* Allow clicks to pass through */
  }

  .hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 3fr .5fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 3;
  }

  .hero h1 {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
  }

  .hero h2 {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 2rem;
    line-height: 160%;
  }

  .hero-subtitle {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
  }

  .btn-primary-large, .btn-secondary-large {
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.2s ease;
    display: inline-block;
  }

  .btn-primary-large {
    background: white;
    color: var(--color-primary);
  }

  .btn-primary-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }

  .btn-secondary-large {
    background: transparent;
    color: white;
    border: 2px solid white;
  }

  .btn-secondary-large:hover {
    background: white;
    color: var(--color-primary);
  }

  .hero-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin: 2rem 0;
  }

  .hero-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .hero-icon {
    width: 24px;
    height: 24px;
    color: white;
    flex-shrink: 0;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero-feature p {
    margin: 0;
    font-size: 1.125rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
  }

  .hero-stats {
    display: flex;
    gap: 2rem;
  }

  .stat {
    text-align: center;
  }

  .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
  }

  .stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
  }


  /* Sections */
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 1rem;
    }
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }

  .section-header p {
    font-size: 1.1rem;
    color: #4b5563;
    max-width: 800px;
    margin: 0 auto;
    margin-bottom: 4rem;
  }

  /* Features */
  .features {
    padding: 6rem 0;
    background: #f9fafb;
  }
  
  @media (max-width: 768px) {
    .features {
      padding: 3rem 0;
    }
    
    .features .section-header h2 {
      font-size: 1.75rem;
      margin-bottom: 1rem;
    }
    
    .features .section-header p {
      font-size: 1rem;
      margin-bottom: 2rem;
    }
  }


  /* Analytics Feature Section */
  .analytics-feature {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  }

  .analytics-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
  }

  .feature-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
  }

  .analytics-feature h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .analytics-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2.5rem;
    line-height: 1.6;
  }

  .analytics-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2.5rem;
  }

  .analytics-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
  }

  .analytics-item i {
    width: 40px;
    height: 40px;
    background: var(--color-primary);
    color: white;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 0.25rem;
  }

  .analytics-item h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
  }

  .analytics-item p {
    color: #6b7280;
    line-height: 1.5;
    margin: 0;
  }

  .analytics-cta {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-analytics {
    display: inline-block;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(2, 85, 130, 0.3);
  }

  .btn-analytics:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(2, 85, 130, 0.4);
  }

  .analytics-note {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
  }

  /* Analytics Dashboard Visual */
  .analytics-dashboard {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border: 1px solid #e5e7eb;
  }

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
  }

  .dashboard-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--color-primary);
  }

  .dashboard-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #10b981;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .dashboard-status i {
    font-size: 0.5rem;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .dashboard-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
  }

  .stat-icon.green { background: #10b981; }
  .stat-icon.blue { background: #3b82f6; }
  .stat-icon.orange { background: #f59e0b; }

  .stat-content {
    display: flex;
    flex-direction: column;
  }

  .stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
  }

  .stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }

  .dashboard-chart {
    background: #f9fafb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
  }

  .chart-header {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }

  .chart-bars {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 80px;
  }

  .chart-bar {
    flex: 1;
    background: var(--color-primary);
    border-radius: 4px 4px 0 0;
    opacity: 0.6;
    transition: all 0.3s ease;
    min-height: 20px;
  }

  .chart-bar.active {
    opacity: 1;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  }

  .chart-bar:hover {
    opacity: 0.8;
    transform: scaleY(1.05);
  }

  /* Benefits */
  .benefits {
    padding: 6rem 0;
  }

  .benefits-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4rem;
    align-items: center;
  }

  .benefits-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .benefit-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .benefit-icon {
    width: 50px;
    height: 50px;
    background: var(--color-primary);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
  }

  .benefit-text h3 {
    font-size: 1.2rem;
    margin: 0 0 0.5rem 0;
  }

  .benefit-stats {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    color: white;
    padding: 2rem;
    border-radius: 1rem;
  }

  .stat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .stat-grid-expanded {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .stat-item {
    text-align: center;
  }

  .stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
  }

  .stat-desc {
    font-size: 0.875rem;
    opacity: 0.9;
  }

  /* Pricing */
  .pricing {
    padding: 6rem 0;
    background: #f9fafb;
  }

  /* Billing Toggle */
  .billing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
  }

  .toggle-label {
    font-weight: 500;
    color: #4b5563;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .toggle-label.active {
    color: var(--color-primary);
    font-weight: 600;
  }

  .savings-badge {
    background: linear-gradient(135deg, #10b981, var(--color-green));
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
  }

  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 34px;
  }

  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
  }

  input:checked + .toggle-slider {
    background-color: var(--color-primary);
  }

  input:checked + .toggle-slider:before {
    transform: translateX(24px);
  }

  /* Pricing Content Grid */
  .pricing-content-grid {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 3rem;
    align-items: start;
    margin-bottom: 3rem;
    align-items: center;
  }

  /* Single Pricing Card */
  .pricing-single {
    display: flex;
    justify-content: center;
  }

  .pricing-card-large {
    background: white;
    border-radius: 1.5rem;
    padding: 3rem;
    width: 100%;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border: 2px solid var(--color-primary);
    position: relative;
  }

  .pricing-header {
    text-align: center;
    margin-bottom: 2.5rem;
  }

  .pricing-header h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--color-primary);
  }

  .price-display {
    text-align: center;
  }

  .price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
  }

  .currency {
    font-size: 2rem;
    font-weight: 600;
    color: var(--color-primary);
  }

  .amount {
    font-size: 4rem;
    font-weight: 700;
    color: var(--color-primary);
  }

  .period {
    color: #4b5563;
    font-size: 1.1rem;
  }

  .savings-text {
    color: #10b981;
    font-weight: 600;
    font-size: 1.1rem;
  }

  .pricing-features-large {
    margin-bottom: 2.5rem;
  }

  .pricing-features-large h3 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.25rem;
    color: var(--color-primary);
  }

  .features-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .features-columns ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .features-columns li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
  }

  .features-columns i {
    color: var(--color-primary);
    font-size: 1.1rem;
  }

  .pricing-cta {
    text-align: center;
  }

  .pricing-button-large {
    display: inline-block;
    background: var(--color-primary);
    color: white;
    padding: 1.25rem 3rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
  }

  .pricing-button-large:hover {
    background: var(--color-secondary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(2, 85, 130, 0.3);
  }

  .pricing-note {
    color: #4b5563;
    font-size: 0.875rem;
    margin: 0;
  }

  /* Team Calculator */
  .team-calculator {
    text-align: center;
  }

  .team-calculator h3 {
    margin-bottom: 2rem;
    color: var(--color-primary);
    font-size: 1.25rem;
  }

  /* Pricing Tiers Info */
  .pricing-tiers-info {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .tier-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background: #f3f4f6;
    border-radius: 0.5rem;
    border: 2px solid transparent;
    transition: all 0.2s ease;
  }

  .tier-item.active {
    background: var(--color-primary-light);
    border-color: var(--color-primary);
    transform: scale(1.05);
  }

  .tier-users {
    font-size: 0.875rem;
    color: #4b5563;
    margin-bottom: 0.25rem;
  }

  .tier-item.active .tier-users {
    color: var(--color-primary);
    font-weight: 600;
  }

  .tier-price {
    font-size: 1rem;
    font-weight: 700;
    color: var(--color-primary);
  }

  .calculator-card {
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    width: 100%;
    border: 1px solid var(--border);
  }

  .team-size-input {
    margin-bottom: 2rem;
  }

  .team-label {
    display: block;
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .slider-container {
    position: relative;
  }

  .team-slider {
    width: 100%;
    height: 4px;
    border-radius: 50px;
    background: var(--border);
    outline: none;
    appearance: none;
    margin-bottom: 1rem;
    padding: 0.2rem 0;
  }

  .team-slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
  }

  .team-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(2, 85, 130, 0.3);
  }

  .team-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }

  .slider-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }

  .calculator-results {
    border-top: 2px solid #f3f4f6;
    padding-top: 1.5rem;
  }

  .result-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f9fafb;
  }

  .result-row.total-row {
    border-bottom: none;
    border-top: 2px solid var(--color-primary);
    padding-top: 1rem;
    margin-top: 0.5rem;
  }

  .result-row.savings-row {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    margin: 0 -1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border-bottom: none;
  }

  .result-label {
    font-weight: 500;
    color: #4b5563;
  }

  .result-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #111827;
  }

  .result-value.savings {
    color: #10b981;
  }

  .result-value.total {
    font-size: 1.25rem;
    color: var(--color-primary);
  }

  .volume-discount {
    background: var(--color-primary-light);
    /* border: 1px solid var(--color-primary); */
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-primary);
    font-weight: 500;
    font-size: 0.875rem;
  }

  /* Contact */
  .contact {
    padding: 6rem 0;
    background: var(--color-primary);
    color: white;
  }

  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }

  .contact h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .contact-methods {
    margin-top: 2rem;
  }

  .contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem 2rem;
    background: rgba(255,255,255,0.1);
    border-radius: 0.5rem;
  }

  .contact-method i {
    font-size: 1.5rem;
    width: 40px;
  }

  .contact-method h4 {
    margin: 0 0 0.25rem 0;
  }

  .contact-method p {
    margin: 0;
    font-weight: 600;
  }

  .contact-method span {
    font-size: 0.875rem;
    opacity: 0.8;
  }

  .contact-form {
    background: white;
    color: var(--color-primary);
    padding: 2rem;
    border-radius: 1rem;
  }

  .contact-form h3 {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .demo-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    font-weight: 500;
    color: var(--color-primary);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .form-group input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border);
    border-radius: 8px;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #111827;
    background-color: #ffffff;
    transition: all 0.2s ease;
    box-sizing: border-box;
  }

  .form-group input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .form-group input::placeholder {
    /* color: #9ca3af; */
    color: black;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .form-submit {
    width: 100%;
    background: var(--color-primary);
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
  }

  .form-submit:hover {
    background: var(--color-secondary);
  }

  .form-note {
    text-align: center;
    font-size: 0.875rem;
    opacity: 0.7;
    margin: 0;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {

    .pricing, 
    .benefits,
    .analytics,
    .contact {
      padding: 3rem 0;
    }
    .hero {
      min-height: 70vh;
      padding: 3rem 0;
    }
    
    .hero-video {
      /* Reduce video quality on mobile for performance */
      transform: scale(1.1);
    }
    
    .hero-container {
      grid-template-columns: 1fr;
      text-align: center;
    }
    
    h1,
    .hero h1 {
      font-size: 2.5rem;
    }
    
    .hero-buttons {
      justify-content: center;
    }
    
    .benefits-content,
    .contact-content {
      grid-template-columns: 1fr;
    }
    
    .pricing-content-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .pricing-card-large {
      padding: 2rem;
      margin: 0 1rem;
    }
    
    .features-columns {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .amount {
      font-size: 3rem;
    }
    
    .billing-toggle {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }
    
    .calculator-card {
      padding: 1.5rem;
      margin: 0 0rem;
    }
    
    .slider-value {
      font-size: 1.25rem;
    }
    
    .form-row {
      grid-template-columns: 1fr;
    }
    
    /* Analytics responsive */
    .analytics-content {
      grid-template-columns: 1fr;
      gap: 3rem;
    }
    
    .analytics-feature h2 {
      font-size: 2rem;
    }
    
    .analytics-dashboard {
      margin: 0 1rem;
      padding: 1.5rem;
    }
    
    .dashboard-stats {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }
    
    .stat-card {
      padding: 0.75rem;
    }
    
    .chart-bars {
      height: 60px;
    }
    
    /* Pricing tiers mobile */
    .pricing-tiers-info {
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .tier-item {
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      padding: 0.75rem 1rem;
    }
    
    /* FAQ Section mobile */
    .faq-container {
      max-width: 800px;
      margin: 0 auto;
    }
  }
  
  /* FAQ Section */
  .faq-section {
    padding: 6rem 0;
    background: white;
  }
  
  .faq-container {
    max-width: 900px;
    margin: 0 auto;
  }
  
  .faq-section .section-header {
    margin-bottom: 3rem;
  }
  
  .faq-section .section-header h2 {
    color: var(--color-primary);
  }
  
  @media (max-width: 768px) {
    .faq-section {
      padding: 4rem 0;
    }
  }
</style>
