<script lang="ts">
  import '../app.css';
  import { page } from '$app/stores';
  
  // Check if we're in the app route
  $: isAppRoute = $page.route.id?.startsWith('/app')
</script>

{#if isAppRoute}
  <!-- App routes use their own layout -->
  <slot />
{:else}
  <!-- Website routes get minimal layout -->
  <slot />
{/if}

<style lang="less">
  :global {
    :root {
      --color-primary: #025582;
      --color-primary-light: color-mix(in srgb, var(--color-primary) 10%, white 90%);

      --color-secondary: rgb(0, 51, 68);
      --color-secondary-light: color-mix(in srgb, var(--color-secondary) 10%, white 90%);
      --color-white: #ffffff;
      --color-black: #071821;
      --color-red: #dc2626;
      --color-red-light: color-mix(in srgb, var(--color-red) 10%, white 90%);
      --color-orange: #ea580c;
      --color-orange-light: color-mix(in srgb, var(--color-orange) 10%, white 90%);
      --color-yellow: #ca8a04;
      --color-yellow-light: color-mix(in srgb, var(--color-yellow) 10%, white 90%);
      --color-green: #059669;
      --color-green-light: color-mix(in srgb, var(--color-green) 10%, white 90%);

      --color-grey: #6b7280;

      --bg: #0255822d;
      --color-instance-inbound: #DB2955;
      --color-instance-outbound: #4DA1A9;
      --color-instance-inbound-light: #db295530;
      --color-instance-outbound-light: #4da1a930;
      --border: #c2c7d1;

      --thick-border-width: 3px;

      --label-weight: 400;
      --label-color: #374151;
      --label-size: 0.8rem;

      --value-size: 0.9rem;

      --button-size: 0.9rem;

      --cell-padding: .275rem;

      --subheader-size: .9rem;

      --section-header-size: 0.825rem;
      --gap: 0.5rem;


    }
    html {
      background-color: var(--bg);
    }
    .output-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--gap);
      .output-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;
        padding: 0.75rem;
        background-color: #f9fafb;
        border-radius: 0.375rem;
        border: 3px solid #d1d5db;
        label {
          font-weight: 500;
          color: #374151;
          font-size: 0.875rem;
        }
        .output-value {
          font-family: 'Martian Mono', 'Courier New', monospace;
          font-weight: 600;
          color: #111827;
        }
      }
    }

    @media (max-width: 768px) {
      .output-grid {
        grid-template-columns: 1fr 1fr;
        .sync-item,
        .output-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;
        }
      }

      .tab-buttons {
        gap: 0.25rem;
      }

    }

  }


</style> 