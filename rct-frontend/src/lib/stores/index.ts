import { writable, derived, get } from 'svelte/store';

// Types for our application state
export interface TabInstance {
  id: string;
  name: string;
  tags: string[];
  createdAt: Date;
  lastModified: Date;
  data: InstanceData;
}

export enum UserType {
  Superuser = 1,
  Admin = 2,
  TeamMember = 3
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  organisationId: string;
  organisationName: string;
  role: string;
  userType: UserType;
  jobTitle?: string;
  department?: string;
}

export interface Tenant {
  id: string;
  reference: string;
  organisationId: string;
  createdBy: string;
  createdAt: Date;
}

export interface RentCalculatorData {
  reference: string;
  name?: string;
  currentBalance: number;
  weeklyRent: number;
  tenantMonthlyPayment: number;
  tenantMonthlyPaymentFormula?: string; // Store formula string separately
  apaHbMonthlyPayment: number;
  tpdMonthlyPayment: number;
  tenantWeeklyPayment: number;
  benefitsHbWeeklyPayment: number;
}

export interface ArrangementPlannerData {
  weeksToNextPay: number;
  paymentDue: number;
  timeframe: number;
}

export interface AccountChargesData {
  grossWeeklyRent: number;
  rentComponent: number;
  nonUcServiceChargeFormula?: string;
  nonUcServiceChargeTotal?: number;
  // Legacy fields for backward compatibility
  nonUcServiceCharge1?: number;
  nonUcServiceCharge2?: number;
  nonUcServiceCharge3?: number;
  grossWeeklyRentOverridden?: boolean;
}

export interface InstanceData {
  rentCalculator: RentCalculatorData;
  arrangementPlanner: ArrangementPlannerData;
  accountCharges: AccountChargesData;
  notes: string;
}


export interface Action {
  code: string;
  title: string;
  description: string;
  responseDescription: string; // Text to use in quick responses/notes
  priority: 'urgent' | 'high' | 'medium' | 'low';
  triggered: string;
  order: number;
}

// Case Detail Tab interfaces
export interface CaseDetailTab {
  id: string;
  type: 'case-detail';
  caseId: string;
  tenantReference: string;
  name: string;
  createdAt: Date;
  lastModified: Date;
}

// Settings Tab interface
export interface SettingsTab {
  id: string;
  type: 'settings';
  name: string;
  createdAt: Date;
  lastModified: Date;
}

// Profile Tab interface
export interface ProfileTab {
  id: string;
  type: 'profile';
  name: string;
  createdAt: Date;
  lastModified: Date;
}

// Analytics Tab interface
export interface AnalyticsTab {
  id: string;
  type: 'analytics';
  name: string;
  createdAt: Date;
  lastModified: Date;
}

// Union type for all tab types
export type AppTab = TabInstance | CaseDetailTab | SettingsTab | ProfileTab | AnalyticsTab;

// Default data structures
const defaultRentCalculatorData: RentCalculatorData = {
  reference: '',
  currentBalance: 0,
  weeklyRent: 0,
  tenantMonthlyPayment: 0,
  apaHbMonthlyPayment: 0,
  tpdMonthlyPayment: 0,
  tenantWeeklyPayment: 0,
  benefitsHbWeeklyPayment: 0
};

const defaultArrangementPlannerData: ArrangementPlannerData = {
  weeksToNextPay: 0,
  paymentDue: 0,
  timeframe: 0
};

const defaultAccountChargesData: AccountChargesData = {
  grossWeeklyRent: 0,
  rentComponent: 0,
  nonUcServiceChargeFormula: '',
  nonUcServiceChargeTotal: 0,
  grossWeeklyRentOverridden: false
};

// Helper function to create fresh default instance data (deep clone)
function createDefaultInstanceData(): InstanceData {
  return {
    rentCalculator: {
      reference: '',
      currentBalance: 0,
      weeklyRent: 0,
      tenantMonthlyPayment: 0,
      apaHbMonthlyPayment: 0,
      tpdMonthlyPayment: 0,
      tenantWeeklyPayment: 0,
      benefitsHbWeeklyPayment: 0
    },
    arrangementPlanner: {
      weeksToNextPay: 0,
      paymentDue: 0,
      timeframe: 0
    },
    accountCharges: {
      grossWeeklyRent: 0,
      rentComponent: 0,
      nonUcServiceChargeFormula: '',
      nonUcServiceChargeTotal: 0,
      grossWeeklyRentOverridden: false
    },
    notes: ''
  };
}

// Browser check for SSR compatibility
const isBrowser = typeof window !== 'undefined';

// Helper function to get stored global case counter
function getStoredGlobalCaseCounter(): number {
  if (!isBrowser) return 1;
  try {
    const stored = localStorage.getItem('globalCaseCounter');
    return stored ? parseInt(stored, 10) : 1;
  } catch {
    return 1;
  }
}

// Global case counter for sequential case numbering
export const globalCaseCounter = writable<number>(getStoredGlobalCaseCounter());

// Subscribe to case counter changes and persist to localStorage
if (isBrowser) {
  globalCaseCounter.subscribe(counter => {
    localStorage.setItem('globalCaseCounter', counter.toString());
  });
}

// UUID generation that works in both browser and SSR environments
function generateId(): string {
  if (isBrowser && typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for SSR or older browsers
  return 'xxxx-xxxx-4xxx-yxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Format timestamp for display
function formatTimestamp(date: Date): string {
  return date.toLocaleString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Helper function to get stored user (for fallback only)
function getStoredUser(): User | null {
  if (!isBrowser) return null;
  try {
    const stored = localStorage.getItem('currentUser');
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
}

// Helper function to get stored token
function getStoredToken(): string | null {
  if (!isBrowser) return null;
  try {
    return localStorage.getItem('authToken');
  } catch {
    return null;
  }
}

// Authentication stores - currentUser will be populated from API only
export const currentUser = writable<User | null>(null);
export const authToken = writable<string | null>(getStoredToken());
export const isAuthLoading = writable<boolean>(true);
export const isAuthenticated = derived(currentUser, ($user) => $user !== null);

// Derived store to check if user is superuser (can access admin menu)
export const isSuperuser = derived(currentUser, ($user) => $user?.userType === UserType.Superuser);

// Subscribe to currentUser changes and persist to localStorage
if (isBrowser) {
  currentUser.subscribe(user => {
    if (user) {
      localStorage.setItem('currentUser', JSON.stringify(user));
    } else {
      localStorage.removeItem('currentUser');
    }
  });

  authToken.subscribe(token => {
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  });
}

// Helper functions to get stored tab instances
function getStoredTabInstances(): TabInstance[] {
  if (!isBrowser) return [];
  try {
    const stored = localStorage.getItem('tabInstances');
    if (!stored) return [];
    const instances = JSON.parse(stored);
    // Convert date strings back to Date objects
    return instances.map((instance: any) => ({
      ...instance,
      createdAt: new Date(instance.createdAt),
      lastModified: new Date(instance.lastModified)
    }));
  } catch {
    return [];
  }
}

function getStoredActiveTabId(): string | null {
  if (!isBrowser) return null;
  try {
    return localStorage.getItem('activeTabId');
  } catch {
    return null;
  }
}

// Helper function to get next case number and increment counter
function getNextCaseNumber(): number {
  const currentCounter = get(globalCaseCounter);
  globalCaseCounter.set(currentCounter + 1);
  return currentCounter;
}

// Helper function to create a new tab instance
function createNewTabInstance(name?: string, tags: string[] = []): TabInstance {
  const caseNumber = name ? undefined : getNextCaseNumber();
  return {
    id: generateId(),
    name: name || `Case #${caseNumber}`,
    tags,
    createdAt: new Date(),
    lastModified: new Date(),
    data: createDefaultInstanceData() // Use deep clone function
  };
}

// Helper function to ensure at least one tab exists
function ensureTabsExist() {
  const instances = get(tabInstances);
  const currentActiveId = get(activeTabId);
  
  // If no tabs exist, create a default one
  if (instances.length === 0) {
    console.log('No tabs found, creating default tab');
    const newTabId = createNewTab('New Case');
    activeTabId.set(newTabId);
    return;
  }
  
  // If no active tab is set, set one
  if (!currentActiveId || !instances.some(tab => tab.id === currentActiveId)) {
    console.log('No active tab set, activating first available tab');
    activeTabId.set(instances[0].id);
  }
}

// Migration function to convert old instance system to new tab system
function migrateOldInstanceData(): TabInstance[] {
  if (!isBrowser) return [];
  
  const existingTabs = getStoredTabInstances();
  if (existingTabs.length > 0) return existingTabs;
  
  // Check for old inbound/outbound data
  const oldInboundData = localStorage.getItem('inboundData');
  const oldOutboundData = localStorage.getItem('outboundData');
  const oldActiveInstance = localStorage.getItem('activeInstance');
  
  const migratedTabs: TabInstance[] = [];
  
  if (oldInboundData) {
    try {
      const inboundInstanceData = JSON.parse(oldInboundData);
      const caseNumber = getNextCaseNumber();
      const reference = inboundInstanceData.rentCalculator?.reference;
      const name = inboundInstanceData.rentCalculator?.name;
      
      // Prefer name over reference over case number for migration
      let tabName: string;
      if (name && name.trim()) {
        tabName = name.trim();
      } else if (reference && reference.trim()) {
        tabName = reference.trim();
      } else {
        tabName = `Case #${caseNumber}`;
      }
      
      const inboundTab: TabInstance = {
        id: generateId(),
        name: tabName,
        tags: [],
        createdAt: new Date(),
        lastModified: new Date(),
        data: inboundInstanceData
      };
      migratedTabs.push(inboundTab);
    } catch (e) {
      console.warn('Failed to migrate inbound data:', e);
    }
  }
  
  if (oldOutboundData) {
    try {
      const outboundInstanceData = JSON.parse(oldOutboundData);
      const caseNumber = getNextCaseNumber();
      const reference = outboundInstanceData.rentCalculator?.reference;
      const name = outboundInstanceData.rentCalculator?.name;
      
      // Prefer name over reference over case number for migration
      let tabName: string;
      if (name && name.trim()) {
        tabName = name.trim();
      } else if (reference && reference.trim()) {
        tabName = reference.trim();
      } else {
        tabName = `Case #${caseNumber}`;
      }
      
      const outboundTab: TabInstance = {
        id: generateId(),
        name: tabName,
        tags: [],
        createdAt: new Date(),
        lastModified: new Date(),
        data: outboundInstanceData
      };
      migratedTabs.push(outboundTab);
    } catch (e) {
      console.warn('Failed to migrate outbound data:', e);
    }
  }
  
  // Clean up old localStorage keys
  if (oldInboundData || oldOutboundData) {
    localStorage.removeItem('inboundData');
    localStorage.removeItem('outboundData');
    localStorage.removeItem('activeInstance');
    console.log('Migrated old instance data to new tab system');
  }
  
  // If no data to migrate and no existing tabs, create a default tab
  if (migratedTabs.length === 0) {
    migratedTabs.push(createNewTabInstance('New Case'));
  }
  
  return migratedTabs;
}

// Initialize tab instances with migration - defer until browser is available
function getInitialTabInstances(): TabInstance[] {
  if (!isBrowser) return [createNewTabInstance('New Case')];
  return migrateOldInstanceData();
}

function getInitialActiveTabId(): string | null {
  if (!isBrowser) return null;
  const instances = getInitialTabInstances();
  return instances.length > 0 ? (getStoredActiveTabId() || instances[0].id) : null;
}

// Tab instance management with localStorage persistence
export const tabInstances = writable<TabInstance[]>(getInitialTabInstances());
export const activeTabId = writable<string | null>(getInitialActiveTabId());

// Subscribe to changes and persist to localStorage
if (isBrowser) {
  tabInstances.subscribe(instances => {
    localStorage.setItem('tabInstances', JSON.stringify(instances));
  });

  activeTabId.subscribe(tabId => {
    if (tabId) {
      localStorage.setItem('activeTabId', tabId);
    } else {
      localStorage.removeItem('activeTabId');
    }
  });
}

// Current active tab instance data (derived from activeTabId)
export const currentInstanceData = derived(
  [tabInstances, activeTabId],
  ([$tabInstances, $activeTabId]) => {
    if (!$activeTabId) return createDefaultInstanceData();
    const activeTab = $tabInstances.find(tab => tab.id === $activeTabId);
    return activeTab?.data || createDefaultInstanceData();
  }
);

// Current active tab (derived from activeTabId)
export const currentTab = derived(
  [tabInstances, activeTabId],
  ([$tabInstances, $activeTabId]) => {
    if (!$activeTabId) return null;
    return $tabInstances.find(tab => tab.id === $activeTabId) || null;
  }
);

// Track save status of tabs
export const tabSaveStatus = writable<Map<string, { saved: boolean, lastSaved?: Date }>>(new Map());


// Current tenant
export const currentTenant = writable<Tenant | null>(null);

// Loading states
export const isLoading = writable<boolean>(false);
export const loadingMessage = writable<string>('');

// Error handling
export const errorMessage = writable<string>('');

// View mode store
export const viewMode = writable<'compact' | 'full'>('full');

// Navigation is always in header mode now - sidebar system removed

// Helper function to refresh user data from API
async function refreshUserDataFromAPI() {
  try {
    const token = get(authToken);
    if (!token) {
      currentUser.set(null);
      return;
    }
    
    const { getUser } = await import('$lib/utils/api');
    const freshUserData = await getUser(token);
    currentUser.set(freshUserData);
    console.log('User data refreshed from API');
  } catch (error) {
    console.error('Failed to refresh user data from API:', error);
    // Clear user data if API call fails (no localStorage fallback)
    currentUser.set(null);
    throw error; // Re-throw to allow caller to handle
  }
}

// Reactive tab initialization - ensures tabs exist when authenticated
if (isBrowser) {
  // Subscribe to authentication state changes to ensure tabs exist
  isAuthenticated.subscribe(authenticated => {
    if (authenticated) {
      // Small delay to ensure stores are properly loaded
      setTimeout(() => {
        const instances = get(tabInstances);
        if (instances.length === 0) {
          console.log('Auto-initializing tabs after authentication');
          ensureTabsExist();
        }
      }, 50);
    }
  });
  
  // Check if we have a token on page load and load user data from API
  const token = getStoredToken();
  if (token) {
    // Set token immediately (loading state already true)
    authToken.set(token);
    
    // Load user data from API immediately (no localStorage fallback)
    refreshUserDataFromAPI().then(() => {
      console.log('User data loaded from API on startup');
      isAuthLoading.set(false);
    }).catch((error) => {
      console.error('Failed to load user data from API on startup:', error);
      // Clear invalid token if API call fails
      authToken.set(null);
      isAuthLoading.set(false);
    });
  } else {
    // No token found, authentication check complete
    isAuthLoading.set(false);
  }
}

// Derived store for total monthly payments made
export const totalMonthlyPayments = derived(
  [currentInstanceData],
  ([$currentInstanceData]) => {
    const rentCalculatorData = $currentInstanceData?.rentCalculator || {};
    const tenantPayment = rentCalculatorData.tenantMonthlyPayment || 0;
    const apaPayment = rentCalculatorData.apaHbMonthlyPayment || 0;
    const tpdPayment = rentCalculatorData.tpdMonthlyPayment || 0;
    // Convert weekly payments to monthly
    const tenantWeekly = rentCalculatorData.tenantWeeklyPayment || 0;
    const tenantWeeklyMonthly = tenantWeekly * 52 / 12;
    const benefitsWeekly = rentCalculatorData.benefitsHbWeeklyPayment || 0;
    const benefitsMonthly = benefitsWeekly * 52 / 12;
    
    return tenantPayment + apaPayment + tpdPayment + tenantWeeklyMonthly + benefitsMonthly;
  }
);

// Priority mapping for color scheme
const priorityOrder = {
  'urgent': 4,
  'high': 3,
  'medium': 2,
  'low': 1
};

// Enhanced derived store for suggested actions with comprehensive business rules
export const suggestedActions = derived(
  [currentInstanceData, totalMonthlyPayments],
  ([$currentInstanceData, $totalMonthlyPayments]) => {
    const rentCalculatorData = $currentInstanceData?.rentCalculator || {};
    const balance = rentCalculatorData.currentBalance || 0;
    const weeklyRent = rentCalculatorData.weeklyRent || 0;
    const monthlyRent = weeklyRent * 52 / 12;
    const fourWeeksRent = weeklyRent * 4;
    const eightWeeksRent = weeklyRent * 8;
    
    // Check if APA or TPD fields are filled (to filter out corresponding suggestions)
    const apaFilled = (rentCalculatorData.apaHbMonthlyPayment || 0) > 0;
    const tpdFilled = (rentCalculatorData.tpdMonthlyPayment || 0) > 0;
    
    const suggestions: Action[] = [];
    
    // Only show suggestions if there's actually a weekly rent set (prevents issues with empty forms)
    if (weeklyRent <= 0) {
      return suggestions;
    }
    
    // Arr1 (low) – should only show if current balance between 0 and 400
    if (balance > 0 && balance <= 400) {
      suggestions.push({
        code: 'Arr1',
        title: 'Initiate escalation policy',
        description: 'Make initial contact to discuss arrears',
        responseDescription: 'First contact made to address arrears',
        priority: 'low',
        triggered: `Current balance (£${balance.toFixed(2)}) between £0-£400`,
        order: 1
      });
    }
    
    // NSP (high) – debt over 4 weeks rent
    if (balance > fourWeeksRent && fourWeeksRent > 0) {
      suggestions.push({
        code: 'NSP',
        title: 'Serve Notice of Seeking Possession',
        description: 'Notice required for arrears exceeding 4 weeks rent',
        responseDescription: 'Notice of Seeking Possession served',
        priority: 'high',
        triggered: `Current balance (£${balance.toFixed(2)}) > 4 weeks rent (£${fourWeeksRent.toFixed(2)})`,
        order: 2
      });
    }
    
    // SCRF (high) – debt over 8 weeks rent. Not to be suggested if full rent plus £21 per month or more is being paid
    if (balance > eightWeeksRent && eightWeeksRent > 0 && $totalMonthlyPayments < (monthlyRent + 21)) {
      suggestions.push({
        code: 'SCRF',
        title: 'Consider legal action',
        description: 'Court referral recommended for high arrears',
        responseDescription: 'Case referred to court for legal action',
        priority: 'high',
        triggered: `Current balance (£${balance.toFixed(2)}) > 8 weeks rent (£${eightWeeksRent.toFixed(2)}) and payments below rent + £21`,
        order: 3
      });
    }
    
    // APA (med) – debt on the account, and no payments in the APA input
    if (balance > 0 && !apaFilled) {
      suggestions.push({
        code: 'APA',
        title: 'Request direct payments',
        description: 'Alternative Payment Arrangement needed due to arrears',
        responseDescription: 'Alternative Payment Arrangement requested',
        priority: 'medium',
        triggered: 'Current balance > 0 and no APA payments recorded',
        order: 4
      });
    }
    
    // TPD (med) – debt over 8 weeks rent, and no payments in the TPD input
    if (balance > eightWeeksRent && eightWeeksRent > 0 && !tpdFilled) {
      suggestions.push({
        code: 'TPD',
        title: 'Request direct payments',
        description: 'Third Party Deduction needed for high arrears',
        responseDescription: 'Third Party Deduction requested',
        priority: 'medium',
        triggered: `Current balance (£${balance.toFixed(2)}) > 8 weeks rent (£${eightWeeksRent.toFixed(2)}) and no TPD payments recorded`,
        order: 5
      });
    }
    
    // IRP (med) – debt on the account, some money is being paid, but not enough to cover the full rent. Do not show if STTC is showing.
    const isSTTCShowing = balance > 0 && monthlyRent > 0 && Math.abs($totalMonthlyPayments - monthlyRent) < 1;
    if (balance > 0 && $totalMonthlyPayments > 0 && monthlyRent > 0 && $totalMonthlyPayments < monthlyRent && !isSTTCShowing) {
      suggestions.push({
        code: 'IRP',
        title: 'Discuss incorrect payments',
        description: 'Payments received are less than monthly rent due',
        responseDescription: 'Discussed incorrect payments with tenant',
        priority: 'medium',
        triggered: `Total payments (£${$totalMonthlyPayments.toFixed(2)}) < monthly rent (£${monthlyRent.toFixed(2)})`,
        order: 6
      });
    }
    
    // STTC (med) – if there is a debt on the account but the full rent (roughly) is being paid
    if (isSTTCShowing) {
      suggestions.push({
        code: 'STTC',
        title: 'Address static arrears',
        description: 'Arrears not reducing despite full rent payments',
        responseDescription: 'Addressed static arrears with tenant',
        priority: 'medium',
        triggered: `Balance exists (£${balance.toFixed(2)}) but payments equal rent (£${monthlyRent.toFixed(2)})`,
        order: 7
      });
    }
    
    // Sort by order and return
    return suggestions.sort((a, b) => a.order - b.order);
  }
);

// Derived store for highest priority action (for banner styling)
export const highestPriorityAction = derived(
  [suggestedActions],
  ([$suggestedActions]) => {
    if ($suggestedActions.length === 0) return 'medium'; // default to medium if no actions
    
    // Find the highest priority action
    const highestPriority = $suggestedActions.reduce((highest, action) => {
      const currentPriorityLevel = priorityOrder[action.priority as keyof typeof priorityOrder] || 0;
      const highestPriorityLevel = priorityOrder[highest as keyof typeof priorityOrder] || 0;
      return currentPriorityLevel > highestPriorityLevel ? action.priority : highest;
    }, 'low');
    
    return highestPriority;
  }
);

// Helper function to automatically update tab name when reference or name changes
function updateTabNameFromData(tabId: string, reference?: string, name?: string) {
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === tabId) {
        // Only update if the current name follows the "Case #X" or "Case #X - Reference" pattern or is just a reference/name
        const isAutomaticName = /^Case #\d+( - .*)?$/.test(instance.name) || /^[A-Za-z0-9\-_\/\s]+$/.test(instance.name);
        if (isAutomaticName) {
          const caseNumberMatch = instance.name.match(/^Case #\d+/);
          const caseNumber = caseNumberMatch ? caseNumberMatch[0] : 'Case';
          
          // Prefer name over reference over case number
          let newName: string;
          if (name && name.trim()) {
            newName = name.trim();
          } else if (reference && reference.trim()) {
            newName = reference.trim();
          } else {
            newName = caseNumber;
          }
          
          return {
            ...instance,
            name: newName,
            lastModified: new Date()
          };
        }
      }
      return instance;
    });
  });
}

// Helper functions for updating tab instance data
export function updateRentCalculatorData(data: Partial<RentCalculatorData>) {
  const currentTabId = get(activeTabId);
  if (!currentTabId) return;
  
  console.log('Store: updateRentCalculatorData called', { tabId: currentTabId, data });
  
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === currentTabId) {
        const updated = {
          ...instance,
          lastModified: new Date(),
          data: {
            ...instance.data,
            rentCalculator: { ...instance.data.rentCalculator, ...data }
          }
        };
        console.log('Store: tab data updated', updated.data.rentCalculator);
        return updated;
      }
      return instance;
    });
  });
  
  // Mark tab as unsaved
  tabSaveStatus.update(status => {
    const newStatus = new Map(status);
    newStatus.set(currentTabId, { saved: false });
    return newStatus;
  });
  
  // If reference or name was updated, automatically update tab name
  if (data.reference !== undefined || data.name !== undefined) {
    // Get the current instance to access both reference and name
    const currentInstance = get(tabInstances).find(instance => instance.id === currentTabId);
    if (currentInstance) {
      const currentRef = data.reference !== undefined ? data.reference : currentInstance.data.rentCalculator.reference;
      const currentName = data.name !== undefined ? data.name : currentInstance.data.rentCalculator.name;
      updateTabNameFromData(currentTabId, currentRef, currentName);
    }
  }
}

export function updateArrangementPlannerData(data: Partial<ArrangementPlannerData>) {
  const currentTabId = get(activeTabId);
  if (!currentTabId) return;
  
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === currentTabId) {
        return {
          ...instance,
          lastModified: new Date(),
          data: {
            ...instance.data,
            arrangementPlanner: { ...instance.data.arrangementPlanner, ...data }
          }
        };
      }
      return instance;
    });
  });
  
  // Mark tab as unsaved
  tabSaveStatus.update(status => {
    const newStatus = new Map(status);
    newStatus.set(currentTabId, { saved: false });
    return newStatus;
  });
}

export function updateAccountChargesData(data: Partial<AccountChargesData>) {
  const currentTabId = get(activeTabId);
  if (!currentTabId) return;
  
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === currentTabId) {
        return {
          ...instance,
          lastModified: new Date(),
          data: {
            ...instance.data,
            accountCharges: { ...instance.data.accountCharges, ...data }
          }
        };
      }
      return instance;
    });
  });
  
  // Mark tab as unsaved
  tabSaveStatus.update(status => {
    const newStatus = new Map(status);
    newStatus.set(currentTabId, { saved: false });
    return newStatus;
  });
}

export function updateNotes(notes: string) {
  const currentTabId = get(activeTabId);
  if (!currentTabId) return;
  
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === currentTabId) {
        return {
          ...instance,
          lastModified: new Date(),
          data: {
            ...instance.data,
            notes
          }
        };
      }
      return instance;
    });
  });
  
  // Mark tab as unsaved
  tabSaveStatus.update(status => {
    const newStatus = new Map(status);
    newStatus.set(currentTabId, { saved: false });
    return newStatus;
  });
}



export function clearAllData() {
  tabInstances.set([]);
  activeTabId.set(null);
  currentTenant.set(null);
  globalCaseCounter.set(1); // Reset case counter
  
  // Clear localStorage
  if (isBrowser) {
    localStorage.removeItem('tabInstances');
    localStorage.removeItem('activeTabId');
    localStorage.removeItem('globalCaseCounter');
  }
}

export function clearCurrentInstance() {
  const currentTabId = get(activeTabId);
  if (!currentTabId) return;
  
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === currentTabId) {
        return {
          ...instance,
          lastModified: new Date(),
          data: createDefaultInstanceData()
        };
      }
      return instance;
    });
  });
}

// New tab management functions
export function createNewTab(name?: string, tags: string[] = []): string {
  const newTab = createNewTabInstance(name, tags);
  
  tabInstances.update(instances => [...instances, newTab]);
  activeTabId.set(newTab.id);
  
  return newTab.id;
}

export function closeTab(tabId: string) {
  tabInstances.update(instances => {
    const filtered = instances.filter(tab => tab.id !== tabId);
    
    // If we're closing the active tab, switch to another tab or create a new one
    const currentActiveId = get(activeTabId);
    if (currentActiveId === tabId) {
      if (filtered.length > 0) {
        activeTabId.set(filtered[filtered.length - 1].id);
      } else {
        // No rent calculator tabs left, check if there are other types of tabs
        const caseDetails = get(caseDetailTabs);
        const settings = get(settingsTabs);
        const profiles = get(profileTabs);
        const analytics = get(analyticsTabs);
        
        if (caseDetails.length > 0) {
          // Switch to a case detail tab
          activeCaseDetailTabId.set(caseDetails[caseDetails.length - 1].id);
          activeTabId.set(null);
        } else if (settings.length > 0) {
          // Switch to a settings tab
          activeSettingsTabId.set(settings[settings.length - 1].id);
          activeTabId.set(null);
        } else if (profiles.length > 0) {
          // Switch to a profile tab
          activeProfileTabId.set(profiles[profiles.length - 1].id);
          activeTabId.set(null);
        } else if (analytics.length > 0) {
          // Switch to an analytics tab
          activeAnalyticsTabId.set(analytics[analytics.length - 1].id);
          activeTabId.set(null);
        } else {
          // No other tabs exist, create a new rent calculator tab as last resort
          const newTab = createNewTabInstance();
          filtered.push(newTab);
          activeTabId.set(newTab.id);
        }
      }
    }
    
    return filtered;
  });
}

export function switchToTab(tabId: string) {
  const instances = get(tabInstances);
  const tabExists = instances.some(tab => tab.id === tabId);
  
  if (tabExists) {
    activeTabId.set(tabId);
  }
}

export function updateTabName(tabId: string, name: string) {
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === tabId) {
        return {
          ...instance,
          name,
          lastModified: new Date()
        };
      }
      return instance;
    });
  });
}

export function updateTabTags(tabId: string, tags: string[]) {
  tabInstances.update(instances => {
    return instances.map(instance => {
      if (instance.id === tabId) {
        return {
          ...instance,
          tags,
          lastModified: new Date()
        };
      }
      return instance;
    });
  });
}

// Case Detail Tab Management
export const caseDetailTabs = writable<CaseDetailTab[]>([]);
export const activeCaseDetailTabId = writable<string | null>(null);

// Settings Tab Management
export const settingsTabs = writable<SettingsTab[]>([]);
export const activeSettingsTabId = writable<string | null>(null);

// Profile Tab Management
export const profileTabs = writable<ProfileTab[]>([]);
export const activeProfileTabId = writable<string | null>(null);

// Analytics Tab Management
export const analyticsTabs = writable<AnalyticsTab[]>([]);
export const activeAnalyticsTabId = writable<string | null>(null);

// Combined tabs store for unified tab management
export const allTabs = derived(
  [tabInstances, caseDetailTabs, settingsTabs, profileTabs, analyticsTabs],
  ([$tabInstances, $caseDetailTabs, $settingsTabs, $profileTabs, $analyticsTabs]) => {
    return [...$tabInstances, ...$caseDetailTabs, ...$settingsTabs, ...$profileTabs, ...$analyticsTabs] as AppTab[];
  }
);

// Active tab (could be any type)
export const activeTab = derived(
  [allTabs, activeTabId, activeCaseDetailTabId, activeSettingsTabId, activeProfileTabId, activeAnalyticsTabId],
  ([$allTabs, $activeTabId, $activeCaseDetailTabId, $activeSettingsTabId, $activeProfileTabId, $activeAnalyticsTabId]) => {
    const activeId = $activeAnalyticsTabId || $activeProfileTabId || $activeSettingsTabId || $activeCaseDetailTabId || $activeTabId;
    if (!activeId) return null;
    return $allTabs.find(tab => tab.id === activeId) || null;
  }
);

// TODO: Refactor case detail tabs to work with Case entities instead of session notes

export function closeCaseDetailTab(tabId: string) {
  caseDetailTabs.update(tabs => {
    const filtered = tabs.filter(tab => tab.id !== tabId);

    // If we're closing the active case detail tab, switch to another tab
    const currentActiveId = get(activeCaseDetailTabId);
    if (currentActiveId === tabId) {
      if (filtered.length > 0) {
        activeCaseDetailTabId.set(filtered[filtered.length - 1].id);
      } else {
        // No case detail tabs left, switch back to rent calculator tabs
        activeCaseDetailTabId.set(null);
        const instances = get(tabInstances);
        if (instances.length > 0) {
          activeTabId.set(instances[instances.length - 1].id);
        } else {
          // Create a new rent calculator tab if none exist
          const newTabId = createNewTab();
          activeTabId.set(newTabId);
        }
      }
    }

    return filtered;
  });
}

export function switchToCaseDetailTab(tabId: string) {
  const tabs = get(caseDetailTabs);
  const tabExists = tabs.some(tab => tab.id === tabId);

  if (tabExists) {
    activeCaseDetailTabId.set(tabId);
    activeTabId.set(null); // Deactivate rent calculator tabs
    activeSettingsTabId.set(null); // Deactivate settings tabs
    activeProfileTabId.set(null); // Deactivate profile tabs
    activeAnalyticsTabId.set(null); // Deactivate analytics tabs
  }
}

export function switchToRentCalculatorTab(tabId: string) {
  const instances = get(tabInstances);
  const tabExists = instances.some(tab => tab.id === tabId);

  if (tabExists) {
    activeTabId.set(tabId);
    activeCaseDetailTabId.set(null); // Deactivate case detail tabs
    activeSettingsTabId.set(null); // Deactivate settings tabs
    activeProfileTabId.set(null); // Deactivate profile tabs
    activeAnalyticsTabId.set(null); // Deactivate analytics tabs
  }
}

// Settings Tab Functions
export function createSettingsTab(): string {
  // Check if a settings tab already exists
  const existingTabs = get(settingsTabs);
  if (existingTabs.length > 0) {
    // Switch to existing settings tab
    switchToSettingsTab(existingTabs[0].id);
    return existingTabs[0].id;
  }

  const newTab: SettingsTab = {
    id: generateId(),
    type: 'settings',
    name: 'Tags & Templates',
    createdAt: new Date(),
    lastModified: new Date()
  };

  settingsTabs.update(tabs => [...tabs, newTab]);
  
  // Initialize save status for the new settings tab
  tabSaveStatus.update(status => {
    const newStatus = new Map(status);
    newStatus.set(newTab.id, { saved: true, lastSaved: new Date() });
    return newStatus;
  });
  
  switchToSettingsTab(newTab.id);

  return newTab.id;
}

export function closeSettingsTab(tabId: string) {
  settingsTabs.update(tabs => {
    const filtered = tabs.filter(tab => tab.id !== tabId);

    // If we're closing the active settings tab, switch to another tab
    const currentActiveId = get(activeSettingsTabId);
    if (currentActiveId === tabId) {
      if (filtered.length > 0) {
        activeSettingsTabId.set(filtered[filtered.length - 1].id);
      } else {
        // No settings tabs left, switch back to rent calculator tabs
        activeSettingsTabId.set(null);
        const instances = get(tabInstances);
        if (instances.length > 0) {
          activeTabId.set(instances[instances.length - 1].id);
        } else {
          // Create a new rent calculator tab if none exist
          const newTabId = createNewTab();
          activeTabId.set(newTabId);
        }
      }
    }

    return filtered;
  });
  
  // Clean up save status for the closed tab
  tabSaveStatus.update(status => {
    const newStatus = new Map(status);
    newStatus.delete(tabId);
    return newStatus;
  });
}

export function switchToSettingsTab(tabId: string) {
  const tabs = get(settingsTabs);
  const tabExists = tabs.some(tab => tab.id === tabId);

  if (tabExists) {
    activeSettingsTabId.set(tabId);
    activeTabId.set(null); // Deactivate rent calculator tabs
    activeCaseDetailTabId.set(null); // Deactivate case detail tabs
    activeProfileTabId.set(null); // Deactivate profile tabs
    activeAnalyticsTabId.set(null); // Deactivate analytics tabs
  }
}

// Profile Tab Functions
export function createProfileTab(): string {
  // Check if a profile tab already exists
  const existingTabs = get(profileTabs);
  if (existingTabs.length > 0) {
    // Switch to existing profile tab
    switchToProfileTab(existingTabs[0].id);
    return existingTabs[0].id;
  }

  const newTab: ProfileTab = {
    id: generateId(),
    type: 'profile',
    name: 'Profile Settings',
    createdAt: new Date(),
    lastModified: new Date()
  };

  profileTabs.update(tabs => [...tabs, newTab]);
  switchToProfileTab(newTab.id);

  return newTab.id;
}

export function closeProfileTab(tabId: string) {
  profileTabs.update(tabs => {
    const filtered = tabs.filter(tab => tab.id !== tabId);

    // If we're closing the active profile tab, switch to another tab
    const currentActiveId = get(activeProfileTabId);
    if (currentActiveId === tabId) {
      if (filtered.length > 0) {
        activeProfileTabId.set(filtered[filtered.length - 1].id);
      } else {
        // No profile tabs left, switch back to rent calculator tabs
        activeProfileTabId.set(null);
        const instances = get(tabInstances);
        if (instances.length > 0) {
          activeTabId.set(instances[instances.length - 1].id);
        } else {
          // Create a new rent calculator tab if none exist
          const newTabId = createNewTab();
          activeTabId.set(newTabId);
        }
      }
    }

    return filtered;
  });
}

export function switchToProfileTab(tabId: string) {
  const tabs = get(profileTabs);
  const tabExists = tabs.some(tab => tab.id === tabId);

  if (tabExists) {
    activeProfileTabId.set(tabId);
    activeTabId.set(null); // Deactivate rent calculator tabs
    activeCaseDetailTabId.set(null); // Deactivate case detail tabs
    activeSettingsTabId.set(null); // Deactivate settings tabs
    activeAnalyticsTabId.set(null); // Deactivate analytics tabs
  }
}

// Analytics Tab Functions
export function createAnalyticsTab(): string {
  // Check if an analytics tab already exists
  const existingTabs = get(analyticsTabs);
  if (existingTabs.length > 0) {
    // Switch to existing analytics tab
    switchToAnalyticsTab(existingTabs[0].id);
    return existingTabs[0].id;
  }

  const newTab: AnalyticsTab = {
    id: generateId(),
    type: 'analytics',
    name: 'Portfolio Analytics',
    createdAt: new Date(),
    lastModified: new Date()
  };

  analyticsTabs.update(tabs => [...tabs, newTab]);
  switchToAnalyticsTab(newTab.id);

  return newTab.id;
}

export function closeAnalyticsTab(tabId: string) {
  analyticsTabs.update(tabs => {
    const filtered = tabs.filter(tab => tab.id !== tabId);

    // If we're closing the active analytics tab, switch to another tab
    const currentActiveId = get(activeAnalyticsTabId);
    if (currentActiveId === tabId) {
      if (filtered.length > 0) {
        activeAnalyticsTabId.set(filtered[filtered.length - 1].id);
      } else {
        // No analytics tabs left, switch back to rent calculator tabs
        activeAnalyticsTabId.set(null);
        const instances = get(tabInstances);
        if (instances.length > 0) {
          activeTabId.set(instances[instances.length - 1].id);
        } else {
          // Create a new rent calculator tab if none exist
          const newTabId = createNewTab();
          activeTabId.set(newTabId);
        }
      }
    }

    return filtered;
  });
}

export function switchToAnalyticsTab(tabId: string) {
  const tabs = get(analyticsTabs);
  const tabExists = tabs.some(tab => tab.id === tabId);

  if (tabExists) {
    activeAnalyticsTabId.set(tabId);
    activeTabId.set(null); // Deactivate rent calculator tabs
    activeCaseDetailTabId.set(null); // Deactivate case detail tabs
    activeSettingsTabId.set(null); // Deactivate settings tabs
    activeProfileTabId.set(null); // Deactivate profile tabs
  }
}

// Save current tab to case log and close it
export function saveToCaseLogAndClose(): boolean {
  const currentTabId = get(activeTabId);
  if (!currentTabId) return false;
  
  const currentTabData = get(currentTab);
  if (!currentTabData) return false;
  
  // TODO: Implement actual case saving to backend
  const tenantRef = currentTabData.data.rentCalculator.reference || 'Unknown';
  console.log(`Case saved: ${currentTabData.name} for tenant ${tenantRef}`);
  
  closeTab(currentTabId);
  return true;
}

// Authentication functions using API
export async function login(email: string, password: string): Promise<boolean> {
  isLoading.set(true);
  loadingMessage.set('Logging in...');
  
  try {
    const { login: apiLogin, getUser } = await import('$lib/utils/api');
    
    const loginResponse = await apiLogin({ email, password });
    
    if (!loginResponse.success || !loginResponse.token) {
      errorMessage.set(loginResponse.error || 'Login failed. Please try again.');
      return false;
    }
    
    // Set token first
    authToken.set(loginResponse.token);
    
    // Get user data
    const userData = await getUser(loginResponse.token);
    currentUser.set(userData);
    
    // Ensure we have at least one tab after successful login
    ensureTabsExist();
    
    // Authentication complete
    isAuthLoading.set(false);
    
    return true;
  } catch (error) {
    console.error('Login error:', error);
    errorMessage.set('Login failed. Please check your connection and try again.');
    isAuthLoading.set(false);
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

export function logout() {
  currentUser.set(null);
  authToken.set(null);
  isAuthLoading.set(false);
  clearAllData();

  // Clear all localStorage related to the app
  if (isBrowser) {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
  }
}

// Export function to manually refresh user data
export async function refreshUserData(): Promise<boolean> {
  try {
    await refreshUserDataFromAPI();
    return true;
  } catch (error) {
    console.error('Failed to refresh user data:', error);
    return false;
  }
}

export async function updateUser(updatedUser: Partial<User>): Promise<boolean> {
  isLoading.set(true);
  loadingMessage.set('Updating profile...');
  
  try {
    const token = get(authToken);
    if (!token) {
      throw new Error('No authentication token available');
    }

    // Prepare the update request
    const updateRequest = {
      firstName: updatedUser.firstName || '',
      lastName: updatedUser.lastName || '',
      jobTitle: updatedUser.jobTitle,
      department: updatedUser.department
    };

    // Dynamic import to avoid circular dependencies
    const { updateUser: apiUpdateUser } = await import('$lib/utils/api');
    
    // Call the API to update user
    const updatedUserData = await apiUpdateUser(updateRequest, token);
    
    // Update the local store with the response
    currentUser.set(updatedUserData);
    
    return true;
  } catch (error) {
    console.error('Update user error:', error);
    errorMessage.set('Failed to update profile. Please try again.');
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

// Case data persistence functions using API
export async function saveCaseData(tabId: string): Promise<boolean> {
  isLoading.set(true);
  loadingMessage.set('Saving case...');
  
  try {
    const token = get(authToken);
    if (!token) {
      errorMessage.set('Please log in to save case data.');
      return false;
    }

    const tab = get(tabInstances).find(t => t.id === tabId);
    if (!tab) {
      errorMessage.set('Case not found.');
      return false;
    }

    const { createCase, updateCase } = await import('$lib/utils/api');
    
    const caseRequest = {
      reference: tab.data.rentCalculator.reference,
      name: tab.data.rentCalculator.name,
      currentBalance: tab.data.rentCalculator.currentBalance,
      weeklyRent: tab.data.rentCalculator.weeklyRent,
      tenantMonthlyPayment: tab.data.rentCalculator.tenantMonthlyPayment,
      apaHbMonthlyPayment: tab.data.rentCalculator.apaHbMonthlyPayment,
      tpdMonthlyPayment: tab.data.rentCalculator.tpdMonthlyPayment,
      tenantWeeklyPayment: tab.data.rentCalculator.tenantWeeklyPayment,
      benefitsHbWeeklyPayment: tab.data.rentCalculator.benefitsHbWeeklyPayment,
      weeksToNextPay: tab.data.arrangementPlanner.weeksToNextPay,
      paymentDue: tab.data.arrangementPlanner.paymentDue,
      timeframe: tab.data.arrangementPlanner.timeframe,
      grossWeeklyRent: tab.data.accountCharges.grossWeeklyRent,
      rentComponent: tab.data.accountCharges.rentComponent,
      nonUcServiceChargeFormula: tab.data.accountCharges.nonUcServiceChargeFormula,
      nonUcServiceChargeTotal: tab.data.accountCharges.nonUcServiceChargeTotal || 0,
      grossWeeklyRentOverridden: tab.data.accountCharges.grossWeeklyRentOverridden || false,
      notes: tab.data.notes
    };

    // Check if tab has a backend case ID (stored in tags or metadata)
    const existingCaseId = tab.tags.find(tag => tag.startsWith('case-id:'))?.replace('case-id:', '');
    
    if (existingCaseId) {
      // Update existing case
      await updateCase(existingCaseId, caseRequest, token);
    } else {
      // Create new case
      const newCase = await createCase(caseRequest, token);
      
      // Add case ID to tab tags
      updateTabTags(tabId, [...tab.tags, `case-id:${newCase.id}`]);
    }

    // Update save status
    tabSaveStatus.update(status => {
      const newStatus = new Map(status);
      newStatus.set(tabId, { saved: true, lastSaved: new Date() });
      return newStatus;
    });

    return true;
  } catch (error) {
    console.error('Save case error:', error);
    errorMessage.set('Failed to save case. Please try again.');
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

export async function loadCaseData(caseId: string): Promise<string | null> {
  isLoading.set(true);
  loadingMessage.set('Loading case...');
  
  try {
    const token = get(authToken);
    if (!token) {
      errorMessage.set('Please log in to load case data.');
      return null;
    }

    const { getCase } = await import('$lib/utils/api');
    const caseData = await getCase(caseId, token);
    
    // Create a new tab with the loaded data
    const tabId = createNewTab(caseData.reference || 'Loaded Case', [`case-id:${caseId}`]);
    
    // Update the tab with the loaded data
    tabInstances.update(instances => {
      return instances.map(instance => {
        if (instance.id === tabId) {
          return {
            ...instance,
            data: {
              rentCalculator: {
                reference: caseData.reference,
                currentBalance: caseData.currentBalance,
                weeklyRent: caseData.weeklyRent,
                tenantMonthlyPayment: caseData.tenantMonthlyPayment,
                apaHbMonthlyPayment: caseData.apaHbMonthlyPayment,
                tpdMonthlyPayment: caseData.tpdMonthlyPayment,
                tenantWeeklyPayment: caseData.tenantWeeklyPayment,
                benefitsHbWeeklyPayment: caseData.benefitsHbWeeklyPayment
              },
              arrangementPlanner: {
                weeksToNextPay: caseData.weeksToNextPay,
                paymentDue: caseData.paymentDue,
                timeframe: caseData.timeframe
              },
              accountCharges: {
                grossWeeklyRent: caseData.grossWeeklyRent,
                rentComponent: caseData.rentComponent,
                nonUcServiceChargeFormula: caseData.nonUcServiceChargeFormula,
                nonUcServiceChargeTotal: caseData.nonUcServiceChargeTotal,
                grossWeeklyRentOverridden: caseData.grossWeeklyRentOverridden
              },
              notes: caseData.notes
            }
          };
        }
        return instance;
      });
    });

    return tabId;
  } catch (error) {
    console.error('Load case error:', error);
    errorMessage.set('Failed to load case. Please try again.');
    return null;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

export async function loadUserCases(): Promise<boolean> {
  isLoading.set(true);
  loadingMessage.set('Loading cases...');
  
  try {
    const token = get(authToken);
    if (!token) {
      return false;
    }

    const { getCases } = await import('$lib/utils/api');
    const cases = await getCases(token);
    
    console.log('Loaded cases from API:', cases);
    return true;
  } catch (error) {
    console.error('Load cases error:', error);
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

// TODO: Replace with case loading functionality


// Auto-save functionality
let autoSaveInterval: ReturnType<typeof setInterval> | null = null;
let lastSavedData: string | null = null;

export function enableAutoSave(intervalMinutes: number = 5) {
  if (autoSaveInterval) {
    clearInterval(autoSaveInterval);
  }
  
  autoSaveInterval = setInterval(async () => {
    const currentTabId = get(activeTabId);
    if (!currentTabId) return;
    
    // Check if data has changed since last save
    const currentData = JSON.stringify(get(currentInstanceData));
    if (currentData === lastSavedData) return;
    
    try {
      const success = await saveCaseData(currentTabId);
      if (success) {
        lastSavedData = currentData;
        console.log('Auto-saved case data for tab:', currentTabId);
        
        // Update save status to reflect auto-save
        tabSaveStatus.update(status => {
          const newStatus = new Map(status);
          newStatus.set(currentTabId, { saved: true, lastSaved: new Date() });
          return newStatus;
        });
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, intervalMinutes * 60 * 1000);
}

export function disableAutoSave() {
  if (autoSaveInterval) {
    clearInterval(autoSaveInterval);
    autoSaveInterval = null;
  }
}

// Auto-enable auto-save when user is authenticated
if (isBrowser) {
  isAuthenticated.subscribe(authenticated => {
    if (authenticated) {
      enableAutoSave(5); // Auto-save every 5 minutes
    } else {
      disableAutoSave();
    }
  });
}

// Legacy functions for backward compatibility
export async function saveInstanceData(): Promise<boolean> {
  // This function is kept for backward compatibility but not used in new tab system
  console.warn('saveInstanceData() is deprecated. Use saveCaseData() instead.');
  return false;
}

export async function loadInstanceData(): Promise<boolean> {
  // This function is kept for backward compatibility but not used in new tab system
  console.warn('loadInstanceData() is deprecated. Use loadCaseData() instead.');
  return false;
} 