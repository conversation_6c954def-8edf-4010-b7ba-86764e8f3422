import { writable, get } from 'svelte/store';
import { viewMode } from './index';

// Calendar preference options
export type CalendarPreference = 'ask-every-time' | 'modern-outlook' | 'classic-outlook' | 'web-outlook' | 'ics-download';

// Calculator view preference
export type CalculatorViewPreference = 'view1' | 'view2';

// Notes position preference
export type NotesPosition = 'bottom' | 'sidebar';

// Design system settings interface (from DevMode)
export interface DesignSystemSettings {
  labelWeight: number;
  labelColor: string;
  labelSize: number;
  valueSize: number;
  cellPadding: number;
  subheaderSize: number;
  buttonSize: number;
  gap: number;
  sectionHeaderSize: number;
  colorPrimary: string;
  colorSecondary: string;
  colorRed: string;
  colorOrange: string;
  colorYellow: string;
  colorGreen: string;
  bgColor: string;
  colorInstanceInbound: string;
  colorInstanceOutbound: string;
}

// User preferences interface
export interface UserPreferences {
  calculatorView: CalculatorViewPreference;
  viewMode: 'compact' | 'full';
  calendarPreference: CalendarPreference;
  notesPosition: NotesPosition;
  confirmTabClose: boolean;
  showQuickResponses: boolean;
  designSystem: DesignSystemSettings;
}

// Default design system values (matching DevMode defaults)
const defaultDesignSystemSettings: DesignSystemSettings = {
  labelWeight: 400,
  labelColor: '#374151',
  labelSize: 0.8,
  valueSize: 1,
  cellPadding: 0.5,
  subheaderSize: 0.9,
  buttonSize: 0.9,
  gap: 0.5,
  sectionHeaderSize: 0.825,
  colorPrimary: '#025582',
  colorSecondary: 'rgb(0, 51, 68)',
  colorRed: '#dc2626',
  colorOrange: '#ea580c',
  colorYellow: '#ca8a04',
  colorGreen: '#059669',
  bgColor: '#0255822d',
  colorInstanceInbound: '#DB2955',
  colorInstanceOutbound: '#4DA1A9'
};

// Default user preferences
const defaultUserPreferences: UserPreferences = {
  calculatorView: 'view2', // Default to full view
  viewMode: 'full',
  calendarPreference: 'ask-every-time',
  notesPosition: 'bottom',
  confirmTabClose: false, // Default to no confirmation for backward compatibility
  showQuickResponses: true, // Default to showing quick responses for backward compatibility
  designSystem: { ...defaultDesignSystemSettings }
};

// Browser check for SSR compatibility
const isBrowser = typeof window !== 'undefined';

// Helper function to get stored preferences
function getStoredPreferences(): UserPreferences {
  if (!isBrowser) return { ...defaultUserPreferences };
  try {
    const stored = localStorage.getItem('userPreferences');
    if (stored) {
      const parsed = JSON.parse(stored);
      return {
        ...defaultUserPreferences,
        ...parsed,
        designSystem: {
          ...defaultDesignSystemSettings,
          ...parsed.designSystem
        }
      };
    }
    return { ...defaultUserPreferences };
  } catch {
    return { ...defaultUserPreferences };
  }
}

// Create user preferences store
export const userPreferences = writable<UserPreferences>(getStoredPreferences());

// Subscribe to changes and persist to localStorage
if (isBrowser) {
  userPreferences.subscribe(preferences => {
    localStorage.setItem('userPreferences', JSON.stringify(preferences));
  });
}

// Helper functions for updating preferences
export function updateUserPreferences(updates: Partial<UserPreferences>) {
  userPreferences.update(current => ({
    ...current,
    ...updates
  }));
}

export function updateDesignSystemSettings(updates: Partial<DesignSystemSettings>) {
  userPreferences.update(current => ({
    ...current,
    designSystem: {
      ...current.designSystem,
      ...updates
    }
  }));
}

export function resetDesignSystemToDefaults() {
  userPreferences.update(current => ({
    ...current,
    designSystem: { ...defaultDesignSystemSettings }
  }));
}

export function resetAllPreferencesToDefaults() {
  userPreferences.set({ ...defaultUserPreferences });
}

// Apply design system settings to CSS custom properties
export function applyDesignSystemToCSS(designSystem: DesignSystemSettings) {
  if (!isBrowser) return;
  
  const root = document.documentElement;
  root.style.setProperty('--label-weight', designSystem.labelWeight.toString());
  root.style.setProperty('--label-color', designSystem.labelColor);
  root.style.setProperty('--label-size', `${designSystem.labelSize}rem`);
  root.style.setProperty('--value-size', `${designSystem.valueSize}rem`);
  root.style.setProperty('--cell-padding', `${designSystem.cellPadding}rem`);
  root.style.setProperty('--subheader-size', `${designSystem.subheaderSize}rem`);
  root.style.setProperty('--button-size', `${designSystem.buttonSize}rem`);
  root.style.setProperty('--gap', `${designSystem.gap}rem`);
  root.style.setProperty('--section-header-size', `${designSystem.sectionHeaderSize}rem`);
  root.style.setProperty('--color-primary', designSystem.colorPrimary);
  root.style.setProperty('--color-secondary', designSystem.colorSecondary);
  root.style.setProperty('--color-red', designSystem.colorRed);
  root.style.setProperty('--color-orange', designSystem.colorOrange);
  root.style.setProperty('--color-yellow', designSystem.colorYellow);
  root.style.setProperty('--color-green', designSystem.colorGreen);
  root.style.setProperty('--bg', designSystem.bgColor);
  root.style.setProperty('--color-instance-inbound', designSystem.colorInstanceInbound);
  root.style.setProperty('--color-instance-outbound', designSystem.colorInstanceOutbound);
}

// Calendar preference labels
export const calendarPreferenceLabels: Record<CalendarPreference, string> = {
  'ask-every-time': 'Ask every time',
  'modern-outlook': 'Modern Outlook App',
  'classic-outlook': 'Classic Outlook App',
  'web-outlook': 'Web Outlook',
  'ics-download': 'Download .ics file'
};

// Sync preferences with existing stores on initialization
if (isBrowser) {
  const currentPrefs = get(userPreferences);
  
  // Sync view mode with existing store
  viewMode.set(currentPrefs.viewMode);
  
  // Apply design system on load
  applyDesignSystemToCSS(currentPrefs.designSystem);
  
  // Subscribe to user preferences changes and apply design system
  userPreferences.subscribe(prefs => {
    applyDesignSystemToCSS(prefs.designSystem);
    
    // Sync with existing stores
    viewMode.set(prefs.viewMode);
  });
}