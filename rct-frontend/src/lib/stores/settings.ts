import { writable } from 'svelte/store';

export interface Tag {
  id: string;
  name: string;
  color: string;
  description?: string;
}

export interface ResponseTemplate {
  id: string;
  category: string;
  title: string;
  text: string;
  tags?: string[];
  keywords?: string[];
}

export interface TalkingPoint {
  id: string;
  category: string;
  title: string;
  content: string;
  keywords?: string[];
}

export interface Settings {
  tags: Tag[];
  responseTemplates: ResponseTemplate[];
  talkingPoints: TalkingPoint[];
}

// Default tags
const defaultTags: Tag[] = [
  { id: 'contact-made', name: 'Contact Made', color: '#059669', description: 'Successful contact with tenant' },
  { id: 'payment-plan', name: 'Payment Plan', color: '#0078d4', description: 'Payment arrangement discussed or agreed' },
  { id: 'legal-action', name: 'Legal Action', color: '#dc2626', description: 'Legal proceedings or warnings' },
  { id: 'vulnerability', name: 'Vulnerability', color: '#7c3aed', description: 'Vulnerability or support needs identified' },
  { id: 'benefits', name: 'Benefits', color: '#ea580c', description: 'Benefits or UC related issues' },
  { id: 'dispute', name: 'Dispute', color: '#ca8a04', description: 'Disagreement or dispute raised' },
  { id: 'urgent', name: 'Urgent', color: '#dc2626', description: 'Requires immediate attention' },
  { id: 'follow-up', name: 'Follow-up', color: '#0d9488', description: 'Follow-up action required' }
];

// Default response templates (moved from StandardResponses)
const defaultResponseTemplates: ResponseTemplate[] = [
  {
    id: 'unsuccessful_contact',
    category: 'Contact',
    title: 'Unsuccessful contact',
    text: 'Called to discuss XXX. No response. Left voicemail asking tenant to call back and followed with email/letter asking tenant to make contact.',
    tags: ['follow-up'],
    keywords: ['contact', 'voicemail', 'email', 'letter', 'no response', 'callback']
  },
  {
    id: 'serve_nosp',
    category: 'Legal Action',
    title: 'Serve Notice of Seeking Possession',
    text: 'Arrears exceed 4 weeks rent. Notice of Seeking Possession served to protect council interests.',
    tags: ['legal-action', 'urgent'],
    keywords: ['NOSP', 'notice', 'possession', 'arrears', 'legal', 'court', 'weeks rent']
  },
  {
    id: 'new_payment_arrangement',
    category: 'Payment Plans',
    title: 'New Payment Arrangement',
    text: 'Tenant has agreed to pay £[amount] per week/month starting from [date]. This arrangement will clear the arrears over [timeframe]. Letter sent confirming arrangement.',
    tags: ['payment-plan', 'contact-made'],
    keywords: ['payment plan', 'arrangement', 'agreed', 'arrears', 'confirmation', 'letter']
  },
  {
    id: 'financial_hardship',
    category: 'Circumstances',
    title: 'Financial Hardship',
    text: 'Tenant experiencing financial hardship due to [reason]. Discussed available support options including discretionary housing payments and debt advice referrals. Offered referral to Housing Support Team and employment support service.',
    tags: ['vulnerability', 'contact-made'],
    keywords: ['hardship', 'support', 'DHP', 'discretionary housing payments', 'debt advice', 'housing support', 'employment']
  },
  {
    id: 'universal_credit_changes',
    category: 'Benefits',
    title: 'Universal Credit Changes',
    text: 'Tenant reports changes in circumstances. MORE DETAILS. Advised to update UC journal and provide evidence to housing team.',
    tags: ['benefits', 'follow-up'],
    keywords: ['UC', 'universal credit', 'changes', 'circumstances', 'journal', 'evidence']
  },
  {
    id: 'change_in_employment',
    category: 'Circumstances',
    title: 'Change in Employment',
    text: 'Tenant reported change in employment. MORE DETAILS. Income affected from DATE. Discussed impact on rent payments and benefit entitlements. Offered referral to employment support service and welfare support.',
    tags: ['contact-made', 'follow-up'],
    keywords: ['employment', 'income', 'job', 'benefits', 'rent payments', 'employment support', 'welfare']
  },
  {
    id: 'legal_action_warning',
    category: 'Legal Action',
    title: 'Legal Action Warning',
    text: 'Tenant advised that current arrears (£Amount) may result in legal proceedings if not addressed urgently. Escalation procedures explained.',
    tags: ['legal-action', 'contact-made'],
    keywords: ['legal', 'proceedings', 'arrears', 'urgent', 'escalation', 'court']
  },
  {
    id: 'eviction_concerns',
    category: 'Legal Action',
    title: 'Eviction Concerns',
    text: 'Tenant expressed concerns about eviction. Advised of support services and importance of maintaining regular payments and contact with Rent team. Advised to significantly reduce debt and apply for a Stay of Eviction.',
    tags: ['legal-action', 'vulnerability', 'contact-made'],
    keywords: ['eviction', 'concerns', 'support', 'payments', 'debt', 'stay of eviction']
  },
  {
    id: 'partial_payment_made',
    category: 'Payments',
    title: 'Partial Payment Made',
    text: 'Payment of £AMOUNT received. £AMOUNT left to pay this period. Discussed plans to cover remainder, next payment of £AMOUNT expected on DATE',
    tags: ['payment-plan', 'contact-made'],
    keywords: ['partial payment', 'received', 'remainder', 'next payment', 'expected']
  },
  {
    id: 'payment_method_setup',
    category: 'Payments',
    title: 'Payment Method Setup',
    text: 'Direct debit set up for £[amount] starting [date]. Confirmation letter sent by post.',
    tags: ['payment-plan', 'contact-made'],
    keywords: ['direct debit', 'setup', 'confirmation', 'letter', 'payment method']
  },
  {
    id: 'successful_contact',
    category: 'Contact',
    title: 'Successful Contact',
    text: 'Spoke with Tenant regarding current arrears and the importance of maintaining regular payments. Outlined current rent charges and shortfall form benefits. Provided information on payment expectations and potential consequences of non-payment, including legal action if necessary. Offered support options and advised on how to access online account.',
    tags: ['contact-made', 'follow-up'],
    keywords: ['contact', 'arrears', 'payments', 'charges', 'shortfall', 'benefits', 'legal action', 'support', 'online account']
  },
  {
    id: 'repair_issues_affecting_payment',
    category: 'Property',
    title: 'Repair Issues Affecting Payment',
    text: 'Tenant noted repairs issues including [details]. Advised to contact repairs team to formally report issues. Discussed possible compensation and impact on current arrears.',
    tags: ['dispute', 'follow-up'],
    keywords: ['repairs', 'issues', 'report', 'compensation', 'arrears', 'property']
  },
  {
    id: 'tenant_moving_out',
    category: 'Tenancy',
    title: 'Tenant Moving Out',
    text: 'Tenant wants to relinquish property. Advised on relinquishment process and to contact Housing Officer for more details.',
    tags: ['contact-made', 'follow-up'],
    keywords: ['moving out', 'relinquish', 'property', 'housing officer', 'tenancy']
  },
  {
    id: 'deceased_tenant',
    category: 'Tenancy',
    title: 'Deceased Tenant',
    text: 'Tenant reported deceased on [date]. Death certificate required. Succession rights discussed with [family member]. Estate contact: [details].',
    tags: ['urgent', 'follow-up'],
    keywords: ['deceased', 'death certificate', 'succession', 'estate', 'family']
  },
  {
    id: 'mental_health_support',
    category: 'Circumstances',
    title: 'Mental Health Support',
    text: 'Tenant disclosed mental health concerns affecting ability to manage finances. Signposted to appropriate support services. Considered reasonable adjustments such as [details].',
    tags: ['vulnerability', 'contact-made', 'follow-up'],
    keywords: ['mental health', 'finances', 'support services', 'reasonable adjustments', 'vulnerability']
  },
  {
    id: 'no_contact_established',
    category: 'Contact',
    title: 'No Contact Established',
    text: 'Multiple contact attempts unsuccessful including calls/letters/emails. Referred for home visit.',
    tags: ['follow-up', 'urgent'],
    keywords: ['no contact', 'attempts', 'calls', 'letters', 'emails', 'home visit']
  },
  {
    id: 'payment_plan_breach',
    category: 'Payment Plans',
    title: 'Payment Plan Breach',
    text: 'Payment arrangement breached. Shortfall from expected payments: £[amount]. Contacted tenant to discuss. Advised of need to restore plan to prevent further action being taken. Tenant to make payment of £Amount on [date]',
    tags: ['payment-plan', 'follow-up', 'contact-made'],
    keywords: ['breach', 'arrangement', 'shortfall', 'restore', 'further action', 'payment']
  },
  {
    id: 'refund_request',
    category: 'Payments',
    title: 'Refund request',
    text: 'Tenant requesting £Amount. Tenant has £Amount available. No outstanding invoices. Request sent to management and acknowledgment sent to tenant by email.',
    tags: ['contact-made', 'follow-up'],
    keywords: ['refund', 'request', 'available', 'management', 'acknowledgment', 'email']
  }
];

// Default talking points
const defaultTalkingPoints: TalkingPoint[] = [
  {
    id: 'alternative_contact',
    category: 'Contact Methods',
    title: 'Use alternative contact',
    content: 'Consider alternative phone numbers, sending email, letter or text. A home visit may be necessary',
    keywords: ['contact', 'phone', 'email', 'letter', 'text', 'visit', 'alternative', 'reach']
  },
  {
    id: 'direct_payments',
    category: 'Payment Support',
    title: 'Apply for direct payments',
    content: 'Contact Universal Credit to discuss managed payments to landlord',
    keywords: ['UC', 'universal credit', 'managed payments', 'direct payments', 'landlord', 'APA']
  },
  {
    id: 'payment_arrangement',
    category: 'Payment Support',
    title: 'Offer payment arrangement',
    content: 'Aim to clear debt within 6 months',
    keywords: ['payment plan', 'arrangement', 'debt', 'installments', 'clear', 'months']
  },
  {
    id: 'payment_options',
    category: 'Payment Support',
    title: 'Explain rent payment options',
    content: 'Phone, automated line, online, Standing order, direct debit, pay point',
    keywords: ['payment', 'phone', 'online', 'standing order', 'direct debit', 'pay point', 'automated']
  },
  {
    id: 'discretionary_housing_payment',
    category: 'Financial Support',
    title: 'Discretionary Housing Payment',
    content: 'https://www.lambeth.gov.uk/benefits-financial-support/extra-support-people-crisis/discretionary-housing-payment-dhp',
    keywords: ['DHP', 'discretionary housing payment', 'financial support', 'shortfall', 'lambeth']
  },
  {
    id: 'debt_advice_referral',
    category: 'Financial Support',
    title: 'Debt advice referral',
    content: 'I can refer you to free debt advice services who can help review your finances and create a manageable budget. They offer confidential support. Every Pound Counts, Citizen\'s Advice Bureau, Centre70, Brixton Advice Centre',
    keywords: ['debt', 'advice', 'budget', 'finances', 'every pound counts', 'citizens advice', 'centre70', 'brixton']
  },
  {
    id: 'benefit_health_check',
    category: 'Benefits',
    title: 'Suggest benefit health check',
    content: 'Offer referral to Housing Support Team. Advise to review Lightning reach: https://www.lightningreach.org/',
    keywords: ['benefits', 'health check', 'housing support', 'lightning reach', 'entitlements']
  },
  {
    id: 'food_fuel_vouchers',
    category: 'Support Services',
    title: 'Food and Fuel vouchers',
    content: 'Offer referral to Housing Support Team. Advise to review www.lambeth.gov.uk/costofliving',
    keywords: ['food', 'fuel', 'vouchers', 'housing support', 'cost of living', 'lambeth']
  },
  {
    id: 'employment_support',
    category: 'Support Services',
    title: 'Offer employment support',
    content: 'We have employment support services available to help with job searching, CV writing, and interview preparation. Would you like me to arrange a referral?',
    keywords: ['employment', 'job', 'CV', 'interview', 'training', 'support', 'work']
  },
  {
    id: 'lightning_reach',
    category: 'Support Services',
    title: 'Refer to Lightning Reach',
    content: 'www.lightningreach.org',
    keywords: ['lightning reach', 'support', 'referral', 'services']
  },
  {
    id: 'universal_credit',
    category: 'Benefits',
    title: 'Suggest applying for Universal Credit',
    content: 'UC Helpline 0800 328 5644. Refer to Housing Support Team or Citizens Advice Bureau for help to claim UC. Based on your circumstances, you may be entitled to Universal Credit. This could help with housing costs and living expenses. Would you like help with the application?',
    keywords: ['UC', 'universal credit', 'helpline', 'housing support', 'citizens advice', 'application', 'benefits']
  },
  {
    id: 'benefits_shortfall',
    category: 'Benefits',
    title: 'Benefits Shortfall',
    content: 'Reasons for shortfall - common deductions outlined in Account Charges tab',
    keywords: ['benefits', 'shortfall', 'deductions', 'account charges', 'housing benefit']
  },
  {
    id: 'welfare_check',
    category: 'Support Services',
    title: 'Welfare check',
    content: 'Consider referral to Lambeth Single Point of Access, Adult Social Care, Every Pound Counts and Housing Support Team',
    keywords: ['welfare', 'check', 'lambeth', 'single point of access', 'adult social care', 'every pound counts', 'housing support']
  },
  {
    id: 'vulnerability_identified',
    category: 'Support Services',
    title: 'Vulnerability identified',
    content: 'Signpost appropriate support. Flag case for additional support. Consider safeguarding referral.',
    keywords: ['vulnerability', 'support', 'safeguarding', 'additional support', 'flag', 'referral']
  }
];

// Browser check for SSR compatibility
const isBrowser = typeof window !== 'undefined';

// UUID generation that works in both browser and SSR environments
function generateId(): string {
  if (isBrowser && typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for SSR or older browsers
  return 'xxxx-xxxx-4xxx-yxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Helper function to get stored settings
function getStoredSettings(): Settings {
  if (!isBrowser) return { tags: defaultTags, responseTemplates: defaultResponseTemplates, talkingPoints: defaultTalkingPoints };
  try {
    const stored = localStorage.getItem('appSettings');
    if (stored) {
      const parsed = JSON.parse(stored);
      // Ensure we have default values if stored settings are incomplete
      return {
        tags: parsed.tags || defaultTags,
        responseTemplates: parsed.responseTemplates || defaultResponseTemplates,
        talkingPoints: parsed.talkingPoints || defaultTalkingPoints
      };
    }
    return { tags: defaultTags, responseTemplates: defaultResponseTemplates, talkingPoints: defaultTalkingPoints };
  } catch {
    return { tags: defaultTags, responseTemplates: defaultResponseTemplates, talkingPoints: defaultTalkingPoints };
  }
}

// Settings store with localStorage persistence
export const settings = writable<Settings>(getStoredSettings());

// Subscribe to changes and persist to localStorage
if (isBrowser) {
  settings.subscribe(settingsData => {
    localStorage.setItem('appSettings', JSON.stringify(settingsData));
  });
}

// Helper functions for managing settings
export function addTag(tag: Omit<Tag, 'id'>): void {
  settings.update(current => ({
    ...current,
    tags: [...current.tags, { ...tag, id: generateId() }]
  }));
}

export function updateTag(id: string, updates: Partial<Tag>): void {
  settings.update(current => ({
    ...current,
    tags: current.tags.map(tag => tag.id === id ? { ...tag, ...updates } : tag)
  }));
}

export function deleteTag(id: string): void {
  settings.update(current => ({
    ...current,
    tags: current.tags.filter(tag => tag.id !== id)
  }));
}

export function addResponseTemplate(template: Omit<ResponseTemplate, 'id'>): void {
  settings.update(current => ({
    ...current,
    responseTemplates: [...current.responseTemplates, { ...template, id: generateId() }]
  }));
}

export function updateResponseTemplate(id: string, updates: Partial<ResponseTemplate>): void {
  settings.update(current => ({
    ...current,
    responseTemplates: current.responseTemplates.map(template => 
      template.id === id ? { ...template, ...updates } : template
    )
  }));
}

export function deleteResponseTemplate(id: string): void {
  settings.update(current => ({
    ...current,
    responseTemplates: current.responseTemplates.filter(template => template.id !== id)
  }));
}

export function addTalkingPoint(talkingPoint: Omit<TalkingPoint, 'id'>): void {
  settings.update(current => ({
    ...current,
    talkingPoints: [...current.talkingPoints, { ...talkingPoint, id: generateId() }]
  }));
}

export function updateTalkingPoint(id: string, updates: Partial<TalkingPoint>): void {
  settings.update(current => ({
    ...current,
    talkingPoints: current.talkingPoints.map(talkingPoint => 
      talkingPoint.id === id ? { ...talkingPoint, ...updates } : talkingPoint
    )
  }));
}

export function deleteTalkingPoint(id: string): void {
  settings.update(current => ({
    ...current,
    talkingPoints: current.talkingPoints.filter(talkingPoint => talkingPoint.id !== id)
  }));
}

export function resetToDefaults(): void {
  settings.set({ tags: defaultTags, responseTemplates: defaultResponseTemplates, talkingPoints: defaultTalkingPoints });
}