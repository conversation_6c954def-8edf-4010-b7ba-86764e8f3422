<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { settings } from '$lib/stores/settings';
  
  const dispatch = createEventDispatcher();
  
  export let notesContent: string = '';
  let searchQuery = '';
  
  // Debounce notes content processing for performance
  let debouncedNotesContent = '';
  let debounceTimer: number;
  
  $: {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      debouncedNotesContent = notesContent;
    }, 300);
  }

  // Helper function to normalize words for singular/plural matching
  function normalizeWord(word: string): string {
    const lowerWord = word.toLowerCase().trim();
    
    // Remove common suffixes to get word stems
    if (lowerWord.endsWith('ies') && lowerWord.length > 4) {
      return lowerWord.slice(0, -3) + 'y'; // companies -> company
    }
    if (lowerWord.endsWith('es') && lowerWord.length > 3) {
      // Check for words ending in -ches, -shes, -xes, etc.
      if (lowerWord.endsWith('ches') || lowerWord.endsWith('shes') || lowerWord.endsWith('xes')) {
        return lowerWord.slice(0, -2);
      }
      return lowerWord.slice(0, -2); // boxes -> box
    }
    if (lowerWord.endsWith('s') && lowerWord.length > 3) {
      return lowerWord.slice(0, -1); // repairs -> repair
    }
    
    return lowerWord;
  }

  // Helper function to check if two words match (accounting for singular/plural)
  function wordsMatch(word1: string, word2: string): boolean {
    const normalized1 = normalizeWord(word1);
    const normalized2 = normalizeWord(word2);
    
    // Direct match
    if (word1.toLowerCase() === word2.toLowerCase()) return true;
    
    // Normalized match (singular/plural)
    if (normalized1 === normalized2) return true;
    
    // One contains the other (for partial matches)
    if (normalized1.length > 2 && normalized2.length > 2) {
      if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) return true;
    }
    
    return false;
  }

  // Relevance scoring algorithm (adapted from StandardResponses)
  function calculateRelevanceScore(talkingPoint: any, notesText: string): number {
    if (!notesText || notesText.trim().length < 3) return 0;
    
    let score = 0;
    const lowerNotesText = notesText.toLowerCase();
    const notesWords = lowerNotesText.split(/\s+/).filter(word => word.length > 2);
    
    // Keyword matching (50 points max)
    if (talkingPoint.keywords) {
      talkingPoint.keywords.forEach((keyword: string) => {
        const lowerKeyword = keyword.toLowerCase();
        
        // Direct text inclusion (highest score)
        if (lowerNotesText.includes(lowerKeyword)) {
          score += 12;
          return;
        }
        
        // Check for singular/plural and word stem matches
        let foundMatch = false;
        notesWords.forEach(notesWord => {
          if (wordsMatch(notesWord, lowerKeyword)) {
            score += 10; // High score for normalized matches
            foundMatch = true;
          }
        });
        
        // If no normalized match, try partial matching
        if (!foundMatch) {
          notesWords.forEach(notesWord => {
            if (notesWord.includes(lowerKeyword) || lowerKeyword.includes(notesWord)) {
              if (notesWord.length > 2 && lowerKeyword.length > 2) {
                score += 4;
              }
            }
          });
        }
      });
    }
    
    // Title matching (25 points max)
    const lowerTitle = talkingPoint.title.toLowerCase();
    if (lowerNotesText.includes(lowerTitle)) {
      score += 25;
    } else {
      // Enhanced title matching with singular/plural handling
      const titleWords = lowerTitle.split(/\s+/).filter((word: string) => word.length > 2);
      titleWords.forEach((titleWord: string) => {
        // Check for direct inclusion first
        if (lowerNotesText.includes(titleWord)) {
          score += 10;
          return;
        }
        
        // Check for singular/plural matches
        notesWords.forEach(notesWord => {
          if (wordsMatch(notesWord, titleWord)) {
            score += 8;
          }
        });
      });
    }
    
    // Category matching (25 points max)
    const lowerCategory = talkingPoint.category.toLowerCase();
    if (lowerNotesText.includes(lowerCategory)) {
      score += 25;
    } else {
      // Enhanced category matching with singular/plural handling
      const categoryWords = lowerCategory.split(/\s+/).filter((word: string) => word.length > 2);
      categoryWords.forEach((categoryWord: string) => {
        // Check for direct inclusion first
        if (lowerNotesText.includes(categoryWord)) {
          score += 10;
          return;
        }
        
        // Check for singular/plural matches
        notesWords.forEach(notesWord => {
          if (wordsMatch(notesWord, categoryWord)) {
            score += 8;
          }
        });
      });
    }
    
    return Math.min(Math.round(score), 100);
  }

  interface TalkingPointWithRelevance {
    id: string;
    category: string;
    title: string;
    content: string;
    keywords?: string[];
    relevanceScore: number;
    relevanceLevel: 'high' | 'medium' | 'low';
  }

  // Add relevance scores to talking points
  $: talkingPointsWithRelevance = $settings.talkingPoints.map((talkingPoint): TalkingPointWithRelevance => ({
    ...talkingPoint,
    relevanceScore: calculateRelevanceScore(talkingPoint, debouncedNotesContent),
    relevanceLevel: getRelevanceLevel(calculateRelevanceScore(talkingPoint, debouncedNotesContent)),
  }));

  // Helper function to determine relevance level for styling
  function getRelevanceLevel(score: number): 'high' | 'medium' | 'low' {
    if (score >= 60) return 'high';
    if (score >= 30) return 'medium';
    return 'low';
  }

  // Sort by relevance score (highest first), then by title
  $: sortedTalkingPoints = [...talkingPointsWithRelevance].sort((a, b) => {
    if (a.relevanceScore !== b.relevanceScore) {
      return b.relevanceScore - a.relevanceScore;
    }
    return a.title.localeCompare(b.title);
  });

  // Tooltip state
  let hoveredPoint: TalkingPointWithRelevance | null = null;
  let tooltipX = 0;
  let tooltipY = 0;
  let tooltipVisible = false;

  function showTooltip(event: MouseEvent, point: TalkingPointWithRelevance) {
    hoveredPoint = point;
    
    // Position tooltip relative to mouse
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const estimatedTooltipWidth = 350;
    const estimatedTooltipHeight = 200;
    
    // Calculate horizontal position
    tooltipX = rect.right + 10; // Start by positioning to the right of the item
    if (tooltipX + estimatedTooltipWidth > windowWidth - 20) {
      tooltipX = rect.left - estimatedTooltipWidth - 10; // Position to the left if no space on right
    }
    
    // Calculate vertical position - prefer above for lower screen elements
    const elementCenterY = rect.top + (rect.height / 2);
    const screenMiddle = windowHeight / 2;
    
    if (elementCenterY > screenMiddle) {
      // Element is in lower half of screen - position tooltip above
      tooltipY = rect.top - estimatedTooltipHeight - 10;
      
      // If tooltip would go above viewport, position below instead
      if (tooltipY < 10) {
        tooltipY = rect.bottom + 10;
      }
    } else {
      // Element is in upper half of screen - position tooltip below
      tooltipY = rect.bottom + 10;
      
      // If tooltip would go below viewport, position above instead
      if (tooltipY + estimatedTooltipHeight > windowHeight - 20) {
        tooltipY = rect.top - estimatedTooltipHeight - 10;
      }
    }
    
    // Final boundary check to ensure tooltip stays on screen
    tooltipY = Math.max(10, Math.min(tooltipY, windowHeight - estimatedTooltipHeight - 20));
    
    tooltipVisible = true;
  }

  function hideTooltip() {
    tooltipVisible = false;
    hoveredPoint = null;
  }

</script>

<div class="talking-points">
  <div class="talking-points-header">
    <h4>Talking Points</h4>
  </div>
  
  <div class="points-list">
    {#if debouncedNotesContent.trim().length < 3}
      <div class="no-results">
        <i class="fas fa-edit"></i>
        <p>Start typing a note to see talking point suggestions</p>
      </div>
    {:else}
      {@const relevantPoints = sortedTalkingPoints.filter(point => point.relevanceScore >= 30)}
      {#if relevantPoints.length > 0}
        {#each relevantPoints as point}
          <div 
            class="talking-point-item"
            class:relevance-high={point.relevanceLevel === 'high'}
            class:relevance-medium={point.relevanceLevel === 'medium'}
            class:relevance-low={point.relevanceLevel === 'low'}
            on:mouseenter={(e) => showTooltip(e, point)}
            on:mouseleave={hideTooltip}
          >
            <div class="point-header">
              <div class="point-title">{point.title}</div>
              <div class="relevance-indicator">
                {#if point.relevanceLevel === 'high'}
                  <span class="relevance-badge high">🎯 {point.relevanceScore}%</span>
                {:else if point.relevanceLevel === 'medium'}
                  <span class="relevance-badge medium">⚡ {point.relevanceScore}%</span>
                {/if}
              </div>
            </div>
          </div>
        {/each}
      {:else}
        <div class="no-results">
          <i class="fas fa-lightbulb"></i>
          <p>No relevant talking points for current notes</p>
        </div>
      {/if}
    {/if}
  </div>
</div>

<!-- Hover Tooltip -->
{#if tooltipVisible && hoveredPoint}
  <div 
    class="talking-point-tooltip"
    style="left: {tooltipX}px; top: {tooltipY}px;"
  >
    <div class="tooltip-header">
      <h5>{hoveredPoint.title}</h5>
      <div class="tooltip-category">{hoveredPoint.category}</div>
    </div>
    <div class="tooltip-content">
      {hoveredPoint.content}
    </div>
    {#if hoveredPoint.keywords && hoveredPoint.keywords.length > 0}
      <div class="tooltip-keywords">
        <strong>Keywords:</strong> {hoveredPoint.keywords.join(', ')}
      </div>
    {/if}
  </div>
{/if}

<style>
  .talking-points {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 0.375rem;
    border: 1px solid var(--border);
  }

  .talking-points-header {
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid var(--border);
  }

  .talking-points-header h4 {
    margin: 0;
    color: #374151;
    font-size: 0.75rem;
    font-weight: 600;
  }


  .points-list {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    overflow-y: auto;
    flex: 1;
  }

  .talking-point-item {
    background: white;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    padding: 0.375rem;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .talking-point-item:hover {
    background: #f8fafc;
    border-color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .point-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .point-title {
    font-weight: 500;
    color: #111827;
    font-size: 0.75rem;
    line-height: 1.2;
    flex: 1;
  }

  .relevance-indicator {
    flex-shrink: 0;
  }

  .relevance-badge {
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    gap: 0.125rem;
  }

  .relevance-badge.high {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
  }

  .relevance-badge.medium {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
  }

  .talking-point-item.relevance-high {
    border-left: 3px solid #10b981;
  }

  .talking-point-item.relevance-medium {
    border-left: 3px solid #f59e0b;
  }

  .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem 1rem;
    text-align: center;
    color: #6b7280;
  }

  .no-results i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
  }

  .no-results p {
    margin: 0;
    font-size: var(--label-size);
    font-weight: 500;
  }

  /* Tooltip Styles */
  .talking-point-tooltip {
    position: fixed;
    z-index: 1000;
    background: white;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    max-width: 350px;
    min-width: 300px;
    animation: tooltipFadeIn 0.2s ease-out;
    pointer-events: none;
  }

  @keyframes tooltipFadeIn {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .tooltip-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border);
  }

  .tooltip-header h5 {
    margin: 0 0 0.25rem 0;
    color: #111827;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .tooltip-category {
    color: var(--color-primary);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .tooltip-content {
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
    white-space: pre-wrap;
  }

  .tooltip-keywords {
    padding-top: 0.5rem;
    border-top: 1px solid #f3f4f6;
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.4;
  }

  .tooltip-keywords strong {
    color: #374151;
  }
</style>