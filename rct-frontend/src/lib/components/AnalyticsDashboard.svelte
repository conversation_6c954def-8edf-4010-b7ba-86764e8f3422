<script lang="ts">
  import { onMount } from 'svelte';
  import { authToken } from '$lib/stores';
  import { get } from 'svelte/store';
  import type { CaseData } from '$lib/utils/api';
  import { 
    calculatePortfolioMetrics, 
    analyzeContactEffectiveness, 
    getUrgentAttentionCases,
    generateTimeSeriesData,
    formatCurrency,
    formatPercentage,
    type PortfolioMetrics,
    type ContactEffectivenessMetrics,
    type TenantAnalytics,
    type TimeSeriesData
  } from '$lib/utils/analytics';
  import MetricCard from './MetricCard.svelte';
  import StatCard from './StatCard.svelte';
  import DonutChart from './DonutChart.svelte';
  import ProgressBar from './ProgressBar.svelte';

  export let isFullView: boolean = false;

  let caseData: CaseData[] = [];
  let portfolioMetrics: PortfolioMetrics | null = null;
  let contactEffectiveness: ContactEffectivenessMetrics[] = [];
  let urgentCases: TenantAnalytics[] = [];
  let timeSeriesData: TimeSeriesData[] = [];
  let isLoading = true;
  let selectedTimeframe = 30; // days

  // Filter options
  let selectedRiskFilter = 'all';
  let selectedEscalationFilter = 'all';
  let minBalanceFilter = 0;

  const riskOptions = [
    { value: 'all', label: 'All Risk Levels' },
    { value: 'critical', label: 'Critical Risk' },
    { value: 'high', label: 'High Risk' },
    { value: 'medium', label: 'Medium Risk' },
    { value: 'low', label: 'Low Risk' }
  ];

  const escalationOptions = [
    { value: 'all', label: 'All Escalation Levels' },
    { value: 'LEGAL', label: 'Legal Action' },
    { value: 'NSP', label: 'NSP Required' },
    { value: 'CONTACT_REQUIRED', label: 'Contact Required' },
    { value: 'NONE', label: 'No Escalation' }
  ];

  const timeframeOptions = [
    { value: 7, label: 'Last 7 days' },
    { value: 30, label: 'Last 30 days' },
    { value: 60, label: 'Last 60 days' },
    { value: 90, label: 'Last 90 days' }
  ];

  async function loadCaseData() {
    isLoading = true;
    try {
      const { getCases } = await import('$lib/utils/api');
      const token = get(authToken);
      
      if (token) {
        console.log('Analytics: Loading cases with auth token');
        caseData = await getCases(token);
        console.log('Analytics: Loaded', caseData.length, 'cases');
      } else {
        console.log('Analytics: No auth token, using mock data');
        loadMockCaseData();
      }
      
      calculateAnalytics();
    } catch (error) {
      console.error('Analytics: Failed to load cases:', error);
      console.log('Analytics: Falling back to mock data');
      loadMockCaseData();
      calculateAnalytics();
    } finally {
      isLoading = false;
    }
  }

  function loadMockCaseData() {
    // Create mock case data for demo
    caseData = [
      {
        id: 'mock-1',
        tenantId: 'tenant-1',
        userId: 'user-1',
        reference: 'TC001',
        currentBalance: 350.75,
        weeklyRent: 85.50,
        tenantMonthlyPayment: 200.00,
        apaHbMonthlyPayment: 100.00,
        tpdMonthlyPayment: 0,
        tenantWeeklyPayment: 46.15,
        benefitsHbWeeklyPayment: 39.35,
        weeksToNextPay: 2,
        paymentDue: 171.00,
        timeframe: 12,
        useWeeklyFrequency: false,
        grossWeeklyRent: 85.50,
        rentComponent: 75.50,
        nonUcServiceChargeTotal: 10.00,
        grossWeeklyRentOverridden: false,
        notes: 'High risk case - multiple missed payments',
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-2',
        tenantId: 'tenant-2',
        userId: 'user-1',
        reference: 'TC002',
        currentBalance: 125.00,
        weeklyRent: 92.00,
        tenantMonthlyPayment: 398.67,
        apaHbMonthlyPayment: 0,
        tpdMonthlyPayment: 0,
        tenantWeeklyPayment: 92.00,
        benefitsHbWeeklyPayment: 0,
        weeksToNextPay: 1,
        paymentDue: 92.00,
        timeframe: 0,
        useWeeklyFrequency: true,
        grossWeeklyRent: 92.00,
        rentComponent: 82.00,
        nonUcServiceChargeTotal: 10.00,
        grossWeeklyRentOverridden: false,
        notes: 'Improving payment pattern',
        createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-3',
        tenantId: 'tenant-3',
        userId: 'user-1',
        reference: 'TC003',
        currentBalance: 750.25,
        weeklyRent: 78.00,
        tenantMonthlyPayment: 150.00,
        apaHbMonthlyPayment: 188.00,
        tpdMonthlyPayment: 0,
        tenantWeeklyPayment: 35.00,
        benefitsHbWeeklyPayment: 43.00,
        weeksToNextPay: 3,
        paymentDue: 234.00,
        timeframe: 8,
        useWeeklyFrequency: false,
        grossWeeklyRent: 78.00,
        rentComponent: 68.00,
        nonUcServiceChargeTotal: 10.00,
        grossWeeklyRentOverridden: false,
        notes: 'Critical case - legal action required',
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-4',
        tenantId: 'tenant-4',
        userId: 'user-1',
        reference: 'TC004',
        currentBalance: -25.00,
        weeklyRent: 88.50,
        tenantMonthlyPayment: 383.50,
        apaHbMonthlyPayment: 0,
        tpdMonthlyPayment: 0,
        tenantWeeklyPayment: 88.50,
        benefitsHbWeeklyPayment: 0,
        weeksToNextPay: 1,
        paymentDue: 0,
        timeframe: 0,
        useWeeklyFrequency: true,
        grossWeeklyRent: 88.50,
        rentComponent: 78.50,
        nonUcServiceChargeTotal: 10.00,
        grossWeeklyRentOverridden: false,
        notes: 'Account in credit - good payer',
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-5',
        tenantId: 'tenant-5',
        userId: 'user-1',
        reference: 'TC005',
        currentBalance: 180.50,
        weeklyRent: 95.00,
        tenantMonthlyPayment: 200.00,
        apaHbMonthlyPayment: 211.67,
        tpdMonthlyPayment: 0,
        tenantWeeklyPayment: 46.15,
        benefitsHbWeeklyPayment: 48.85,
        weeksToNextPay: 2,
        paymentDue: 190.00,
        timeframe: 4,
        useWeeklyFrequency: false,
        grossWeeklyRent: 95.00,
        rentComponent: 85.00,
        nonUcServiceChargeTotal: 10.00,
        grossWeeklyRentOverridden: false,
        notes: 'Medium risk - monitoring required',
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];
  }

  function calculateAnalytics() {
    if (!caseData || caseData.length === 0) {
      portfolioMetrics = null;
      contactEffectiveness = [];
      urgentCases = [];
      timeSeriesData = [];
      return;
    }

    try {
      portfolioMetrics = calculatePortfolioMetrics(caseData);
      contactEffectiveness = analyzeContactEffectiveness(caseData)
        .sort((a, b) => b.reductionPerContact - a.reductionPerContact);
      urgentCases = getUrgentAttentionCases(caseData).slice(0, 15);
      timeSeriesData = generateTimeSeriesData(caseData, selectedTimeframe);
    } catch (error) {
      console.error('Analytics calculation error:', error);
    }
  }

  $: filteredContactEffectiveness = contactEffectiveness.filter(contact => {
    if (minBalanceFilter > 0) {
      const caseItem = caseData.find(c => c.reference === contact.tenantReference);
      const balance = caseItem?.currentBalance || 0;
      if (balance < minBalanceFilter) return false;
    }
    return true;
  });

  function getRiskCategoryColor(risk: string): string {
    switch (risk) {
      case 'critical': return 'var(--color-red)';
      case 'high': return 'var(--color-orange)';
      case 'medium': return 'var(--color-yellow)';
      case 'low': return 'var(--color-green)';
      default: return 'var(--color-grey)';
    }
  }

  function getEscalationColor(escalation: string): string {
    switch (escalation) {
      case 'LEGAL': return 'var(--color-red)';
      case 'NSP': return 'var(--color-orange)';
      case 'CONTACT_REQUIRED': return 'var(--color-yellow)';
      case 'NONE': return 'var(--color-green)';
      default: return 'var(--color-grey)';
    }
  }

  onMount(() => {
    loadCaseData();
  });

  // Recalculate when timeframe changes
  $: if (caseData.length > 0) {
    timeSeriesData = generateTimeSeriesData(caseData, selectedTimeframe);
  }
</script>

<div class="analytics-dashboard" class:full-view={isFullView}>
  <div class="dashboard-header">
    <h2>Portfolio Analytics & Insights</h2>
    <div class="filters">
      <select bind:value={selectedTimeframe}>
        {#each timeframeOptions as option}
          <option value={option.value}>{option.label}</option>
        {/each}
      </select>
      <select bind:value={selectedRiskFilter}>
        {#each riskOptions as option}
          <option value={option.value}>{option.label}</option>
        {/each}
      </select>
      <select bind:value={selectedEscalationFilter}>
        {#each escalationOptions as option}
          <option value={option.value}>{option.label}</option>
        {/each}
      </select>
      <input 
        type="number" 
        placeholder="Min balance £"
        bind:value={minBalanceFilter}
        min="0"
        step="100"
      />
    </div>
  </div>

  {#if isLoading}
    <div class="loading-state">
      <div class="spinner"></div>
      <p>Calculating analytics...</p>
    </div>
  {:else if !portfolioMetrics}
    <div class="empty-state">
      <i class="fas fa-chart-bar"></i>
      <h3>No Data Available</h3>
      <p>Start managing cases to see portfolio analytics and insights.</p>
    </div>
  {:else}
    <div class="analytics-grid">
      <!-- Portfolio Overview -->
      <div class="overview-grid">
        <StatCard 
          label="Total Debt" 
          value={formatCurrency(portfolioMetrics.totalDebt)} 
          color="danger"
          icon="fa-pound-sign"
          size="medium"
        />
        <StatCard 
          label="Total Cases" 
          value={portfolioMetrics.totalCases} 
          subtitle="Active portfolio"
          icon="fa-folder-open"
        />
        <StatCard 
          label="Average Debt" 
          value={formatCurrency(portfolioMetrics.averageDebt)} 
          subtitle="Per case"
          icon="fa-calculator"
        />
        <StatCard 
          label="Total Contacts" 
          value={portfolioMetrics.totalContacts} 
          subtitle="Estimated interactions"
          icon="fa-phone"
        />
        <StatCard 
          label="Contacts/Case" 
          value={portfolioMetrics.averageContactsPerCase.toFixed(1)} 
          subtitle="Average rate"
          icon="fa-chart-line"
        />
        <StatCard 
          label="Health Score" 
          value="{portfolioMetrics.portfolioHealthScore}/100"
          color={portfolioMetrics.portfolioHealthScore >= 80 ? 'success' : portfolioMetrics.portfolioHealthScore >= 60 ? 'warning' : 'danger'}
          icon="fa-heartbeat"
        />
      </div>

      <!-- Risk & Escalation Combined -->
      <div class="risk-escalation-container">
        <!-- Risk Segmentation -->
        <MetricCard title="Risk Segmentation" icon="fa-exclamation-triangle">
          <DonutChart 
            data={Object.entries(portfolioMetrics.casesByRisk).map(([risk, count]) => ({
              label: `${risk.charAt(0).toUpperCase() + risk.slice(1)} Risk`,
              value: count,
              color: getRiskCategoryColor(risk)
            }))}
            size={110}
            strokeWidth={18}
            centerText="Cases"
            centerValue={portfolioMetrics.totalCases.toString()}
            showLabels={true}
            showValues={true}
            showPercentages={true}
          />
        </MetricCard>

        <!-- Escalation Status -->
        <MetricCard title="Escalation Status" icon="fa-gavel">
          <DonutChart 
            data={Object.entries(portfolioMetrics.casesByEscalation).map(([escalation, count]) => ({
              label: escalation === 'NONE' ? 'No Action' : escalation,
              value: count,
              color: getEscalationColor(escalation)
            }))}
            size={110}
            strokeWidth={18}
            centerText="Cases"
            centerValue={portfolioMetrics.totalCases.toString()}
            showLabels={true}
            showValues={true}
            showPercentages={true}
          />
        </MetricCard>

        <!-- Portfolio Trend -->
        <MetricCard title="Portfolio Trend ({selectedTimeframe} days)" icon="fa-chart-line">
          <div class="trend-summary">
            {#if timeSeriesData.length > 1}
              {@const firstDay = timeSeriesData[0]}
              {@const lastDay = timeSeriesData[timeSeriesData.length - 1]}
              {@const debtChange = lastDay.totalDebt - firstDay.totalDebt}
              {@const contactsTotal = timeSeriesData.reduce((sum, day) => sum + day.totalContacts, 0)}
              <div class="trend-metrics">
                <StatCard 
                  label="Debt Change"
                  value={formatCurrency(debtChange)}
                  color={debtChange < 0 ? 'success' : 'danger'}
                  trend={debtChange < 0 ? 'down' : debtChange > 0 ? 'up' : 'neutral'}
                  trendValue={formatPercentage(firstDay.totalDebt > 0 ? (debtChange / firstDay.totalDebt) * 100 : 0)}
                  size="small"
                />
                <StatCard 
                  label="Total Contacts"
                  value={contactsTotal}
                  subtitle="Period total"
                  size="small"
                />
                <StatCard 
                  label="Daily Average"
                  value={Math.round(contactsTotal / selectedTimeframe)}
                  subtitle="Contacts/day"
                  size="small"
                />
              </div>
            {:else}
              <p>Insufficient data for trend analysis</p>
            {/if}
          </div>
        </MetricCard>
      </div>

      <!-- Contact Effectiveness -->
      <MetricCard title="Contact Effectiveness (Top 10)" icon="fa-phone">
        <div class="effectiveness-table">
          <div class="table-header">
            <span>Tenant Ref</span>
            <span>Contacts</span>
            <span>Balance Reduction</span>
            <span>Per Contact</span>
            <span>Response Rate</span>
          </div>
          {#each filteredContactEffectiveness.slice(0, 10) as contact}
            <div class="table-row">
              <span class="tenant-ref">{contact.tenantReference}</span>
              <span>{contact.totalContacts}</span>
              <span class="balance-change" class:positive={contact.balanceReduction > 0} class:negative={contact.balanceReduction < 0}>
                {formatCurrency(contact.balanceReduction)}
              </span>
              <span class="per-contact" class:positive={contact.reductionPerContact > 0} class:negative={contact.reductionPerContact < 0}>
                {formatCurrency(contact.reductionPerContact)}
              </span>
              <span class="response-rate">
                <ProgressBar 
                  value={contact.responseRate * 100} 
                  max={100} 
                  color="var(--color-primary)"
                  height="16px"
                  showLabel={false}
                />
                <span class="rate-text">{Math.round(contact.responseRate * 100)}%</span>
              </span>
            </div>
          {/each}
        </div>
      </MetricCard>

      <!-- Contact Effectiveness Bottom 10 -->
      <MetricCard title="Contact Effectiveness (Bottom 10)" icon="fa-exclamation-triangle" variant="warning">
        <div class="effectiveness-table">
          <div class="table-header">
            <span>Tenant Ref</span>
            <span>Contacts</span>
            <span>Balance Reduction</span>
            <span>Per Contact</span>
            <span>Response Rate</span>
          </div>
          {#each filteredContactEffectiveness.slice(-10).reverse() as contact}
            <div class="table-row bottom-performer">
              <span class="tenant-ref">{contact.tenantReference}</span>
              <span>{contact.totalContacts}</span>
              <span class="balance-change" class:positive={contact.balanceReduction > 0} class:negative={contact.balanceReduction < 0}>
                {formatCurrency(contact.balanceReduction)}
              </span>
              <span class="per-contact" class:positive={contact.reductionPerContact > 0} class:negative={contact.reductionPerContact < 0}>
                {formatCurrency(contact.reductionPerContact)}
              </span>
              <span class="response-rate">
                <ProgressBar 
                  value={contact.responseRate * 100} 
                  max={100} 
                  color={contact.responseRate < 0.3 ? "var(--color-red)" : contact.responseRate < 0.6 ? "var(--color-orange)" : "var(--color-primary)"}
                  height="16px"
                  showLabel={false}
                />
                <span class="rate-text">{Math.round(contact.responseRate * 100)}%</span>
              </span>
            </div>
          {/each}
        </div>
      </MetricCard>

      <!-- Urgent Attention Cases -->
      <MetricCard title="Urgent Attention Required" icon="fa-bell" variant="warning">
        <div class="urgent-list">
          {#each urgentCases.slice(0, 8) as urgent}
            <div class="urgent-item">
              <div class="urgent-header">
                <span class="tenant-ref">{urgent.tenantReference}</span>
                <span class="urgent-balance">{formatCurrency(urgent.currentBalance)}</span>
              </div>
              <div class="urgent-details">
                <span class="risk-badge" style="background-color: {getRiskCategoryColor(urgent.riskCategory)}">
                  {urgent.riskCategory.toUpperCase()}
                </span>
                <span class="escalation-badge" style="background-color: {getEscalationColor(urgent.escalationLevel)}">
                  {urgent.escalationLevel}
                </span>
                <span class="days-since">
                  {urgent.daysSinceLastContact} days ago
                </span>
              </div>
            </div>
          {/each}
        </div>
      </MetricCard>

    </div>
  {/if}
</div>

<style lang="less">
  .analytics-dashboard {
    padding: 1rem;
    background: #f8fafc;
  }

  .analytics-dashboard.full-view {
    padding: 1.5rem;
  }

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .dashboard-header h2 {
    margin: 0;
    color: var(--color-text);
    font-size: 1.5rem;
    font-weight: 600;
  }

  .filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .filters select,
  .filters input {
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    font-size: 0.875rem;
    min-width: 120px;
  }

  .loading-state,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: var(--color-grey);
  }

  .loading-state i,
  .empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f4f6;
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
  }

  .risk-escalation-container {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
    max-height: 300px;
  }

  .risk-escalation-container :global(.metric-card) {
    height: 100%;
    max-height: 300px;
  }

  .risk-escalation-container :global(.metric-card-content) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.75rem;
  }

  .risk-escalation-container :global(.donut-legend) {
    font-size: 0.75rem;
  }

  .risk-escalation-container :global(.legend-item) {
    padding: 0.25rem 0;
  }

  .risk-escalation-container :global(.legend-label) {
    font-size: 0.75rem;
  }

  .risk-escalation-container :global(.legend-value) {
    font-size: 0.75rem;
  }

  .overview-grid {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .trend-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .effectiveness-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 0;
    min-width: 0;
    overflow-x: auto;
  }

  .table-header {
    display: grid;
    grid-template-columns: 1fr 60px 100px 100px 120px;
    gap: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--color-grey);
    text-transform: uppercase;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border);
    align-items: center;
    min-width: 440px;
  }

  .table-header > span:nth-child(2),
  .table-header > span:nth-child(3),
  .table-header > span:nth-child(4) {
    text-align: right;
  }

  .table-row {
    display: grid;
    grid-template-columns: 1fr 60px 100px 100px 120px;
    gap: 0.5rem;
    font-size: 0.875rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
    align-items: center;
    min-width: 440px;
  }

  .table-row > span:nth-child(2),
  .table-row > span:nth-child(3),
  .table-row > span:nth-child(4) {
    text-align: right;
  }

  .tenant-ref {
    font-weight: 600;
    color: var(--color-primary);
  }

  .balance-change,
  .per-contact {
    font-weight: 600;
  }

  .positive {
    color: var(--color-green);
  }

  .negative {
    color: var(--color-red);
  }

  .response-rate {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  .rate-text {
    font-size: 0.75rem;
    font-weight: 600;
  }

  .bottom-performer {
    background: #fef8f0;
    border-left: 3px solid var(--color-orange);
    margin: 0 -0.5rem;
    padding: 0.75rem 0.5rem;
    border-radius: 0.25rem;
  }

  .bottom-performer .tenant-ref {
    color: var(--color-orange);
    font-weight: 700;
  }

  .urgent-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
    margin: 0;
  }

  .urgent-item {
    padding: 0.75rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
  }

  .urgent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .urgent-balance {
    font-weight: 600;
    color: var(--color-red);
  }

  .urgent-details {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .risk-badge,
  .escalation-badge {
    font-size: 0.625rem;
    font-weight: 600;
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
  }

  .days-since {
    font-size: 0.75rem;
    color: var(--color-grey);
    margin-left: auto;
  }

  .trend-summary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 0;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .analytics-grid {
      grid-template-columns: 1fr;
    }
    
    .dashboard-header {
      flex-direction: column;
      align-items: stretch;
    }
    
    .filters {
      justify-content: center;
    }
    
    .overview-grid {
      grid-template-columns: 1fr;
    }
    
    .risk-escalation-container {
      grid-template-columns: 1fr;
      max-height: none;
    }
    
    .table-header,
    .table-row {
      grid-template-columns: 1fr 50px 80px 80px 100px;
      font-size: 0.7rem;
      gap: 0.25rem;
    }
    
    .trend-metrics {
      grid-template-columns: 1fr;
    }
  }
</style>