<script lang="ts">
  import { updateRentCalculatorData, currentInstanceData, highestPriorityAction, suggestedActions } from '$lib/stores';
  import NotesSection from './NotesSection.svelte';
  import OutputGroup from './OutputGroup.svelte';
  import FormGroup from './FormGroup.svelte';
  import DataTable from './DataTable.svelte';
  import { generateRentCollectionPDF } from '$lib/utils/pdfExport';
  
  export const isFullView: boolean = false;
  
  // PDF export state
  let isExportingPDF = false;
  
  // Local state for form inputs
  let currentBalance = 0;
  let weeklyRent = 0;
  let tenantMonthlyPaymentFormula = ''; // Formula string for display
  let apaHbMonthlyPayment = 0;
  let tpdMonthlyPayment = 0;
  let tenantWeeklyPayment = 0;
  let benefitsHbWeeklyPayment = 0;
  
  // Debouncing state
  let updateTimeout: number | null = null;
  let isUpdatingFromStore = false;
  
  
  // Subscribe to current instance data (only when not actively updating)
  $: if ($currentInstanceData && !isUpdatingFromStore) {
    const data = $currentInstanceData.rentCalculator;
    currentBalance = data.currentBalance;
    weeklyRent = data.weeklyRent;
    tenantMonthlyPaymentFormula = data.tenantMonthlyPaymentFormula || (data.tenantMonthlyPayment > 0 ? data.tenantMonthlyPayment.toString() : '');
    apaHbMonthlyPayment = data.apaHbMonthlyPayment;
    tpdMonthlyPayment = data.tpdMonthlyPayment;
    tenantWeeklyPayment = data.tenantWeeklyPayment;
    benefitsHbWeeklyPayment = data.benefitsHbWeeklyPayment;
  }
  
  // Get ArrangementPlanner data for arrears calculation
  $: arrangementData = $currentInstanceData?.arrangementPlanner;
  $: weeksToNextPay = arrangementData?.weeksToNextPay || 0;
  $: paymentDue = arrangementData?.paymentDue || 0;

  // Action/Contact required logic
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;
  $: actionRequired = rentCalculatorData?.currentBalance > 0;
  
  // Reactive calculations that update in real-time
  $: monthlyRent = weeklyRent * 52 / 12;
  
  $: tenantMonthlyValue = evaluateMathExpression(tenantMonthlyPaymentFormula);
  
  $: totalMonthlyPayments = tenantMonthlyValue + apaHbMonthlyPayment + tpdMonthlyPayment + 
           (tenantWeeklyPayment * 52 / 12) + (benefitsHbWeeklyPayment * 52 / 12);
  
  $: totalWeeklyPayments = tenantWeeklyPayment + benefitsHbWeeklyPayment + 
           (tenantMonthlyValue * 12 / 52) + (apaHbMonthlyPayment * 12 / 52) + 
           (tpdMonthlyPayment * 12 / 52);
  
  $: monthlyShortfallSurplus = totalMonthlyPayments - monthlyRent;
  
  $: weeklyShortfallSurplus = totalWeeklyPayments - weeklyRent;
  
  $: tenantContributionMonthly = tenantMonthlyValue + (tenantWeeklyPayment * 52 / 12);
  
  $: tenantContributionWeekly = tenantWeeklyPayment + (tenantMonthlyValue * 12 / 52);
  
  $: benefitsContributionMonthly = apaHbMonthlyPayment + tpdMonthlyPayment + (benefitsHbWeeklyPayment * 52 / 12);
  
  $: benefitsContributionWeekly = benefitsHbWeeklyPayment + ((apaHbMonthlyPayment + tpdMonthlyPayment) * 12 / 52);

  $: totalPaidWeekly = tenantContributionWeekly + benefitsContributionWeekly;

  $: totalPaidMonthly = tenantContributionMonthly + benefitsContributionMonthly;
  
  // Reactive calculations for arrears and dates
  $: arrearsAfterNextPayment = (() => {
    // Get weekly payments that continue as normal
    const regularWeeklyPayments = tenantWeeklyPayment + benefitsHbWeeklyPayment;
    
    // Calculate balance after weeks to next payment, accounting for weekly payments
    let balanceAfterWeeks = currentBalance + (weeklyRent * weeksToNextPay) - (regularWeeklyPayments * weeksToNextPay);
    
    // Subtract arrangement planner payment due
    balanceAfterWeeks -= paymentDue;
    
    // If there are monthly payments that would normally occur during this period, we need to account for their delay
    if (weeksToNextPay > 0) {
      const totalMonthlyPayments = tenantMonthlyValue + apaHbMonthlyPayment + tpdMonthlyPayment;
      
      // Estimate how many monthly payments would normally occur during the weeks to next payment
      // Using 4.33 weeks per month as average
      const monthsInPeriod = weeksToNextPay / 4.33;
      const missedMonthlyPayments = Math.floor(monthsInPeriod) * totalMonthlyPayments;
      
      // Add back the missed monthly payments (since they're delayed)
      balanceAfterWeeks += missedMonthlyPayments;
    }
    
    return balanceAfterWeeks;
  })();
  
  $: arrearsClearDate = (() => {
    if (monthlyShortfallSurplus <= 0 || arrearsAfterNextPayment <= 0) {
      return 'N/A';
    }
    
    const monthsToClearing = Math.ceil(arrearsAfterNextPayment / monthlyShortfallSurplus);
    const clearingDate = new Date();
    clearingDate.setMonth(clearingDate.getMonth() + monthsToClearing);
    
    return clearingDate.toLocaleDateString('en-GB', { 
      month: 'long', 
      year: 'numeric' 
    });
  })();
  
  $: fourWeeksArrears = weeklyRent * 4;
  
  $: eightWeeksArrears = weeklyRent * 8;
  
  $: refundAmount = currentBalance >= 0 ? 0 : Math.max(0, Math.abs(currentBalance) - weeklyRent);

  // Rent threshold calculations for floating boxes
  $: fourWeekProjection = weeklyRent * 4;

  $: eightWeekProjection = weeklyRent * 8;

  // ============================================================================
  // RESOLUTION SUGGESTION LOGIC
  // ============================================================================
  // This reactive calculation determines which resolution(s) to suggest based on tenant situation
  // EASY TO MODIFY: Update the conditions below to match exact business rules
  // ============================================================================
  $: suggestedResolutions = (() => {
    const resolutions = [];
    const balance = currentBalance;
    
    // ========================================
    // BUSINESS LOGIC FOR RESOLUTIONS
    // Updated to use only current balance and weekly rent
    // ========================================
    
    // NSP (No Significant Payment) - Low debt
    if (balance > 0 && balance < fourWeeksArrears) {
      resolutions.push('NSP');
    }
    
    // SCRF (Social Care Referral Fund) - Medium debt
    if (balance >= fourWeeksArrears && balance < eightWeeksArrears) {
      resolutions.push('SCRF');
    }
    
    // APA (Alternative Payment Arrangement) - High debt
    if (balance >= eightWeeksArrears && balance < weeklyRent * 12) {
      resolutions.push('APA');
    }
    
    // TPD (Third Party Deduction) - Very high debt
    if (balance >= weeklyRent * 12) {
      resolutions.push('TPD');
    }
    
    return resolutions;
  })();
  
  // Helper function to get resolution descriptions for clarity
  function getResolutionDescription(resolution: string): string {
    switch (resolution) {
      case 'NSP': return 'No Significant Payment - Low debt (under 4 weeks rent)';
      case 'SCRF': return 'Social Care Referral Fund - Medium debt (4-8 weeks rent)';
      case 'APA': return 'Alternative Payment Arrangement - High debt (8-12 weeks rent)';
      case 'TPD': return 'Third Party Deduction - Very high debt (over 12 weeks rent)';
      default: return '';
    }
  }
  
  
  // Reactive styling for shortfall/surplus
  $: weeklyShortfallColor = weeklyShortfallSurplus > 0 ? 'var(--color-green)' : weeklyShortfallSurplus < 0 ? 'var(--color-red)' : '#111827';
  $: monthlyShortfallColor = monthlyShortfallSurplus > 0 ? 'var(--color-green)' : monthlyShortfallSurplus < 0 ? 'var(--color-red)' : '#111827';
  $: weeklyShortfallLabel = weeklyShortfallSurplus > 0 ? 'Surplus' : weeklyShortfallSurplus < 0 ? 'Shortfall' : 'Balance';
  $: monthlyShortfallLabel = monthlyShortfallSurplus > 0 ? 'Surplus' : monthlyShortfallSurplus < 0 ? 'Shortfall' : 'Balance';

  function getShortfallSurplusColor(value: number): string {
    if (value > 0) return 'var(--color-green)'; // Surplus is good (green)
    if (value < 0) return 'var(--color-red)';   // Shortfall is bad (red)
    return '#111827';
  }

  function getShortfallSurplusLabel(value: number): string {
    if (value > 0) return 'Surplus';
    if (value < 0) return 'Shortfall';
    return 'Balance';
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function evaluateMathExpression(value: string): number {
    // Remove leading/trailing spaces and commas
    const cleaned = value.trim().replace(/,/g, '');
    
    // If it's just a number, parse it directly
    if (/^\d*\.?\d+$/.test(cleaned)) {
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    // Try to evaluate as a mathematical expression
    try {
      // Only allow safe mathematical expressions (numbers, +, -, *, /, ., spaces, parentheses)
      if (!/^[\d+\-*/().\s]+$/.test(cleaned)) {
        // If it contains invalid characters, try parsing as regular number
        const parsed = parseFloat(cleaned);
        return isNaN(parsed) ? 0 : parsed;
      }
      
      // Use Function constructor to safely evaluate mathematical expressions
      const result = new Function('return ' + cleaned)();
      return isNaN(result) ? 0 : result;
    } catch (error) {
      // If evaluation fails, try parsing as regular number
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
  }
  
  function sanitizeNumericInput(value: string): number {
    return evaluateMathExpression(value);
  }
  
  function handleInputChange() {
    // Clear existing timeout
    if (updateTimeout) {
      clearTimeout(updateTimeout);
    }
    
    // Set flag to prevent store subscription from overwriting inputs
    isUpdatingFromStore = true;
    
    // Debounce the store update to prevent cursor jumping
    updateTimeout = setTimeout(() => {
      console.log('RentCalculator: handleInputChange called', {
        currentBalance,
        weeklyRent,
        tenantMonthlyPayment: evaluateMathExpression(tenantMonthlyPaymentFormula),
        tenantMonthlyPaymentFormula,
        apaHbMonthlyPayment,
        tpdMonthlyPayment,
        tenantWeeklyPayment,
        benefitsHbWeeklyPayment
      });
      updateRentCalculatorData({
        currentBalance,
        weeklyRent,
        tenantMonthlyPayment: evaluateMathExpression(tenantMonthlyPaymentFormula),
        tenantMonthlyPaymentFormula,
        apaHbMonthlyPayment,
        tpdMonthlyPayment,
        tenantWeeklyPayment,
        benefitsHbWeeklyPayment
      });
      
      // Reset flag after store update with longer delay
      setTimeout(() => {
        isUpdatingFromStore = false;
      }, 100);
    }, 500); // Increased debounce delay from 300ms to 500ms
  }
  
  function handleFormGroupInput(event: CustomEvent) {
    const { id, value } = event.detail;
    
    switch(id) {
      case 'currentBalance':
        currentBalance = sanitizeNumericInput(value);
        break;
      case 'weeklyRent':
        weeklyRent = sanitizeNumericInput(value);
        break;
      case 'tenantMonthlyPayment':
        tenantMonthlyPaymentFormula = value; // Store raw value for formula support
        break;
      case 'apaHbMonthlyPayment':
        apaHbMonthlyPayment = sanitizeNumericInput(value);
        break;
      case 'tpdMonthlyPayment':
        tpdMonthlyPayment = sanitizeNumericInput(value);
        break;
      case 'tenantWeeklyPayment':
        tenantWeeklyPayment = sanitizeNumericInput(value);
        break;
      case 'benefitsHbWeeklyPayment':
        benefitsHbWeeklyPayment = sanitizeNumericInput(value);
        break;
    }
    
    handleInputChange();
  }
  
  async function handleRentButton() {
    // Copy preset text as per original specification
    const presetText = `Weekly rent is ${formatCurrency(weeklyRent)}, monthly rent is ${formatCurrency(monthlyRent)}`;
    try {
      await navigator.clipboard.writeText(presetText);
      console.log('Rent information copied to clipboard');
    } catch (error) {
      console.error('Failed to copy rent information:', error);
    }
  }

  async function handleExportPDF() {
    if (!$currentInstanceData?.rentCalculator?.reference) {
      alert('Please enter a case reference before exporting to PDF');
      return;
    }
    
    isExportingPDF = true;
    try {
      await generateRentCollectionPDF();
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF generation failed. An HTML file has been downloaded as a fallback.');
    } finally {
      isExportingPDF = false;
    }
  }


  // DataTable columns and rows for summary table
  $: summaryColumns = [
    { key: 'label', label: '', type: 'label' as const, align: 'left' as const, width: '1fr' },
    { key: 'weekly', label: 'Weekly', type: 'currency' as const, align: 'right' as const, width: '140px' },
    { key: 'monthly', label: 'Monthly', type: 'currency' as const, align: 'right' as const, width: '140px' }
  ];

  $: summaryRows = [
    // Shortfall/Surplus row - moved to first position to match RentCalculatorFull
    {
      label: weeklyShortfallLabel,
      weekly: weeklyShortfallSurplus,
      monthly: monthlyShortfallSurplus,
      weekly_style: `color: ${weeklyShortfallColor}; font-weight: 600;`,
      monthly_style: `color: ${monthlyShortfallColor}; font-weight: 600;`
    },
    {
      label: 'Tenant Contribution',
      weekly: tenantContributionWeekly,
      monthly: tenantContributionMonthly
    },
    {
      label: 'Benefits Contribution',
      weekly: benefitsContributionWeekly,
      monthly: benefitsContributionMonthly
    },
    {
      label: 'Total Paid',
      weekly: totalPaidWeekly,
      monthly: totalPaidMonthly
    },
    // Add clearance/refund row after surplus
    ...(currentBalance < 0 ? [{
      label: 'Refund Available',
      weekly_html: '',
      monthly_html: `£<span style="padding: calc(var(--cell-padding));display: inline-flex;">${refundAmount.toFixed(2)}</span>`,
      weekly_style: 'display: none;',
      monthly_style: 'grid-column: span 2; text-align: center; font-weight: bold; background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(13, 155, 58, 0.1)); border-radius: 6px; padding: 8px; color: var(--color-green);'
    }] : monthlyShortfallSurplus > 0 ? [{
      label: 'Arrears Will Clear',
      weekly_html: '',
      monthly_html: arrearsClearDate,
      weekly_style: 'display: none;',
      monthly_style: 'grid-column: span 2; text-align: center; font-weight: bold; background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(13, 155, 58, 0.1)); border-radius: 6px; padding: 8px; color: var(--color-green);'
    }] : [])
  ];

</script>

<div class="rent-calculator">
  <div class="calculator-main">
    <!-- Main Table -->
    <div class="main-table-section">
      <div class="rent-table-section">
        <div class="rent-table">
          <div class="data-cell type-label">Current Balance</div>
          <div class="data-cell" style="grid-column: span 2;">
            <FormGroup
              id="currentBalance"
              value={currentBalance}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>
        </div>

      </div>
      <div class="rent-table-section force-end">
        <!-- Action/Contact Required Section -->
        {#if actionRequired && $suggestedActions.length > 0}
          <div class="action-required-header priority-{$highestPriorityAction}">
            <p>Arrears of {formatCurrency(rentCalculatorData?.currentBalance || 0)} requires immediate contact and action</p>
          </div>
        {:else if actionRequired}
          <div class="contact-required-header">
            <p>Balance of {formatCurrency(rentCalculatorData?.currentBalance || 0)} requires tenant contact</p>
          </div>
        {/if}
      </div>
      <div class="rent-table-section">
      <div class="rent-table">
        <!-- Header row -->
        <div class="header-cell"></div>
        <div class="header-cell align-right">Weekly</div>
        <div class="header-cell align-right">Monthly</div>
        
        <!-- Rent Row -->
        <div class="data-cell type-label">Rent</div>
        <div class="data-cell">
          <FormGroup
            id="weeklyRent"
            label=""
            value={weeklyRent}
            placeholder="0.00"
            type="number"
            showCurrency={true}
            hideLabel={true}
            on:input={handleFormGroupInput}
          />
        </div>
        <div class="data-cell">
          <FormGroup
            id="monthlyRent"
            label=""
            value={monthlyRent}
            type="text"
            showCurrency={true}
            disabled={true}
            hideLabel={true}
          />
        </div>
        
        <!-- Tenant Contribution Row -->
        <div class="data-cell type-label">Tenant Contribution</div>
        <div class="data-cell">
          <FormGroup
            id="tenantWeeklyPayment"
            label=""
            value={tenantWeeklyPayment}
            placeholder="0.00"
            type="number"
            showCurrency={true}
            hideLabel={true}
            on:input={handleFormGroupInput}
          />
        </div>
        <div class="data-cell">
          <FormGroup
            id="tenantMonthlyPayment"
            label=""
            value={tenantMonthlyPaymentFormula}
            placeholder="0.00"
            type="text"
            showCurrency={true}
            showFormula={true}
            hideLabel={true}
            on:input={handleFormGroupInput}
          />
        </div>
        
        <!-- APA Row -->
        <div class="data-cell type-label">APA</div>
        <div class="data-cell">
          <FormGroup
            id="apaWeekly"
            label=""
            value={(apaHbMonthlyPayment * 12 / 52)}
            type="text"
            showCurrency={true}
            disabled={true}
            hideLabel={true}
          />
        </div>
        <div class="data-cell">
          <FormGroup
            id="apaHbMonthlyPayment"
            label=""
            value={apaHbMonthlyPayment}
            placeholder="0.00"
            type="number"
            showCurrency={true}
            hideLabel={true}
            on:input={handleFormGroupInput}
          />
        </div>
        
        <!-- HB Row -->
        <div class="data-cell type-label">HB</div>
        <div class="data-cell">
          <FormGroup
            id="benefitsHbWeeklyPayment"
            label=""
            value={benefitsHbWeeklyPayment}
            placeholder="0.00"
            type="number"
            showCurrency={true}
            hideLabel={true}
            on:input={handleFormGroupInput}
          />
        </div>
        <div class="data-cell">
          <FormGroup
            id="hbMonthly"
            label=""
            value={(benefitsHbWeeklyPayment * 52 / 12)}
            type="text"
            showCurrency={true}
            disabled={true}
            hideLabel={true}
          />
        </div>
        
        <!-- TPD Row -->
        <div class="data-cell type-label">TPD</div>
        <div class="data-cell">
          <FormGroup
            id="tpdWeekly"
            label=""
            value={(tpdMonthlyPayment * 12 / 52)}
            type="text"
            showCurrency={true}
            disabled={true}
            hideLabel={true}
          />
        </div>
        <div class="data-cell">
          <FormGroup
            id="tpdMonthlyPayment"
            label=""
            value={tpdMonthlyPayment}
            placeholder="0.00"
            type="number"
            showCurrency={true}
            hideLabel={true}
            on:input={handleFormGroupInput}
          />
        </div>
        
      </div>
      

      </div>
      <div class="rent-table-section">

        

        <div class="summary-table-container">
          <DataTable
            columns={summaryColumns}
            rows={summaryRows}
            showHeader={true}
            hoverEffect={true}
            matchInputHeight={true}
          />
        </div>

        <div class="calculations-table-container">
          <!-- Floating projection boxes -->
          <div class="projection-boxes">
            <div class="projection-box">
              <div class="projection-header">4 Weeks Rent</div>
              <div class="projection-value">
                £{fourWeekProjection.toFixed(2)}
              </div>
            </div>
            <div class="projection-box">
              <div class="projection-header">8 Weeks Rent</div>
              <div class="projection-value">
                £{eightWeekProjection.toFixed(2)}
              </div>
            </div>
            <button 
              class="export-pdf-button" 
              on:click={handleExportPDF}
              disabled={isExportingPDF}
              title="Export to PDF"
            >
              {#if isExportingPDF}
                <i class="fas fa-spinner fa-spin"></i>
                <span>Exporting...</span>
              {:else}
                <i class="fas fa-file-pdf"></i>
                <span>Export PDF</span>
              {/if}
            </button>
          </div>
        </div>

      
      </div>
      

        <!-- Recommended Actions Section -->
        {#if $suggestedActions.length > 0}
        <div class="actions-section">
          <div class="recommended-actions-section">
            <div class="actions-header">
              <h4>Recommended Actions</h4>
            </div>
            <div class="compact-action-cards">
              {#each $suggestedActions as action}
                <div class="compact-action-card priority-{action.priority}">
                  <div class="compact-action-header">
                    <span class="compact-action-code">{action.code}</span>
                    <span class="compact-action-priority priority-{action.priority}">
                      {action.priority.toUpperCase()}
                    </span>
                  </div>
                  <h6 class="compact-action-title">{action.title}</h6>
                  <p class="compact-action-description">{action.description}</p>
                </div>
              {/each}
            </div>
          </div>
          </div>
        {/if}
      
    </div>
  </div>
</div>

<style lang="less">
  .rent-calculator {
    padding: calc(var(--gap) * 2);
    height: 100%;
    overflow-y: auto;
  }

  .calculator-main {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    margin: 0 auto;
  }

  .main-table-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap);
    align-items: flex-start;
  }

  .rent-table-section {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    justify-content: space-between;
    height: 100%;
    &.force-end {
      justify-content: flex-end;
    }
  }

  /* CSS Grid Table Styling - matches DataTable */
  .rent-table {
    display: grid;
    grid-template-columns: 1fr 140px 140px;
    width: 100%;
    background: white;
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
  }

  .header-cell {
    background: #f8fafc;
    color: #374151;
    font-weight: 700;
    font-size: var(--label-size);
    padding: var(--cell-padding);
    border-bottom: 2px solid var(--border);
    border-right: 1px solid var(--border);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    &:nth-child(3) {
      border-right: none;
    }
  }

  .header-cell:last-child {
    border-right: none;
  }

  .align-right {
    text-align: right;
  }

  .data-cell {
    padding: var(--cell-padding);
    border-bottom: 1px solid var(--border);
    border-right: 1px solid var(--border);
    font-size: var(--value-size);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
  
  }

  .data-cell:nth-child(3n) {
    border-right: none;
  }

  /* Remove border-bottom from last row */
  .data-cell:nth-last-child(-n+3) {
    border-bottom: none;
  }

  /* Label cell styling - matches DataTable type-label */
  .data-cell.type-label {
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--label-size);
    background-color: #f8fafc;
  }

  /* Value cells styling */
  .data-cell:not(.type-label) {
    text-align: right;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
  }
  
  .data-cell :global(.form-group) {
    margin: 0;
  }
  
  .data-cell :global(.form-group input) {
    text-align: right;
    background: transparent;
    font-weight: 500;
  }
  
  
  .data-cell :global(.form-group input:focus) {
    border-color: var(--color-primary);
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .data-cell :global(.currency-input) {
    justify-content: flex-end;
  }
  
  .data-cell :global(.form-group input:disabled) {
    background: transparent;
    border: none;
    // color: inherit;
    cursor: default;
  }
  
  .shortfall-row :global(.form-group input:disabled) {
    color: var(--shortfall-color, inherit);
    font-weight: 600;
  }

  .shortfall-row {
    font-weight: 600;
    border-top: 2px solid var(--border);
  }

  .shortfall-row.type-label {
    font-weight: 600;
  }

  /* Current Balance Integrated Section */
  .current-balance-integrated {
    // background: white;
    // border: 1px solid var(--border);
    // border-radius: 8px;
    // padding: var(--gap);
    // margin-bottom: calc(var(--gap) / 2);
  }

  /* Actions Section */
  .actions-section {
    background: white;
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: var(--gap);
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    grid-column: span 2;
  }

  /* Action Required Header Styles */
  .action-required-header {
    color: white;
    padding: calc(var(--cell-padding) * 3) calc(var(--cell-padding) * 2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    min-height: calc(var(--cell-padding) * 6.4);
    
    p {
      margin: 0;
      font-size: var(--label-size);
      opacity: 0.95;
      text-align: center;
      width: 100%;
    }
  }

  /* Priority-based background colors for Action Required header */
  .action-required-header.priority-urgent {
    background: var(--color-red);
  }

  .action-required-header.priority-high {
    background: var(--color-orange);
  }

  .action-required-header.priority-medium {
    background: var(--color-yellow);
  }

  .action-required-header.priority-low {
    background: var(--color-green);
  }

  /* Contact Required Header Styles */
  .contact-required-header {
    background: var(--color-secondary);
    color: white;
    padding: calc(var(--cell-padding) * 3) calc(var(--cell-padding) * 2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    min-height: calc(var(--cell-padding) * 6.4);
    p {
      margin: 0;
      font-size: 0.875rem;
      opacity: 0.95;
      text-align: center;
      font-weight: 500;
      width: 100%;
    }
  }

  /* Recommended Actions Section */
  .recommended-actions-section {
    // background: #f8fafc;
    // border: 1px solid var(--border);
    // border-radius: 8px;
    // padding: var(--gap);
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .actions-header h4 {
    margin: 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
  }

  /* Compact Action Cards - single column for right sidebar */
  .compact-action-cards {
    display: flex;
    // flex-direction: column;
    gap: var(--gap);
  }

  .compact-action-card {
    background: white;
    border-radius: 6px;
    padding: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
    border-left: 3px solid;
    position: relative;
  }

  .compact-action-card.priority-urgent {
    border-left-color: var(--color-red);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, white 100%);
  }

  .compact-action-card.priority-high {
    border-left-color: var(--color-orange);
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, white 100%);
  }

  .compact-action-card.priority-medium {
    border-left-color: var(--color-yellow);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, white 100%);
  }

  .compact-action-card.priority-low {
    border-left-color: var(--color-green);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, white 100%);
  }

  .compact-action-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .compact-action-code {
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-weight: 700;
    font-size: 0.75rem;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    letter-spacing: 0.05em;
  }

  .compact-action-card.priority-urgent .compact-action-code {
    background: var(--color-red);
  }

  .compact-action-card.priority-high .compact-action-code {
    background: var(--color-orange);
  }

  .compact-action-card.priority-medium .compact-action-code {
    background: var(--color-yellow);
  }

  .compact-action-card.priority-low .compact-action-code {
    background: var(--color-green);
  }

  .compact-action-priority {
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    letter-spacing: 0.05em;
  }

  .compact-action-priority.priority-urgent {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-red);
  }

  .compact-action-priority.priority-high {
    background: rgba(249, 115, 22, 0.1);
    color: var(--color-orange);
  }

  .compact-action-priority.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-yellow);
  }

  .compact-action-priority.priority-low {
    background: rgba(34, 197, 94, 0.1);
    color: var(--color-green);
  }

  .compact-action-title {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
  }

  .compact-action-description {
    margin: 0;
    font-size: 0.75rem;
    color: #475569;
    line-height: 1.4;
  }

  /* Projection boxes styling */
  .projection-boxes {
    display: flex;
    gap: var(--gap);
    justify-content: center;
  }
  
  .projection-box {
    background: white;
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: calc(var(--gap) / 1.5);
    text-align: center;
    flex-grow: 1;
    min-width: 110px;
  }
  
  .projection-header {
    font-size: var(--label-size);
    font-weight: var(--label-weight);
    color: var(--color-primary);
    margin-bottom: calc(var(--gap) / 2);
  }
  
  .projection-value {
    font-size: var(--value-size);
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-weight: 700;
    color: black;
  }
  
  .export-pdf-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: var(--gap);
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: var(--button-size);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
    text-align: center;
  }
  
  .export-pdf-button:hover:not(:disabled) {
    background: color-mix(in srgb, var(--color-primary) 85%, black 15%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  .export-pdf-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  .export-pdf-button i {
    font-size: 18px;
    color: white !important;
  }
  
  .export-pdf-button .fa-spinner {
    animation: spin 1s linear infinite;
    color: white !important;
  }
  
  .export-pdf-button span {
    font-size: var(--label-size);
    font-weight: var(--label-weight);
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .main-table-section {
      grid-template-columns: 1fr;
    }
    
    .action-section {
      order: -1;
    }
    
    .compact-action-cards {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }
  }
  
  @media (max-width: 768px) {
    .compact-action-cards {
      grid-template-columns: 1fr;
    }

    .projection-boxes {
      flex-direction: column;
    }
  }

</style>
