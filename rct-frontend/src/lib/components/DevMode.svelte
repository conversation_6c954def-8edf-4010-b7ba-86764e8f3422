<script lang="ts">
  import { onMount } from 'svelte';
  import Button from './Button.svelte';

  export let isVisible = false;

  // CSS Custom Property values
  let labelWeight = 400;
  let labelColor = '#374151';
  let labelSize = 0.8;
  let valueSize = 0.9;
  let cellPadding = 0.275;
  let subheaderSize = 0.9;
  let buttonSize = 0.9;
  let gap = 0.5;
  let sectionHeaderSize = 0.825;
  
  // Color variables
  let colorPrimary = '#025582';
  let colorSecondary = 'rgb(0, 51, 68)';
  let colorRed = '#dc2626';
  let colorOrange = '#ea580c';
  let colorYellow = '#ca8a04';
  let colorGreen = '#059669';
  let bgColor = '#0255822d';
  let colorInstanceInbound = '#DB2955';
  let colorInstanceOutbound = '#4DA1A9';

  // Dragging state
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };
  let windowPosition = { x: 0, y: 0 };
  let windowElement: HTMLElement;

  // Initialize position to top right
  function initializePosition() {
    if (typeof window === 'undefined' || !windowElement) return;
    const rect = windowElement.getBoundingClientRect();
    windowPosition.x = window.innerWidth - rect.width - 20; // 20px from right
    windowPosition.y = 90; // 100px from top
  }

  // Get current CSS custom property values from the document
  function getCurrentValues() {
    if (typeof document === 'undefined') return;
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    labelWeight = parseInt(computedStyle.getPropertyValue('--label-weight').trim()) || 400;
    labelColor = computedStyle.getPropertyValue('--label-color').trim() || '#374151';
    labelSize = parseFloat(computedStyle.getPropertyValue('--label-size').replace('rem', '').trim()) || 0.8;
    valueSize = parseFloat(computedStyle.getPropertyValue('--value-size').replace('rem', '').trim()) || 0.9;
    cellPadding = parseFloat(computedStyle.getPropertyValue('--cell-padding').replace('rem', '').trim()) || 0.275;
    subheaderSize = parseFloat(computedStyle.getPropertyValue('--subheader-size').replace('rem', '').trim()) || 0.9;
    buttonSize = parseFloat(computedStyle.getPropertyValue('--button-size').replace('rem', '').trim()) || 0.9;
    gap = parseFloat(computedStyle.getPropertyValue('--gap').replace('rem', '').trim()) || 1;
    sectionHeaderSize = parseFloat(computedStyle.getPropertyValue('--section-header-size').replace('rem', '').trim()) || 0.825;
    
    // Color variables
    colorPrimary = computedStyle.getPropertyValue('--color-primary').trim() || '#025582';
    colorSecondary = computedStyle.getPropertyValue('--color-secondary').trim() || 'rgb(0, 51, 68)';
    colorRed = computedStyle.getPropertyValue('--color-red').trim() || '#dc2626';
    colorOrange = computedStyle.getPropertyValue('--color-orange').trim() || '#ea580c';
    colorYellow = computedStyle.getPropertyValue('--color-yellow').trim() || '#ca8a04';
    colorGreen = computedStyle.getPropertyValue('--color-green').trim() || '#059669';
    bgColor = computedStyle.getPropertyValue('--bg').trim() || '#0255822d';
    colorInstanceInbound = computedStyle.getPropertyValue('--color-instance-inbound').trim() || '#DB2955';
    colorInstanceOutbound = computedStyle.getPropertyValue('--color-instance-outbound').trim() || '#4DA1A9';
  }

  // Update CSS custom properties
  function updateCSSProperty(property: string, value: string | number) {
    if (typeof document !== 'undefined') {
      document.documentElement.style.setProperty(property, value.toString());
    }
  }

  // Reactive updates
  $: updateCSSProperty('--label-weight', labelWeight);
  $: updateCSSProperty('--label-color', labelColor);
  $: updateCSSProperty('--label-size', `${labelSize}rem`);
  $: updateCSSProperty('--value-size', `${valueSize}rem`);
  $: updateCSSProperty('--cell-padding', `${cellPadding}rem`);
  $: updateCSSProperty('--subheader-size', `${subheaderSize}rem`);
  $: updateCSSProperty('--button-size', `${buttonSize}rem`);
  $: updateCSSProperty('--gap', `${gap}rem`);
  $: updateCSSProperty('--section-header-size', `${sectionHeaderSize}rem`);
  
  // Color variable reactive updates
  $: updateCSSProperty('--color-primary', colorPrimary);
  $: updateCSSProperty('--color-secondary', colorSecondary);
  $: updateCSSProperty('--color-red', colorRed);
  $: updateCSSProperty('--color-orange', colorOrange);
  $: updateCSSProperty('--color-yellow', colorYellow);
  $: updateCSSProperty('--color-green', colorGreen);
  $: updateCSSProperty('--bg', bgColor);
  $: updateCSSProperty('--color-instance-inbound', colorInstanceInbound);
  $: updateCSSProperty('--color-instance-outbound', colorInstanceOutbound);

  // Reset to defaults
  function resetToDefaults() {
    labelWeight = 400;
    labelColor = '#374151';
    labelSize = 0.8;
    valueSize = 0.9;
    cellPadding = 0.275;
    subheaderSize = 0.9;
    buttonSize = 0.9;
    gap = 0.5;
    sectionHeaderSize = 0.825;
    
    // Reset color variables
    colorPrimary = '#025582';
    colorSecondary = 'rgb(0, 51, 68)';
    colorRed = '#dc2626';
    colorOrange = '#ea580c';
    colorYellow = '#ca8a04';
    colorGreen = '#059669';
    bgColor = '#0255822d';
    colorInstanceInbound = '#DB2955';
    colorInstanceOutbound = '#4DA1A9';
  }

  // Copy current values as CSS
  function copyAsCSS() {
    if (typeof navigator === 'undefined') return;
    const css = `:root {
  --label-weight: ${labelWeight};
  --label-color: ${labelColor};
  --label-size: ${labelSize}rem;
  --value-size: ${valueSize}rem;
  --cell-padding: ${cellPadding}rem;
  --subheader-size: ${subheaderSize}rem;
  --button-size: ${buttonSize}rem;
  --section-header-size: ${sectionHeaderSize}rem;
  --color-primary: ${colorPrimary};
  --color-secondary: ${colorSecondary};
  --color-red: ${colorRed};
  --color-orange: ${colorOrange};
  --color-yellow: ${colorYellow};
  --color-green: ${colorGreen};
  --bg: ${bgColor};
  --color-instance-inbound: ${colorInstanceInbound};
  --color-instance-outbound: ${colorInstanceOutbound};
  --gap: ${gap}rem;
}`;
    
    navigator.clipboard.writeText(css).then(() => {
      console.log('CSS copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy CSS: ', err);
    });
  }

  // Save current values to browser storage
  function saveToBrowserStorage() {
    if (typeof localStorage === 'undefined') return;
    const values = {
      labelWeight,
      labelColor,
      labelSize,
      valueSize,
      cellPadding,
      subheaderSize,
      buttonSize,
      sectionHeaderSize,
      colorPrimary,
      colorSecondary,
      colorRed,
      colorOrange,
      colorYellow,
      colorGreen,
      bgColor,
      colorInstanceInbound,
      colorInstanceOutbound,
      gap,
    };
    
    localStorage.setItem('devmode-values', JSON.stringify(values));
    console.log('Values saved to browser storage');
  }

  // Load values from browser storage
  function loadFromBrowserStorage() {
    if (typeof localStorage === 'undefined') return;
    const stored = localStorage.getItem('devmode-values');
    if (stored) {
      try {
        const values = JSON.parse(stored);
        labelWeight = values.labelWeight ?? 400;
        labelColor = values.labelColor ?? '#374151';
        labelSize = values.labelSize ?? 0.8;
        valueSize = values.valueSize ?? 0.9;
        cellPadding = values.cellPadding ?? 0.275;
        subheaderSize = values.subheaderSize ?? 0.9;
        buttonSize = values.buttonSize ?? 0.9;
        sectionHeaderSize = values.sectionHeaderSize ?? 0.825;
        colorPrimary = values.colorPrimary ?? '#025582';
        colorSecondary = values.colorSecondary ?? 'rgb(0, 51, 68)';
        colorRed = values.colorRed ?? '#dc2626';
        colorOrange = values.colorOrange ?? '#ea580c';
        colorYellow = values.colorYellow ?? '#ca8a04';
        colorGreen = values.colorGreen ?? '#059669';
        bgColor = values.bgColor ?? '#0255822d';
        colorInstanceInbound = values.colorInstanceInbound ?? '#DB2955';
        colorInstanceOutbound = values.colorInstanceOutbound ?? '#4DA1A9';
        gap = values.gap ?? 0.5;
        console.log('Values loaded from browser storage');
      } catch (err) {
        console.error('Failed to load from browser storage:', err);
      }
    }
  }

  // Clear browser storage
  function clearBrowserStorage() {
    if (typeof localStorage === 'undefined') return;
    localStorage.removeItem('devmode-values');
    console.log('Browser storage cleared');
  }

  // Tab state
  let activeTab: 'sizes' | 'colors' = 'sizes';

  function switchTab(tab: 'sizes' | 'colors') {
    activeTab = tab;
  }

  // Dragging functions
  function startDrag(event: MouseEvent) {
    if (typeof document === 'undefined') return;
    if (event.target instanceof HTMLElement && event.target.closest('.drag-handle')) {
      isDragging = true;
      const rect = windowElement.getBoundingClientRect();
      dragOffset.x = event.clientX - rect.left;
      dragOffset.y = event.clientY - rect.top;
      document.addEventListener('mousemove', onDrag);
      document.addEventListener('mouseup', stopDrag);
      event.preventDefault();
    }
  }

  function onDrag(event: MouseEvent) {
    if (typeof window === 'undefined' || !isDragging) return;
    windowPosition.x = event.clientX - dragOffset.x;
    windowPosition.y = event.clientY - dragOffset.y;
    
    // Keep window within viewport bounds
    const rect = windowElement.getBoundingClientRect();
    windowPosition.x = Math.max(0, Math.min(window.innerWidth - rect.width, windowPosition.x));
    windowPosition.y = Math.max(0, Math.min(window.innerHeight - rect.height, windowPosition.y));
  }

  function stopDrag() {
    isDragging = false;
    if (typeof document !== 'undefined') {
      document.removeEventListener('mousemove', onDrag);
      document.removeEventListener('mouseup', stopDrag);
    }
  }

  function closeWindow() {
    isVisible = false;
  }

  onMount(() => {
    getCurrentValues();
    initializePosition();
    // Try to load saved values on mount
    loadFromBrowserStorage();
  });

  // Reactive position updates
  $: if (isVisible && windowElement) {
    initializePosition();
  }
</script>

{#if isVisible}
  <div 
    role="dialog"
    aria-label="Developer Mode Settings"
    tabindex="0"
    class="dev-mode-window {isDragging ? 'dragging' : ''}"
    style="left: {windowPosition.x}px; top: {windowPosition.y}px;"
    bind:this={windowElement}
    on:mousedown={startDrag}
  >
    <div class="window-header drag-handle">
      <div class="window-title">
        <i class="fas fa-code"></i>
        Dev Mode
      </div>
      <Button 
        variant="ghost"
        size="xs"
        className="close-button"
        icon="fas fa-times"
        on:click={closeWindow}
      />
    </div>

    <div class="window-content">
      <p class="dev-description">Adjust design system values in real-time</p>
      
      <!-- Tab Navigation -->
      <div class="tab-nav">
        <Button 
          variant="ghost"
          size="sm"
          className="tab-button {activeTab === 'sizes' ? 'active' : ''}"
          icon="fas fa-text-height"
          on:click={() => switchTab('sizes')}
        >
          Sizes
        </Button>
        <Button 
          variant="ghost"
          size="sm"
          className="tab-button {activeTab === 'colors' ? 'active' : ''}"
          icon="fas fa-palette"
          on:click={() => switchTab('colors')}
        >
          Colours
        </Button>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        {#if activeTab === 'sizes'}
          <div class="controls">

                 <!-- Section Header Size -->
                 <div class="control-group">
                  <label for="section-header-size">Section Header Size: {sectionHeaderSize}rem</label>
                  <input 
                    id="section-header-size"
                    type="range" 
                    min="0.5" 
                    max="1.5" 
                    step="0.025"
                    bind:value={sectionHeaderSize}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>
    
                <!-- Subheader Size -->
                <div class="control-group">
                  <label for="subheader-size">Subheader Size: {subheaderSize}rem</label>
                  <input 
                    id="subheader-size"
                    type="range" 
                    min="0.5" 
                    max="2" 
                    step="0.025"
                    bind:value={subheaderSize}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                
            <!-- Label Weight -->
            <div class="control-group">
              <label for="label-weight">Label Weight: {labelWeight}</label>
              <input 
                id="label-weight"
                type="range" 
                min="100" 
                max="900" 
                step="100"
                bind:value={labelWeight}
                class="range-slider"
              />
              <div class="range-labels">
                <span>100</span>
                <span>900</span>
              </div>
            </div>

            <!-- Label Size -->
            <div class="control-group">
              <label for="label-size">Label Size: {labelSize}rem</label>
              <input 
                id="label-size"
                type="range" 
                min="0.5" 
                max="1.5" 
                step="0.025"
                bind:value={labelSize}
                class="range-slider"
              />
              <div class="range-labels">
                <span>0.5rem</span>
                <span>1.5rem</span>
              </div>
            </div>

            <!-- Value Size -->
            <div class="control-group">
              <label for="value-size">Value Size: {valueSize}rem</label>
              <input 
                id="value-size"
                type="range" 
                min="0.5" 
                max="2" 
                step="0.025"
                bind:value={valueSize}
                class="range-slider"
              />
              <div class="range-labels">
                <span>0.5rem</span>
                <span>2rem</span>
              </div>
            </div>

            <!-- Button Size -->
            <div class="control-group">
              <label for="button-size">Button Size: {buttonSize}rem</label>
              <input 
                id="button-size"
                type="range" 
                min="0.5" 
                max="1.5" 
                step="0.025"
                bind:value={buttonSize}
                class="range-slider"
              />
              <div class="range-labels">
                <span>0.5rem</span>
                <span>1.5rem</span>
              </div>
            </div>

            <!-- Cell Padding -->
            <div class="control-group">
              <label for="cell-padding">Cell Padding: {cellPadding}rem</label>
              <input 
                id="cell-padding"
                type="range" 
                min="0" 
                max="2" 
                step="0.025"
                bind:value={cellPadding}
                class="range-slider"
              />
              <div class="range-labels">
                <span>0rem</span>
                <span>2rem</span>
              </div>
            </div>

            <!-- Gap (Spacing) -->
            <div class="control-group">
              <label for="gap">Gap (Spacing): {gap}rem</label>
              <input 
                id="gap"
                type="range" 
                min="0" 
                max="2" 
                step="0.05"
                bind:value={gap}
                class="range-slider"
              />
              <div class="range-labels">
                <span>0rem</span>
                <span>2rem</span>
              </div>
            </div>

        
          </div>
        {/if}

        {#if activeTab === 'colors'}
          <div class="controls">
            <!-- Primary Color -->
            <div class="control-group">
              <label for="color-primary">Primary Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-primary"
                  type="color" 
                  bind:value={colorPrimary}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorPrimary}
                  class="color-text"
                  placeholder="#025582"
                />
              </div>
            </div>

            <!-- Secondary Color -->
            <div class="control-group">
              <label for="color-secondary">Secondary Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-secondary"
                  type="color" 
                  bind:value={colorSecondary}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorSecondary}
                  class="color-text"
                  placeholder="rgb(0, 51, 68)"
                />
              </div>
            </div>

            <!-- Label Color -->
            <div class="control-group">
              <label for="label-color">Label Colour</label>
              <div class="color-picker-container">
                <input 
                id="label-color"
                type="color" 
                bind:value={labelColor}
                class="color-picker"
              />
              <input 
                type="text" 
                bind:value={labelColor}
                class="color-text"
                placeholder="#374151"
              />
              </div>
            </div>

            <!-- Red Color -->
            <div class="control-group">
              <label for="color-red">Red Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-red"
                  type="color" 
                  bind:value={colorRed}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorRed}
                  class="color-text"
                  placeholder="#dc2626"
                />
              </div>
            </div>

            <!-- Orange Color -->
            <div class="control-group">
              <label for="color-orange">Orange Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-orange"
                  type="color" 
                  bind:value={colorOrange}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorOrange}
                  class="color-text"
                  placeholder="#ea580c"
                />
              </div>
            </div>

            <!-- Yellow Color -->
            <div class="control-group">
              <label for="color-yellow">Yellow Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-yellow"
                  type="color" 
                  bind:value={colorYellow}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorYellow}
                  class="color-text"
                  placeholder="#ca8a04"
                />
              </div>
            </div>

            <!-- Green Color -->
            <div class="control-group">
              <label for="color-green">Green Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-green"
                  type="color" 
                  bind:value={colorGreen}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorGreen}
                  class="color-text"
                  placeholder="#059669"
                />
              </div>
            </div>

            <!-- Background Color -->
            <div class="control-group">
              <label for="bg-color">Background Colour</label>
              <div class="color-picker-container">
                <input 
                  id="bg-color"
                  type="color" 
                  bind:value={bgColor}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={bgColor}
                  class="color-text"
                  placeholder="#0255822d"
                />
              </div>
            </div>

            <!-- Instance Inbound Color -->
            <div class="control-group">
              <label for="color-instance-inbound">Instance Inbound Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-instance-inbound"
                  type="color" 
                  bind:value={colorInstanceInbound}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorInstanceInbound}
                  class="color-text"
                  placeholder="#DB2955"
                />
              </div>
            </div>

            <!-- Instance Outbound Color -->
            <div class="control-group">
              <label for="color-instance-outbound">Instance Outbound Colour</label>
              <div class="color-picker-container">
                <input 
                  id="color-instance-outbound"
                  type="color" 
                  bind:value={colorInstanceOutbound}
                  class="color-picker"
                />
                <input 
                  type="text" 
                  bind:value={colorInstanceOutbound}
                  class="color-text"
                  placeholder="#4DA1A9"
                />
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- Action Buttons - Always Visible -->
      <div class="actions-footer">
        <div class="dev-actions">
          <Button 
            variant="danger"
            size="xs"
            icon="fas fa-undo"
            fullWidth={true}
            on:click={resetToDefaults}
          >
            Reset
          </Button>
          <Button 
            variant="success"
            size="xs"
            icon="fas fa-copy"
            fullWidth={true}
            on:click={copyAsCSS}
          >
            Copy CSS
          </Button>
        </div>
        
        <div class="dev-actions storage-actions">
          <Button 
            variant="primary"
            size="xs"
            icon="fas fa-save"
            fullWidth={true}
            on:click={saveToBrowserStorage}
          >
            Save
          </Button>
          <Button 
            variant="warning"
            size="xs"
            icon="fas fa-trash"
            fullWidth={true}
            on:click={clearBrowserStorage}
          >
            Clear
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .dev-mode-window {
    position: fixed;
    width: 300px;
    max-height: 80vh;
    background: #1e293b;
    color: var(--border);
    border-radius: 12px;
    font-size: 0.875rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    z-index: 99999;
    border: 1px solid #334155;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .dev-mode-window.dragging {
    transform: scale(1.02);
    box-shadow: 0 25px 30px -5px rgba(0, 0, 0, 0.4), 0 15px 15px -5px rgba(0, 0, 0, 0.15);
    cursor: grabbing;
  }

  .window-header {
    background: #334155;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
    user-select: none;
    border-bottom: 1px solid #475569;
  }

  .window-header:active {
    cursor: grabbing;
  }

  .window-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #f1f5f9;
    font-size: 0.9rem;
  }


  .window-content {
    padding: 1rem;
    height: calc(80vh - 60px);
    display: flex;
    flex-direction: column;
  }

  .dev-description {
    margin: 0 0 1rem 0;
    font-size: 0.8rem;
    color: #94a3b8;
    line-height: 1.4;
  }

  .controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .control-group label {
    font-weight: 500;
    color: #f1f5f9;
    font-size: 0.8rem;
  }

  .color-picker-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .range-slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #475569;
    outline: none;
    border: none;
    -webkit-appearance: none;
    padding: 5px;
  }

  .range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    transition: background 0.2s ease;
  }

  .range-slider::-webkit-slider-thumb:hover {
    background: var(--color-primary);
  }

  .range-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: none;
    transition: background 0.2s ease;
  }

  .range-slider::-moz-range-thumb:hover {
    background: var(--color-primary);
  }

  .range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #94a3b8;
  }

  .color-picker {
    width: 40px;
    height: 32px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background: none;
    padding: 0;
  }

  .color-text {
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-size: 0.8rem;
    padding: 0.375rem 0.5rem;
    background: #334155;
    border: 1px solid #475569;
    border-radius: 4px;
    color: var(--border);
    width: 100%;
  }

  .color-text:focus {
    outline: none;
    border-color: var(--color-secondary);
    background: #1e293b;
  }

  .dev-actions {
    display: flex;
    gap: 0.5rem;
  }



  .storage-actions {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #475569;
  }


  /* Tab Navigation */
  .tab-nav {
    display: flex;
    background: #334155;
    border-radius: 6px;
    padding: 2px;
    margin-bottom: 1rem;
    gap: 2px;
  }

  :global(.tab-nav .tab-button) {
    flex: 1;
    padding: 0.5rem 0.75rem !important;
    border: none !important;
    background: transparent !important;
    color: #94a3b8 !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    cursor: pointer;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  :global(.tab-nav .tab-button:hover) {
    background: #475569 !important;
    color: var(--border) !important;
  }

  :global(.tab-nav .tab-button.active) {
    background: var(--color-primary) !important;
    color: white !important;
  }

  :global(.tab-nav .tab-button.active:hover) {
    background: var(--color-secondary) !important;
    color: white !important;
  }

  .tab-content {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1rem;
  }

  .actions-footer {
    border-top: 1px solid #475569;
    padding: 1rem;
    margin-top: auto;
    background: #1e293b;
  }

  .dev-actions {
    display: flex;
    gap: 0.75rem;
  }

  .storage-actions {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #475569;
  }

  /* Override button styles in dev mode actions */
  :global(.actions-footer .btn) {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.5rem !important;
    min-height: 28px;
  }
</style>