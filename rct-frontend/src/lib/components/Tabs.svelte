<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  export let tabs: Array<{
    id: string;
    label: string;
    icon?: string;
    count?: number;
  }> = [];
  export let activeTab: string = '';

  const dispatch = createEventDispatcher<{
    tabChange: { tabId: string }
  }>();

  function handleTabClick(tabId: string) {
    activeTab = tabId;
    dispatch('tabChange', { tabId });
  }
</script>

<div class="tabs-container">
  {#each tabs as tab}
    <button 
      class="tab-btn"
      class:active={activeTab === tab.id}
      on:click={() => handleTabClick(tab.id)}
    >
      {#if tab.icon}
        <i class="{tab.icon}"></i>
      {/if}
      <span class="tab-label">
        {tab.label}
        {#if tab.count !== undefined}
          ({tab.count})
        {/if}
      </span>
    </button>
  {/each}
</div>

<style>
  .tabs-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    background: #f1f5f9;
    padding: 0.5rem;
    border-radius: 0.75rem;
    width: fit-content;
  }

  .tab-btn {
    padding: 0.75rem 1.25rem;
    border: none;
    background: transparent;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    color: #64748b;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
  }

  .tab-btn:hover {
    color: var(--color-primary);
    background: rgba(37, 99, 235, 0.1);
  }

  .tab-btn.active {
    color: var(--color-primary);
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 600;
  }

  .tab-btn i {
    font-size: 0.875rem;
  }

  .tab-label {
    display: inline;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .tabs-container {
      width: 100%;
      overflow-x: auto;
      flex-wrap: nowrap;
    }

    .tab-btn {
      flex-shrink: 0;
      padding: 0.75rem 1rem;
    }
  }
</style>