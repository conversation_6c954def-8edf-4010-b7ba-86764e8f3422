<script lang="ts">
  import { page } from '$app/stores';
  import { base } from '$app/paths';

  // const logo = '/Rent-Collection-Toolkit-Logo-White.svg';  
  const logo = '/RCT-Long.svg';  
  
  function scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }
  
</script>

<header class="website-header">
  <div class="header-container">
    <div class="header-left">
      <a href="/" class="logo-link">
        <img src="{logo}" alt="Rent Collection Toolkit" class="header-logo" width="160" height="40">
      </a>
    </div>
    
    <nav class="header-nav">
      <a href="#features" on:click|preventDefault={() => scrollToSection('features')}>Features</a>
      <a href="#benefits" on:click|preventDefault={() => scrollToSection('benefits')}>Benefits</a>
      <a href="#pricing" on:click|preventDefault={() => scrollToSection('pricing')}>Pricing</a>
      <a href="#faq" on:click|preventDefault={() => scrollToSection('faq')}>FAQ</a>
    </nav>
    
    <div class="header-right">
      <a href="/app" class="btn-primary">Login to App</a>
    </div>
  </div>
</header>

<style>
  .website-header {
    background: var(--color-primary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.15);
  }

  .header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
  }

  .logo-link {
    display: inline-block;
    text-decoration: none;
    transition: opacity 0.2s ease;
  }

  .logo-link:hover {
    opacity: 0.8;
  }

  .header-logo {
    height: 40px;
    width: auto;
    display: block;
    max-width: 300px;
  }

  .header-nav {
    display: flex;
    gap: 2rem;
  }

  .header-nav a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .header-nav a:hover {
    color: #ffffff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  }

  .btn-primary {
    background: #ffffff;
    color: var(--color-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    display: inline-block;
    border: 2px solid transparent;
  }

  .btn-primary:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .header-container {
      padding: 1rem;
    }
    
    .header-nav {
      display: none;
    }
    
    .header-logo {
      height: 32px;
      max-width: 200px;
    }
    
    .btn-primary {
      padding: 0.5rem 1rem;
      font-size: 0.75rem;
    }
  }
</style>