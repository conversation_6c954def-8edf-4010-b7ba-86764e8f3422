<!--
  Reusable Button Component
  
  Usage:
  <Button variant="primary" size="md" icon="fas fa-save" on:click={handleSave}>
    Save Changes
  </Button>
  
  Props:
  - variant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' | 'outline'
  - size: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  - disabled: boolean
  - loading: boolean
  - fullWidth: boolean
  - type: 'button' | 'submit' | 'reset'
  - href: string (renders as link)
  - target: string (for links)
  - icon: string (FontAwesome class)
  - iconPosition: 'left' | 'right'
  - className: string (additional CSS classes)
-->

<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let variant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' | 'outline' | 'ghost-inverted' = 'primary';
  export let size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  export let disabled: boolean = false;
  export let loading: boolean = false;
  export let fullWidth: boolean = false;
  export let type: 'button' | 'submit' | 'reset' = 'button';
  export let href: string | undefined = undefined;
  export let target: string | undefined = undefined;
  export let icon: string | undefined = undefined;
  export let iconPosition: 'left' | 'right' = 'left';
  export let className: string = '';

  // Event dispatcher
  const dispatch = createEventDispatcher();

  function handleClick(event: MouseEvent) {
    if (!disabled && !loading) {
      dispatch('click', event);
    }
  }

  // Determine if this should render as a link
  $: isLink = href !== undefined;
  $: Tag = isLink ? 'a' : 'button';
</script>

<svelte:element 
  this={Tag}
  class="btn btn-{variant} btn-{size} {className}"
  class:btn-full={fullWidth}
  class:btn-loading={loading}
  class:btn-disabled={disabled}
  type={isLink ? undefined : type}
  role={isLink ? undefined : 'button'}
  {href}
  {target}
  {disabled}
  on:click={handleClick}
>
  {#if loading}
    <div class="btn-spinner"></div>
  {/if}
  
  {#if icon && iconPosition === 'left' && !loading}
    <i class="btn-icon btn-icon-left {icon}"></i>
  {/if}
  
  <span class="btn-content">
    <slot />
  </span>
  
  {#if icon && iconPosition === 'right' && !loading}
    <i class="btn-icon btn-icon-right {icon}"></i>
  {/if}
</svelte:element>

<style>
  .btn {
    font-family: 'Martian Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    line-height: 1.5;
    cursor: pointer;
    border: none;
    border-radius: 0.375rem;
    transition: all 150ms ease-in-out;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
  }

  .btn:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  .btn-disabled,
  .btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    pointer-events: none;
  }

  .btn-loading {
    cursor: not-allowed;
    pointer-events: none;
  }

  .btn-full {
    width: 100%;
  }

  /* Size variants */
  .btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: calc(var(--button-size) - 0.125rem);
    gap: 0.25rem;
  }

  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: calc(var(--button-size) - 0.125rem);
    gap: 0.375rem;
  }

  .btn-md {
    padding: 0.5rem 1rem;
    font-size: var(--button-size);
    gap: 0.5rem;
  }

  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: calc(var(--button-size) + 0.125rem);
    gap: 0.5rem;
  }

  .btn-xl {
    padding: 1rem 2rem;
    font-size: calc(var(--button-size) + 0.25rem);
    gap: 0.75rem;
  }

  /* Color variants */
  .btn-primary {
    background-color: var(--color-primary);
    color: #ffffff;
    border: 1px solid var(--color-primary);
  }

  .btn-primary:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
  }

  .btn-secondary {
    background-color: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }

  .btn-success {
    background-color: var(--color-green, #059669);
    color: #ffffff;
    border: 1px solid var(--color-green, #059669);
  }

  .btn-success:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #047857;
    border-color: #047857;
  }

  .btn-warning {
    background-color: var(--color-orange, #f59e0b);
    color: #ffffff;
    border: 1px solid var(--color-orange, #f59e0b);
  }

  .btn-warning:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #d97706;
    border-color: #d97706;
  }

  .btn-danger {
    background-color: var(--color-red, #dc2626);
    color: #ffffff;
    border: 1px solid var(--color-red, #dc2626);
  }

  .btn-danger:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #b91c1c;
    border-color: #b91c1c;
  }

  .btn-ghost {
    background-color: transparent;
    color: #374151;
    border: 1px solid transparent;
  }

  .btn-ghost:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #f3f4f6;
    color: #374151;
  }

  .btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
  }

  .btn-outline:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: var(--color-primary);
    color: #ffffff;
  }

  .btn-ghost-inverted {
    background-color: transparent;
    color: #ffffff;
    border: 1px solid transparent;
  }

  .btn-ghost-inverted:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }

  /* Loading spinner */
  .btn-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-radius: 50%;
    border-top-color: currentColor;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Icon styling */
  .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-icon-left {
    margin-left: -0.125rem;
  }

  .btn-icon-right {
    margin-right: -0.125rem;
  }

  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Loading state content hiding */
  .btn-loading .btn-content {
    opacity: 0.7;
  }

  /* Dark theme support for dev mode */
  :global(.dev-mode-window) .btn-secondary {
    background-color: #334155;
    color: var(--border);
    border-color: #475569;
  }

  :global(.dev-mode-window) .btn-secondary:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #475569;
    border-color: #64748b;
  }

  :global(.dev-mode-window) .btn-ghost {
    color: var(--border);
  }

  :global(.dev-mode-window) .btn-ghost:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #475569;
  }
</style>