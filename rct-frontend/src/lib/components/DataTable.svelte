<script lang="ts">
  export let columns: Array<{
    key: string;
    label: string;
    align?: 'left' | 'center' | 'right';
    type?: 'text' | 'currency' | 'custom' | 'label';
    width?: string;
  }> = [];
  
  export let rows: Array<Record<string, any>> = [];
  export let className: string = '';
  export let showHeader: boolean = true;
  export let hoverEffect: boolean = true;
  export let striped: boolean = false;
  export let matchInputHeight: boolean = false;
  
  function getCellAlignment(column: any): string {
    return column.align || 'left';
  }
  
  function getCellClass(column: any, value: any): string {
    const baseClass = `data-cell align-${getCellAlignment(column)}`;
    const typeClass = column.type ? `type-${column.type}` : '';
    return `${baseClass} ${typeClass}`.trim();
  }
  
  function formatCellValue(value: any, column: any): string {
    if (column.type === 'currency' && typeof value === 'number') {
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP'
      }).format(value);
    }
    return value?.toString() || '';
  }
</script>

<div class="data-table-container {className}">
  <div 
    class="data-table" 
    class:hover-effect={hoverEffect}
    class:striped
    class:match-row-height={matchInputHeight}
    style="grid-template-columns: {columns.map(col => col.width || '1fr').join(' ')}"
  >
    {#if showHeader}
      <!-- Header row -->
      {#each columns as column}
        <div class="header-cell align-{getCellAlignment(column)}">
          {column.label}
        </div>
      {/each}
    {/if}
    
    <!-- Data rows -->
    {#each rows as row, rowIndex}
      {#each columns as column, columnIndex}
        {@const shouldSkip = columnIndex > 0 && 
          columns.slice(0, columnIndex).some((prevCol, prevIndex) => {
            const prevStyle = row[prevCol.key + '_style'] || '';
            const spanMatch = prevStyle.match(/grid-column:\s*span\s*(\d+)/);
            if (spanMatch) {
              const spanCount = parseInt(spanMatch[1]);
              return prevIndex + spanCount > columnIndex;
            }
            return false;
          })
        }
        {#if !shouldSkip}
          {@const isSpanning = (row[column.key + '_style'] || '').includes('grid-column')}
          <div class="{getCellClass(column, row[column.key])} row-{rowIndex} {row._rowClass || ''}" 
               class:last-row-cell={rowIndex === rows.length - 1}
               class:last-column-cell={columnIndex === columns.length - 1}
               class:spanning-cell={isSpanning}
               style="{row[column.key + '_style'] || ''} {row._rowStyle || ''}">
            {#if row[column.key + '_html']}
              {@html row[column.key + '_html']}
            {:else if column.type === 'currency' && typeof row[column.key] === 'number'}
              <span class="currency-symbol">£</span>
              <span class="currency-value">{formatCellValue(row[column.key], column).replace('£', '')}</span>
            {:else if row[column.key] !== undefined && row[column.key] !== null && row[column.key] !== ''}
              {formatCellValue(row[column.key], column)}
            {/if}
          </div>
        {/if}
      {/each}
    {/each}
  </div>
</div>

<style lang="less">
  .data-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border);
  }

  .data-table {
    display: grid;
    width: 100%;
    &.match-row-height {
      span:last-child {
        padding: calc(var(--cell-padding) + 3.2px);
        display: inline-flex;

      }
      .data-cell {
        &:nth-child(3n + 4) {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .header-cell {
    background: #f8fafc;
    color: #374151;
    font-weight: 700;
    font-size: var(--label-size);
    padding: var(--cell-padding);
    border-bottom: 2px solid var(--border);
    border-right: 1px solid var(--border);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    // &:nth-child(3) {
    //   border-right: none;
    // }
  }

  .header-cell:last-child {
    border-right: none;
  }

  .data-cell {
    padding: var(--cell-padding);
    border-bottom: 1px solid var(--border);
    border-right: 1px solid var(--border);
    font-size: var(--value-size);
    transition: background-color 0.2s ease;

  }

  .last-column-cell {
    border-right: none;
  }

  .spanning-cell {
    border-right: none;
  }

  /* Remove border-bottom from last row */
  .last-row-cell {
    border-bottom: none;
  }

  /* Alignment classes */
  .align-left {
    text-align: left;
  }

  .align-center {
    text-align: center;
  }

  .align-right {
    text-align: right;
  }

  /* Type-specific styling */
  .type-currency {
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-weight: 600;
  }

  .currency-symbol {
    opacity: 0.7;
    margin-right: 0.1em;
  }

  .currency-value {
    font-variant-numeric: tabular-nums;
  }

  /* Label cell styling */
  .data-cell.type-label {
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--label-size);
    background-color: #f8fafc;
  }

  /* Hover effects */
  .data-table.hover-effect .data-cell:hover {
    background-color: #f9fafb;
  }

  /* Striped rows */
  .data-table.striped .data-cell:nth-child(odd) {
    background-color: #f9fafb;
  }

  /* Row-level styling classes */
  .balance-critical {
    background-color: #fef2f2 !important;
    color: #7f1d1d;
  }

  .balance-warning {
    background-color: #fffbeb !important;
    color: #92400e;
  }

  .balance-debt {
    background-color: #fef7f7 !important;
  }

  .balance-credit {
    background-color: #f0fdf4 !important;
  }

  .current-week {
    background-color: #f3f4f6 !important;
    &:nth-child(4) {
      border-left: 4px solid var(--color-primary) !important;
    }    
  }

  .has-payment {
    background-color: var(--color-secondary-light) !important;
    &:nth-child(4n+1) {
      border-left: 4px solid var(--color-secondary) !important;
    }
  }

  /* Indicator styling */
  :global(.data-table .week-indicator) {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
  }

  :global(.data-table .week-indicator.current) {
    background-color: var(--color-primary);
    color: white;
  }

  :global(.data-table .payment-indicator) {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
    background-color: #059669;
    color: white;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .header-cell,
    .data-cell {
      padding: 0.75rem 0.5rem;
      font-size: 0.875rem;
    }

    .header-cell {
      font-size: 0.8rem;
    }
  }
</style>