<script lang="ts">
  import OutputGroup from './OutputGroup.svelte';

  // export let title: string = "Synchronised Data from Rent Calculator";
  export let items: Array<{
    label: string;
    value: string;
    color?: string;
  }> = [];
</script>

<div class="sync-data">
  <!-- <h4>{title}</h4> -->
  <div class="sync-grid">
    {#each items as item}
      <OutputGroup
        label={item.label}
        value={item.value}
        color={item.color || '#111827'}
        showCurrency={item.value.includes('£')}
      />
    {/each}
  </div>
</div>

<style>
  /* Synchronised Data Section */
  .sync-data {
    background-color: #f9fafb;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .sync-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--gap);
  }

  /* Responsive Design */
</style>