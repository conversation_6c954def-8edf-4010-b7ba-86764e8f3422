<script lang="ts">
  import WebsiteHeader from './WebsiteHeader.svelte';
  import WebsiteFooter from './WebsiteFooter.svelte';
  
  export let title: string;
  export let subtitle: string = '';
  export let heroBackground: string = 'var(--color-primary)';
  export let showHero: boolean = true;
</script>

<svelte:head>
  <title>{title} - Rent Collection Toolkit</title>
  <meta name="description" content={subtitle || title}>
</svelte:head>

<WebsiteHeader />

<main class="website-main">
  {#if showHero}
    <section class="page-hero" style="background: {heroBackground}">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{title}</h1>
          {#if subtitle}
            <p class="hero-subtitle">{subtitle}</p>
          {/if}
        </div>
      </div>
    </section>
  {/if}
  
  <div class="page-content">
    <div class="container">
      <slot />
    </div>
  </div>
</main>

<WebsiteFooter />

<style>
  .website-main {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .page-hero {
    color: white;
    padding: 4rem 0;
    position: relative;
  }

  .hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
  }

  .hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    margin: 0;
  }

  .hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    margin: 1.5rem 0 0 0;
    opacity: 0.9;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }

  .page-content {
    flex: 1;
    padding: 4rem 0;
    background: white;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .page-hero {
      padding: 3rem 0;
    }
    
    .hero-content h1 {
      font-size: 2.5rem;
    }
    
    .hero-subtitle {
      font-size: 1.1rem;
    }
    
    .page-content {
      padding: 3rem 0;
    }
    
    .container {
      padding: 0 1rem;
    }
  }
</style>