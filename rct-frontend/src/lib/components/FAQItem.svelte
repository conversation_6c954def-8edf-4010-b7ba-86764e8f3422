<script lang="ts">
  import { slide } from 'svelte/transition';
  
  export let question: string;
  export let answer: string;
  export let isOpen: boolean = false;

  function toggleOpen() {
    isOpen = !isOpen;
  }
</script>

<div class="faq-item">
  <button 
    class="faq-question" 
    on:click={toggleOpen}
    aria-expanded={isOpen}
    aria-controls="answer-{question.slice(0, 10)}"
  >
    <span class="question-text">{question}</span>
    <span class="toggle-icon" class:open={isOpen}>
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 8L10 12L14 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </span>
  </button>
  
  {#if isOpen}
    <div 
      class="faq-answer" 
      id="answer-{question.slice(0, 10)}"
      transition:slide={{ duration: 200 }}
    >
      <p>{answer}</p>
    </div>
  {/if}
</div>

<style lang="less">
  .faq-item {
    background: white;
    border: 1px solid var(--border);
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
  }

  .faq-question {
    width: 100%;
    padding: 1.25rem 1.5rem;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &:focus {
      outline: none;
      background-color: rgba(59, 130, 246, 0.05);
    }
  }

  .question-text {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.5;
    flex: 1;
  }

  .toggle-icon {
    flex-shrink: 0;
    color: #64748b;
    transition: transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &.open {
      transform: rotate(180deg);
    }
  }

  .faq-answer {
    padding: 0 1.5rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    
    p {
      margin: 1rem 0 0;
      font-size: 0.95rem;
      line-height: 1.7;
      color: #475569;
      white-space: pre-line;
    }
  }
</style>