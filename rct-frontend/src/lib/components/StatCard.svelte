<script lang="ts">
  export let label: string;
  export let value: string | number;
  export let subtitle: string = '';
  export let trend: 'up' | 'down' | 'neutral' | null = null;
  export let trendValue: string = '';
  export let color: 'primary' | 'success' | 'warning' | 'danger' | 'neutral' = 'neutral';
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let icon: string = '';
  
  const colorClasses = {
    primary: 'text-blue-600 bg-blue-50 border-blue-200',
    success: 'text-green-600 bg-green-50 border-green-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    danger: 'text-red-600 bg-red-50 border-red-200',
    neutral: 'text-gray-600 bg-gray-50 border-gray-200'
  };
  
  const sizeClasses = {
    small: '',
    medium: '',
    large: ''
  };
  
  const valueSizeClasses = {
    small: 'text-lg',
    medium: 'text-2xl',
    large: 'text-3xl'
  };
  
  const trendIcons = {
    up: 'fa-arrow-up',
    down: 'fa-arrow-down',
    neutral: 'fa-minus'
  };
  
  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600'
  };
</script>

<div class="stat-card">
  <div class="stat-header">
    <div class="stat-label-container">
      {#if icon}
        <i class="fas {icon} stat-icon"></i>
      {/if}
      <span class="stat-label">{label}</span>
    </div>
    
    {#if trend && trendValue}
      <div class="trend-indicator {trendColors[trend]}">
        <i class="fas {trendIcons[trend]} trend-icon"></i>
        <span class="trend-value">{trendValue}</span>
      </div>
    {/if}
  </div>
  
  <div class="stat-value-container">
    <div class="stat-value {valueSizeClasses[size]} {color === 'neutral' ? 'text-gray-900' : colorClasses[color].split(' ')[0]}">{value}</div>
    {#if subtitle}
      <div class="stat-subtitle">{subtitle}</div>
    {/if}
  </div>
</div>

<style>
  .stat-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--gap);
  }
  
  .stat-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
  }
  
  .stat-label-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .stat-icon {
    color: var(--color-primary);
    font-size: 0.875rem;
  }
  
  .stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-grey);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
  
  .trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .trend-icon {
    font-size: 0.625rem;
  }
  
  .stat-value-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .stat-value {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.25rem;
  }
  
  .stat-subtitle {
    font-size: 0.75rem;
    color: var(--color-grey);
    font-weight: 500;
  }
  
  /* Color variants */
  .text-blue-600 { color: #2563eb; }
  .text-green-600 { color: #059669; }
  .text-yellow-600 { color: #d97706; }
  .text-red-600 { color: #dc2626; }
  .text-gray-600 { color: #4b5563; }
  .text-gray-900 { color: #111827; }
</style>