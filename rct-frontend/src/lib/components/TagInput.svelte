<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { settings, type Tag } from '$lib/stores/settings';
  
  const dispatch = createEventDispatcher();
  
  export let selectedTags: string[] = [];
  export let placeholder = 'Add tags...';
  export let dropdownDirection: 'up' | 'down' = 'down';
  
  let inputValue = '';
  let showDropdown = false;
  let inputElement: HTMLInputElement;
  let dropdownElement: HTMLDivElement;
  let focusedIndex = -1;
  
  // Filter available tags based on input and exclude already selected
  $: availableTags = $settings.tags.filter(tag => 
    tag.name.toLowerCase().includes(inputValue.toLowerCase()) &&
    !selectedTags.includes(tag.id)
  );
  
  $: filteredTags = availableTags.slice(0, 8); // Limit to 8 suggestions
  
  // Get selected tag objects for display
  $: selectedTagObjects = selectedTags.map(tagId => 
    $settings.tags.find(tag => tag.id === tagId)
  ).filter(Boolean) as Tag[];
  
  function handleInput() {
    showDropdown = inputValue.length > 0 && filteredTags.length > 0;
    focusedIndex = -1;
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      focusedIndex = Math.min(focusedIndex + 1, filteredTags.length - 1);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      focusedIndex = Math.max(focusedIndex - 1, -1);
    } else if (event.key === 'Enter') {
      event.preventDefault();
      if (focusedIndex >= 0 && filteredTags[focusedIndex]) {
        selectTag(filteredTags[focusedIndex]);
      }
    } else if (event.key === 'Escape') {
      showDropdown = false;
      focusedIndex = -1;
      inputElement.blur();
    } else if (event.key === 'Backspace' && inputValue === '' && selectedTags.length > 0) {
      // Remove last tag if input is empty and backspace is pressed
      removeTag(selectedTags[selectedTags.length - 1]);
    }
  }
  
  function selectTag(tag: Tag) {
    if (!selectedTags.includes(tag.id)) {
      selectedTags = [...selectedTags, tag.id];
      dispatch('tagsChanged', selectedTags);
    }
    inputValue = '';
    showDropdown = false;
    focusedIndex = -1;
    inputElement.focus();
  }
  
  function removeTag(tagId: string) {
    selectedTags = selectedTags.filter(id => id !== tagId);
    dispatch('tagsChanged', selectedTags);
  }
  
  function handleClickOutside(event: MouseEvent) {
    if (!inputElement?.contains(event.target as Node) && 
        !dropdownElement?.contains(event.target as Node)) {
      showDropdown = false;
      focusedIndex = -1;
    }
  }
  
  function handleFocus() {
    if (inputValue.length > 0 && filteredTags.length > 0) {
      showDropdown = true;
    }
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="tag-input-container">
  <div class="tag-input-wrapper">
    <!-- Selected Tags -->
    <div class="selected-tags">
      {#each selectedTagObjects as tag}
        <span class="tag-chip" style="background-color: {tag.color};">
          {tag.name}
          <button 
            class="tag-remove" 
            on:click={() => removeTag(tag.id)}
            title="Remove tag"
          >
            <i class="fas fa-times"></i>
          </button>
        </span>
      {/each}
    </div>
    
    <!-- Input Field -->
    <input
      bind:this={inputElement}
      bind:value={inputValue}
      on:input={handleInput}
      on:keydown={handleKeydown}
      on:focus={handleFocus}
      {placeholder}
      class="tag-input"
      autocomplete="off"
    />
  </div>
  
  <!-- Dropdown -->
  {#if showDropdown}
    <div bind:this={dropdownElement} class="tag-dropdown" class:dropdown-up={dropdownDirection === 'up'}>
      {#each filteredTags as tag, index}
        <button
          class="tag-option"
          class:focused={index === focusedIndex}
          on:click={() => selectTag(tag)}
        >
          <span class="tag-color-indicator" style="background-color: {tag.color};"></span>
          <span class="tag-name">{tag.name}</span>
          {#if tag.description}
            <span class="tag-description">{tag.description}</span>
          {/if}
        </button>
      {/each}
      {#if filteredTags.length === 0}
        <div class="no-tags">No matching tags found</div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .tag-input-container {
    position: relative;
    width: 100%;
  }

  .tag-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    /* gap: 0.25rem; */
    padding: 0 0 0 0.1rem;
    /* border: 1px solid #d1d5db; */
    border-radius: 0.375rem;
    background: white;
    min-height: 42px;
    align-items: center;
  }

  .tag-input-wrapper:focus-within {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .tag-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    margin-right: 0.25rem;
  }

  .tag-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }

  .tag-remove:hover {
    opacity: 1;
  }

  .tag-remove i {
    font-size: 0.625rem;
  }

  .tag-input {
    flex: 1;
    min-width: 120px;
    border: none;
    outline: none;
    padding: 0.25rem 0;
    font-size: 0.875rem;
    background: transparent;
  }

  .tag-input::placeholder {
    color: #9ca3af;
  }

  .tag-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 50;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 4px;
  }

  .tag-dropdown.dropdown-up {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 4px;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .tag-option {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .tag-option:hover,
  .tag-option.focused {
    background: #f3f4f6;
  }

  .tag-color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .tag-name {
    font-weight: 500;
    font-size: 0.875rem;
    color: #374151;
  }

  .tag-description {
    font-size: 0.75rem;
    color: #6b7280;
    margin-left: auto;
  }

  .no-tags {
    padding: 0.75rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
    font-style: italic;
  }

  /* Scrollbar styling */
  .tag-dropdown::-webkit-scrollbar {
    width: 6px;
  }

  .tag-dropdown::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  .tag-dropdown::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .tag-dropdown::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
</style>