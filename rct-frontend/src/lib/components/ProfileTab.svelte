<script lang="ts">
  import { onMount } from 'svelte';
  import { currentUser, updateUser, viewMode, refreshUserData } from '$lib/stores';
  import type { ProfileTab } from '$lib/stores';
  import {
    userPreferences,
    updateUserPreferences,
    updateDesignSystemSettings,
    resetDesignSystemToDefaults,
    resetAllPreferencesToDefaults,
    calendarPreferenceLabels,
    type CalendarPreference
  } from '$lib/stores/userPreferences';
  import Button from './Button.svelte';
  import FormGroup from './FormGroup.svelte';
  import Tabs from './Tabs.svelte';
  
  export let tab: ProfileTab;
  
  // Active tab state
  let activeTab: 'personal' | 'preferences' | 'design' = 'personal';
  

  
  // Tab configuration
  const mainTabs = [
    {
      id: 'personal',
      label: 'Personal Information',
      icon: 'fas fa-user'
    },
    {
      id: 'preferences',
      label: 'App Preferences',
      icon: 'fas fa-cog'
    },
    {
      id: 'design',
      label: 'Design System',
      icon: 'fas fa-palette'
    }
  ];
  

  
  function handleMainTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId as 'personal' | 'preferences' | 'design';
  }
  

  
  // Form state
  let editingPersonal = false;
  let personalForm = {
    email: '',
    organizationName: '',
    firstName: '',
    lastName: '',
    jobTitle: '',
    department: ''
  };
  
  // Initialize form with current user data
  $: if ($currentUser && !editingPersonal) {
    personalForm = {
      email: $currentUser.email || '',
      organizationName: $currentUser.organizationName || '',
      firstName: $currentUser.firstName || '',
      lastName: $currentUser.lastName || '',
      jobTitle: $currentUser.jobTitle || '',
      department: $currentUser.department || ''
    };
  }
  
  function startEditingPersonal() {
    editingPersonal = true;
  }
  
  function cancelEditingPersonal() {
    editingPersonal = false;
    // Reset form to current user data
    if ($currentUser) {
      personalForm = {
        email: $currentUser.email || '',
        organizationName: $currentUser.organizationName || '',
        firstName: $currentUser.firstName || '',
        lastName: $currentUser.lastName || '',
        jobTitle: $currentUser.jobTitle || '',
        department: $currentUser.department || ''
      };
    }
  }
  
  function savePersonalInfo() {
    if ($currentUser) {
      // Only update editable fields (exclude email and organizationName)
      const { email, organizationName, ...editableFields } = personalForm;
      updateUser({
        ...$currentUser,
        ...editableFields
      });
      editingPersonal = false;
    }
  }
  
  function toggleViewMode() {
    const newMode = $viewMode === 'compact' ? 'full' : 'compact';
    viewMode.set(newMode);
  }
  
  // Navigation is always in header mode now - sidebar system removed

  // Design system update handlers
  function handleDesignSystemChange(property: string, value: number | string) {
    updateDesignSystemSettings({ [property]: value });
  }

  function resetDesignSystem() {
    resetDesignSystemToDefaults();
  }

  function resetAllSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      resetAllPreferencesToDefaults();
    }
  }
  
  let isLoadingProfile = true;
  let profileLoadError = '';

  onMount(async () => {
    try {
      // Refresh user data from API (no localStorage fallback)
      const success = await refreshUserData();
      if (!success || !$currentUser) {
        profileLoadError = 'Unable to load profile data from server.';
      }
    } catch (error) {
      console.error('Failed to load profile data:', error);
      profileLoadError = 'Failed to load profile data. Please try again.';
    } finally {
      isLoadingProfile = false;
    }
  });
</script>

<div class="profile-tab-container">
  <div class="profile-header">
    <div class="header-content">
      <div class="header-title">
        <h1>Profile Settings</h1>
        <p>Manage your account and application preferences</p>
      </div>
    </div>
  </div>

  {#if isLoadingProfile}
    <div class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading profile data...</p>
      </div>
    </div>
  {:else if profileLoadError}
    <div class="error-container">
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{profileLoadError}</p>
        <Button variant="primary" on:click={() => window.location.reload()}>
          Reload
        </Button>
      </div>
    </div>
  {:else}

  <div class="settings-container">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <Tabs
        tabs={mainTabs}
        bind:activeTab
        on:tabChange={handleMainTabChange}
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      {#if activeTab === 'personal'}
        <!-- Personal Information Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Personal Information</h2>
            <p>Manage your personal details and contact information</p>
          </div>
          
          {#if !editingPersonal}
            <div class="section-action-header">
              <Button
                variant="primary"
                size="sm"
                icon="fas fa-edit"
                on:click={startEditingPersonal}
              >
                Edit Information
              </Button>
            </div>
          {/if}

          <div class="info-container">
            {#if editingPersonal}
              <!-- Edit Mode -->
              <div class="edit-form">
                <div class="form-grid">
                  <div class="form-group">
                    <label>Email Address</label>
                    <input type="email" bind:value={personalForm.email} readonly class="readonly-field" />
                  </div>
                  <div class="form-group">
                    <label>Organization</label>
                    <input type="text" bind:value={personalForm.organizationName} readonly class="readonly-field" />
                  </div>
                  <div class="form-group">
                    <label>First Name</label>
                    <input type="text" bind:value={personalForm.firstName} />
                  </div>
                  <div class="form-group">
                    <label>Last Name</label>
                    <input type="text" bind:value={personalForm.lastName} />
                  </div>
                  <div class="form-group">
                    <label>Job Title</label>
                    <input type="text" bind:value={personalForm.jobTitle} />
                  </div>
                  <div class="form-group">
                    <label>Department</label>
                    <input type="text" bind:value={personalForm.department} />
                  </div>
                </div>
                <div class="form-actions">
                  <Button variant="primary" on:click={savePersonalInfo}>Save Changes</Button>
                  <Button variant="ghost" on:click={cancelEditingPersonal}>Cancel</Button>
                </div>
              </div>
            {:else}
              <!-- Display Mode -->
              <div class="info-display">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">Email Address:</span>
                    <span class="info-value">{$currentUser?.email || 'Not set'}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Organisation:</span>
                    <span class="info-value">{$currentUser?.organizationName || 'Not set'}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">First Name:</span>
                    <span class="info-value">{$currentUser?.firstName || 'Not set'}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Last Name:</span>
                    <span class="info-value">{$currentUser?.lastName || 'Not set'}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Job Title:</span>
                    <span class="info-value">{$currentUser?.jobTitle || 'Not set'}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Department:</span>
                    <span class="info-value">{$currentUser?.department || 'Not set'}</span>
                  </div>
                </div>
              </div>
            {/if}
          </div>
        </div>
      {/if}

      {#if activeTab === 'preferences'}
        <!-- App Preferences Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Application Preferences</h2>
            <p>Customize how the application works for you</p>
          </div>

          <div class="preferences-container">
            <div class="preferences-grid">
              <!-- Left Column -->
              <div class="preferences-column">
                <div class="preference-group">
                  <h3>View Mode</h3>
                  <p>Choose how you want to view your rent calculations</p>
                  <div class="preference-options">
                    <Button
                      variant={$viewMode === 'compact' ? 'primary' : 'outline'}
                      icon="fas fa-compress-alt"
                      on:click={() => viewMode.set('compact')}
                    >
                      Tab View
                    </Button>
                    <Button
                      variant={$viewMode === 'full' ? 'primary' : 'outline'}
                      icon="fas fa-expand-alt"
                      on:click={() => viewMode.set('full')}
                    >
                      Full View
                    </Button>
                  </div>
                </div>

                <!-- Navigation Style setting removed - header navigation is now the only option -->

                <div class="preference-group">
                  <h3>Tab Close Confirmation</h3>
                  <p>Choose whether to show a confirmation dialog when closing tabs</p>
                  <div class="preference-options">
                    <Button
                      variant={$userPreferences.confirmTabClose ? 'primary' : 'outline'}
                      icon="fas fa-shield-alt"
                      on:click={() => updateUserPreferences({ confirmTabClose: true })}
                    >
                      Ask for Confirmation
                    </Button>
                    <Button
                      variant={!$userPreferences.confirmTabClose ? 'primary' : 'outline'}
                      icon="fas fa-bolt"
                      on:click={() => updateUserPreferences({ confirmTabClose: false })}
                    >
                      Close Immediately
                    </Button>
                  </div>
                </div>

                <div class="form-section">
                  <h4><i class="fas fa-comments"></i> Quick Responses</h4>
                  <p>Choose whether to show quick response suggestions when typing notes</p>
                  <div class="preference-options">
                    <Button
                      variant={$userPreferences.showQuickResponses ? 'primary' : 'outline'}
                      icon="fas fa-comments"
                      on:click={() => updateUserPreferences({ showQuickResponses: true })}
                    >
                      Show Quick Responses
                    </Button>
                    <Button
                      variant={!$userPreferences.showQuickResponses ? 'primary' : 'outline'}
                      icon="fas fa-eye-slash"
                      on:click={() => updateUserPreferences({ showQuickResponses: false })}
                    >
                      Hide Quick Responses
                    </Button>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="preferences-column">
                <div class="preference-group">
                  <h3>Default Calendar Application</h3>
                  <p>Choose your preferred calendar app for adding events</p>
                  <div class="radio-group">
                    {#each Object.entries(calendarPreferenceLabels) as [value, label]}
                      <label class="radio-option">
                        <input
                          type="radio"
                          name="calendarPreference"
                          {value}
                          checked={$userPreferences.calendarPreference === value}
                          on:change={() => updateUserPreferences({ calendarPreference: value as CalendarPreference })}
                        />
                        <span class="radio-custom"></span>
                        <i class="fas fa-calendar radio-icon"></i>
                        <span class="radio-label">{label}</span>
                      </label>
                    {/each}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {/if}

      {#if activeTab === 'design'}
        <!-- Design System Tab -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Design System</h2>
            <p>Customize the visual appearance of the application</p>
          </div>

          <!-- Two Column Layout for Sizes and Colors -->
          <div class="design-system-grid">
            <!-- Left Column - Size Controls -->
            <div class="design-column">
              <h3>Size Settings</h3>
              <div class="design-controls">
                <div class="control-group">
                  <label for="section-header-size">Section Header Size: {$userPreferences.designSystem.sectionHeaderSize}rem</label>
                  <input
                    id="section-header-size"
                    type="range"
                    min="0.5"
                    max="1.5"
                    step="0.025"
                    value={$userPreferences.designSystem.sectionHeaderSize}
                    on:input={(e) => handleDesignSystemChange('sectionHeaderSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="subheader-size">Subheader Size: {$userPreferences.designSystem.subheaderSize}rem</label>
                  <input
                    id="subheader-size"
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.025"
                    value={$userPreferences.designSystem.subheaderSize}
                    on:input={(e) => handleDesignSystemChange('subheaderSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="label-weight">Label Weight: {$userPreferences.designSystem.labelWeight}</label>
                  <input
                    id="label-weight"
                    type="range"
                    min="100"
                    max="900"
                    step="100"
                    value={$userPreferences.designSystem.labelWeight}
                    on:input={(e) => handleDesignSystemChange('labelWeight', parseInt(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>100</span>
                    <span>900</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="label-size">Label Size: {$userPreferences.designSystem.labelSize}rem</label>
                  <input
                    id="label-size"
                    type="range"
                    min="0.5"
                    max="1.5"
                    step="0.025"
                    value={$userPreferences.designSystem.labelSize}
                    on:input={(e) => handleDesignSystemChange('labelSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="value-size">Value Size: {$userPreferences.designSystem.valueSize}rem</label>
                  <input
                    id="value-size"
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.025"
                    value={$userPreferences.designSystem.valueSize}
                    on:input={(e) => handleDesignSystemChange('valueSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="button-size">Button Size: {$userPreferences.designSystem.buttonSize}rem</label>
                  <input
                    id="button-size"
                    type="range"
                    min="0.5"
                    max="1.5"
                    step="0.025"
                    value={$userPreferences.designSystem.buttonSize}
                    on:input={(e) => handleDesignSystemChange('buttonSize', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0.5rem</span>
                    <span>1.5rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="cell-padding">Cell Padding: {$userPreferences.designSystem.cellPadding}rem</label>
                  <input
                    id="cell-padding"
                    type="range"
                    min="0"
                    max="2"
                    step="0.025"
                    value={$userPreferences.designSystem.cellPadding}
                    on:input={(e) => handleDesignSystemChange('cellPadding', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0rem</span>
                    <span>2rem</span>
                  </div>
                </div>

                <div class="control-group">
                  <label for="gap">Gap (Spacing): {$userPreferences.designSystem.gap}rem</label>
                  <input
                    id="gap"
                    type="range"
                    min="0"
                    max="2"
                    step="0.05"
                    value={$userPreferences.designSystem.gap}
                    on:input={(e) => handleDesignSystemChange('gap', parseFloat(e.target.value))}
                    class="range-slider"
                  />
                  <div class="range-labels">
                    <span>0rem</span>
                    <span>2rem</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column - Color Controls -->
            <div class="design-column">
              <h3>Color Settings</h3>
              <div class="design-controls">
                <div class="color-control-group">
                  <label for="color-primary">Primary Color</label>
                  <div class="color-picker-container">
                    <input
                      id="color-primary"
                      type="color"
                      value={$userPreferences.designSystem.colorPrimary}
                      on:input={(e) => handleDesignSystemChange('colorPrimary', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.colorPrimary}
                      on:input={(e) => handleDesignSystemChange('colorPrimary', e.target.value)}
                      class="color-text"
                      placeholder="#025582"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-secondary">Secondary Color</label>
                  <div class="color-picker-container">
                    <input
                      id="color-secondary"
                      type="color"
                      value={$userPreferences.designSystem.colorSecondary}
                      on:input={(e) => handleDesignSystemChange('colorSecondary', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.colorSecondary}
                      on:input={(e) => handleDesignSystemChange('colorSecondary', e.target.value)}
                      class="color-text"
                      placeholder="rgb(0, 51, 68)"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="label-color">Label Color</label>
                  <div class="color-picker-container">
                    <input
                      id="label-color"
                      type="color"
                      value={$userPreferences.designSystem.labelColor}
                      on:input={(e) => handleDesignSystemChange('labelColor', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.labelColor}
                      on:input={(e) => handleDesignSystemChange('labelColor', e.target.value)}
                      class="color-text"
                      placeholder="#374151"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-red">Red Color</label>
                  <div class="color-picker-container">
                    <input
                      id="color-red"
                      type="color"
                      value={$userPreferences.designSystem.colorRed}
                      on:input={(e) => handleDesignSystemChange('colorRed', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.colorRed}
                      on:input={(e) => handleDesignSystemChange('colorRed', e.target.value)}
                      class="color-text"
                      placeholder="#dc2626"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-orange">Orange Color</label>
                  <div class="color-picker-container">
                    <input
                      id="color-orange"
                      type="color"
                      value={$userPreferences.designSystem.colorOrange}
                      on:input={(e) => handleDesignSystemChange('colorOrange', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.colorOrange}
                      on:input={(e) => handleDesignSystemChange('colorOrange', e.target.value)}
                      class="color-text"
                      placeholder="#ea580c"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-yellow">Yellow Color</label>
                  <div class="color-picker-container">
                    <input
                      id="color-yellow"
                      type="color"
                      value={$userPreferences.designSystem.colorYellow}
                      on:input={(e) => handleDesignSystemChange('colorYellow', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.colorYellow}
                      on:input={(e) => handleDesignSystemChange('colorYellow', e.target.value)}
                      class="color-text"
                      placeholder="#ca8a04"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="color-green">Green Color</label>
                  <div class="color-picker-container">
                    <input
                      id="color-green"
                      type="color"
                      value={$userPreferences.designSystem.colorGreen}
                      on:input={(e) => handleDesignSystemChange('colorGreen', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.colorGreen}
                      on:input={(e) => handleDesignSystemChange('colorGreen', e.target.value)}
                      class="color-text"
                      placeholder="#059669"
                    />
                  </div>
                </div>

                <div class="color-control-group">
                  <label for="bg-color">Background Color</label>
                  <div class="color-picker-container">
                    <input
                      id="bg-color"
                      type="color"
                      value={$userPreferences.designSystem.bgColor}
                      on:input={(e) => handleDesignSystemChange('bgColor', e.target.value)}
                      class="color-picker"
                    />
                    <input
                      type="text"
                      value={$userPreferences.designSystem.bgColor}
                      on:input={(e) => handleDesignSystemChange('bgColor', e.target.value)}
                      class="color-text"
                      placeholder="#0255822d"
                    />
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
  {/if}
</div>

<style>
  .profile-tab-container {
    padding: 1.5rem;
    max-width: 100%;
    height: 100%;
    overflow-y: auto;
  }

  .profile-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .header-title h1 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .header-title p {
    margin: 0;
    color: #6b7280;
  }

  .settings-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .tab-content {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .settings-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .section-header h2 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .section-header p {
    margin: 0;
    color: #6b7280;
  }

  .section-action-header {
    display: flex;
    justify-content: flex-end;
  }

  .edit-form {
    background: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .form-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
  }

  /* Form styles */
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .form-group .required {
    color: #ef4444;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.15s ease;
  }

  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .readonly-field {
    background-color: #f9fafb !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
    border-color: #e5e7eb !important;
  }

  .readonly-field:focus {
    border-color: #e5e7eb !important;
    box-shadow: none !important;
  }

  .info-display {
    background: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-label {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.875rem;
  }

  .info-value {
    color: #111827;
    font-weight: 500;
  }

  .preferences-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .preferences-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .preferences-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .preference-group h3 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .preference-group p {
    margin: 0 0 1rem 0;
    color: #6b7280;
  }

  .preference-options {
    display: flex;
    gap: 0.5rem;
  }

  .design-system-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
  }

  .design-column {
    display: flex;
    flex-direction: column;
  }

  .design-column h3 {
    margin: 0 0 1rem 0;
    color: #111827;
    font-size: 1.125rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  @media (max-width: 768px) {
    .design-system-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }



  /* Radio Groups */
  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .radio-option:hover {
    background: #f9fafb;
    border-color: #d1d5db;
  }

  .radio-option input {
    display: none;
  }

  .radio-custom {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .radio-option input:checked + .radio-custom {
    border-color: var(--color-primary);
  }

  .radio-option input:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--color-primary);
    border-radius: 50%;
  }

  .radio-icon {
    color: var(--color-primary);
    font-size: 0.875rem;
    flex-shrink: 0;
  }

  .radio-label {
    font-weight: 500;
    color: #374151;
    flex: 1;
  }

  /* Loading and Error States */
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    padding: 2rem;
  }

  .loading-spinner,
  .error-message {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .loading-spinner i {
    font-size: 2rem;
    color: var(--color-primary);
  }

  .error-message i {
    font-size: 2rem;
    color: var(--color-red);
  }

  .loading-spinner p,
  .error-message p {
    margin: 0;
    color: #6b7280;
    font-size: 1rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .profile-tab-container {
      padding: 1rem;
    }
    
    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }
    
    .form-grid,
    .info-grid {
      grid-template-columns: 1fr;
    }
    
    .preference-options {
      flex-direction: column;
    }

    .preferences-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  /* Design System Controls */
  .design-controls {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .control-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
  }

  .range-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e5e7eb;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
  }

  .range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--color-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }

  .color-control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .color-control-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
  }

  .color-picker-container {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .color-picker {
    width: 50px;
    height: 40px;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    background: none;
    padding: 0;
  }

  .color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  .color-picker::-webkit-color-swatch {
    border: none;
    border-radius: 0.25rem;
  }

  .color-text {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }

  .color-text:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }
</style>
