<script lang="ts">
  import { login, isLoading, loadingMessage, errorMessage } from '$lib/stores';
  import { base } from '$app/paths';
  import { onMount } from 'svelte';
  import Button from './Button.svelte';  
  
  let email = '';
  let logoError = false;
  let isRegistering = false;
  
  onMount(() => {
    // Check URL parameter for register mode
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('mode') === 'register') {
      isRegistering = true;
    }
  });
  
  // Registration fields
  let firstName = '';
  let lastName = '';
  let confirmPassword = '';
  
  function handleLogoError() {
    logoError = true;
  }
  let password = '';
  
  async function handleLogin() {
    if (!email || !password) {
      errorMessage.set('Please enter both email and password.');
      return;
    }
    
    const success = await login(email, password);
    if (!success) {
      password = ''; // Clear password on failed login
    }
  }
  
  async function handleRegister() {
    if (!email || !password || !firstName || !lastName) {
      errorMessage.set('Please fill in all required fields.');
      return;
    }
    
    if (password !== confirmPassword) {
      errorMessage.set('Passwords do not match.');
      return;
    }
    
    if (password.length < 8) {
      errorMessage.set('Password must be at least 8 characters long.');
      return;
    }
    
    isLoading.set(true);
    loadingMessage.set('Creating account...');
    
    try {
      const { register } = await import('$lib/utils/api');
      
      const response = await register({
        email,
        password,
        firstName,
        lastName
      });
      
      if (response.success) {
        // Registration successful, now login
        loadingMessage.set('Logging in...');
        const loginSuccess = await login(email, password);
        if (!loginSuccess) {
          errorMessage.set('Registration successful! Please try logging in with your new credentials.');
        }
      } else {
        errorMessage.set(response.error || 'Registration failed. Please try again.');
      }
    } catch (error) {
      console.error('Registration error:', error);
      errorMessage.set('Registration failed. Please check your internet connection and try again.');
    } finally {
      isLoading.set(false);
      loadingMessage.set('');
    }
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (isRegistering) {
        handleRegister();
      } else {
        handleLogin();
      }
    }
  }
  
  function toggleMode() {
    isRegistering = !isRegistering;
    errorMessage.set('');
    hasUserInteracted = false;
    // Clear form fields when switching modes
    email = '';
    password = '';
    firstName = '';
    lastName = '';
    confirmPassword = '';
  }
  
  // Clear error message when user starts typing (only if there's an error)
  let hasUserInteracted = false;
  
  function clearErrorOnInput() {
    if (hasUserInteracted && $errorMessage) {
      errorMessage.set('');
    }
    hasUserInteracted = true;
  }
  
</script>

<div class="login-container">
  <div class="login-card" class:register-mode={isRegistering}>
    <div class="login-header">
      {#if !logoError}
        <img 
          src="{base}/Rent-Collection-Toolkit-Logo.svg" 
          alt="Rent Collection Toolkit" 
          class="login-logo"
          on:error={handleLogoError}
        >
      {:else}
        <div class="logo-fallback">
          <h1>Rent Collection Toolkit</h1>
        </div>
      {/if}
      <p>{isRegistering ? 'Create your account to get started' : 'Welcome back! Please sign in to continue'}</p>
    </div>
    
    <!-- Mode Toggle -->
    <div class="mode-toggle">
      <button 
        type="button" 
        class="toggle-btn" 
        class:active={!isRegistering}
        on:click={() => !isRegistering || toggleMode()}
        disabled={$isLoading}
      >
        Sign In
      </button>
      <button 
        type="button" 
        class="toggle-btn" 
        class:active={isRegistering}
        on:click={() => isRegistering || toggleMode()}
        disabled={$isLoading}
      >
        Register
      </button>
    </div>
    
    <form class="login-form" on:submit|preventDefault={isRegistering ? handleRegister : handleLogin}>
      {#if isRegistering}
        <div class="form-row">
          <div class="form-group">
            <label for="firstName">First Name</label>
            <input
              id="firstName"
              type="text"
              bind:value={firstName}
              on:keydown={handleKeydown}
              on:input={clearErrorOnInput}
              disabled={$isLoading}
              placeholder="Enter your first name"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="lastName">Last Name</label>
            <input
              id="lastName"
              type="text"
              bind:value={lastName}
              on:keydown={handleKeydown}
              on:input={clearErrorOnInput}
              disabled={$isLoading}
              placeholder="Enter your last name"
              required
            />
          </div>
        </div>
      {/if}
      
      <div class="form-group">
        <label for="email">Email Address</label>
        <input
          id="email"
          type="email"
          bind:value={email}
          on:keydown={handleKeydown}
          on:input={clearErrorOnInput}
          disabled={$isLoading}
          placeholder="Enter your email"
          required
        />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input
          id="password"
          type="password"
          bind:value={password}
          on:keydown={handleKeydown}
          on:input={clearErrorOnInput}
          disabled={$isLoading}
          placeholder={isRegistering ? "Create a password (min 8 characters)" : "Enter your password"}
          required
        />
        {#if !isRegistering}
          <button type="button" class="forgot-password-link" on:click={() => errorMessage.set('Password reset feature coming soon!')}>
            Forgot your password?
          </button>
        {/if}
      </div>
      
      {#if isRegistering}
        <div class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <input
            id="confirmPassword"
            type="password"
            bind:value={confirmPassword}
            on:keydown={handleKeydown}
            on:input={clearErrorOnInput}
            disabled={$isLoading}
            placeholder="Confirm your password"
            required
          />
        </div>
      {/if}
      
      
      {#if $errorMessage}
        <div class="error-message">
          {$errorMessage}
        </div>
      {/if}
      
      <Button
        type="submit"
        variant="primary"
        size="lg"
        fullWidth={true}
        loading={$isLoading}
        disabled={$isLoading || !email || !password || (isRegistering && (!firstName || !lastName || !confirmPassword))}
        icon={$isLoading ? undefined : (isRegistering ? 'fas fa-user-plus' : 'fas fa-sign-in-alt')}
      >
        {#if $isLoading}
          {$loadingMessage}
        {:else}
          {isRegistering ? 'Create Account' : 'Sign In'}
        {/if}
      </Button>
    </form>
  </div>
  
  <!-- Homepage link -->
  <div class="homepage-link">
    <a href="/" class="back-to-homepage">← Back to homepage</a>
  </div>
</div>

<style>
  .login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: var(--bg);
    z-index: 1000;
    box-sizing: border-box;
    min-height: 100vh;
  }

  .login-logo {
    width: 100%;
    margin-bottom: 30px;
    max-width: 280px;
  }

  .logo-fallback {
    margin-bottom: 30px;
    text-align: center;
  }

  .logo-fallback h1 {
    color: var(--color-primary);
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .login-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.2);
    padding: 3rem;
    width: 100%;
    max-width: 420px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: fadeInUp 0.6s ease-out;
    transition: max-width 0.3s ease;
  }

  .login-card.register-mode {
    max-width: 540px;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .login-header {
    text-align: center;
    margin-bottom: 2.5rem;
  }

  .login-header h1 {
    color: var(--color-primary);
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .login-header p {
    color: #6b7280;
    margin: 0;
    font-size: 1rem;
    font-weight: 400;
  }

  .mode-toggle {
    display: flex;
    background: #f3f4f6;
    border-radius: 12px;
    padding: 4px;
    gap: 2px;
    margin-bottom: 2rem;
  }

  .toggle-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .toggle-btn.active {
    background: #ffffff;
    color: var(--color-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .toggle-btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border);
    border-radius: 8px;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #111827;
    background-color: #ffffff;
    transition: all 0.2s ease;
    box-sizing: border-box;
  }

  .form-group input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .form-group input:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  .form-group input::placeholder {
    color: #9ca3af;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
  }

  .forgot-password-link {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0;
    margin-top: 0.5rem;
    text-align: left;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    text-decoration: underline;
    transition: color 0.2s ease;
  }

  .forgot-password-link:hover {
    color: var(--color-secondary);
  }

  .homepage-link {
    text-align: center;
    margin-top: 2rem;
  }

  .back-to-homepage {
    color: var(--color-primary);
    font-size: 0.875rem;
    text-decoration: none;
    font-family: 'Martian Grotesk', system-ui, -apple-system, sans-serif;
    transition: color 0.2s ease;
  }

  .back-to-homepage:hover {
    color: var(--color-secondary);
    text-decoration: underline;
  }

  .error-message {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #dc2626;
    padding: 1rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    border: 1px solid #f87171;
    animation: shake 0.5s ease-in-out;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }


  /* Responsive Design */
  @media (max-width: 480px) {
    .login-container {
      padding: 1rem;
    }
    
    .login-card {
      padding: 2rem;
    }
    
    .login-header h1 {
      font-size: 1.5rem;
    }
    
    .form-row {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .toggle-btn {
      font-size: 0.8rem;
      padding: 0.6rem 0.8rem;
    }
  }
</style> 