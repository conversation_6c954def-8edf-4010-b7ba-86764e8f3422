<script lang="ts">
  import { browser } from '$app/environment';

  export let isOpen = false;
  export let title = '';
  export let size: 'small' | 'medium' | 'large' | 'xl' = 'large';
  export let showHeader = true;
  export let showCloseButton = true;

  function closeModal() {
    isOpen = false;
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && isOpen) {
      closeModal();
    }
  }

  $: if (browser) {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
  <div class="modal-backdrop" role="dialog" aria-modal="true" tabindex="-1" on:click={handleBackdropClick} on:keydown={handleKeydown}>
    <div class="modal-content size-{size}">
      {#if showHeader}
        <div class="modal-header">
          <h2 class="modal-title">
            <slot name="icon" />
            {title}
          </h2>
          <div class="modal-header-actions">
            <slot name="header-actions" />
            {#if showCloseButton}
              <button class="close-button" on:click={closeModal} aria-label="Close modal">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            {/if}
          </div>
        </div>
      {/if}
      
      <div class="modal-body">
        <slot />
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    backdrop-filter: blur(12px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
    animation: backdropFadeIn 0.3s ease-out;
  }

  @keyframes backdropFadeIn {
    from { 
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to { 
      opacity: 1;
      backdrop-filter: blur(12px);
    }
  }

  .modal-content {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 24px;
    box-shadow: 
      0 32px 64px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
  }

  .modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }

  .size-small {
    max-width: 400px;
  }

  .size-medium {
    max-width: 600px;
  }

  .size-large {
    max-width: 800px;
  }

  .size-xl {
    max-width: 95vw; /* Nearly full screen width */
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: scale(0.85) translateY(40px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--gap);
    background: #ffffff;
    border-bottom: 1px solid var(--border);
  }

  .modal-header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .modal-title {
    margin: 0;
    color: var(--color-primary);
    font-size: var(--section-header-size);
    font-weight: 600;
    letter-spacing: -0.025em;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .close-button {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    font-size: 1rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    transition: all 0.25s ease;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-button:hover {
    background: var(--border);
    color: #374151;
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .close-button:active {
    transform: scale(0.95) rotate(90deg);
  }

  .modal-body {
    padding: var(--gap);
    overflow-y: auto;
    flex: 1;
    background: linear-gradient(180deg, #ffffff, #f8fafc);
    position: relative;
  }

  .modal-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(2, 85, 130, 0.1), transparent);
  }

  /* Scrollbar styling */
  .modal-body::-webkit-scrollbar {
    width: 8px;
  }

  .modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .modal-body::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--color-primary), var(--color-secondary));
    border-radius: 4px;
  }

  .modal-body::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--color-secondary), var(--color-primary));
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .modal-backdrop {
      padding: 1rem;
    }

    .modal-header {
      padding: 2rem 1.5rem 1rem 1.5rem;
    }

    .modal-title {
      font-size: var(--section-header-size);
    }

    .modal-body {
      padding: 1.5rem;
    }

    .close-button {
      width: 40px;
      height: 40px;
      padding: 0.5rem;
    }

    .size-small,
    .size-medium,
    .size-large,
    .size-xl {
      max-width: 100%;
    }
  }

</style>
