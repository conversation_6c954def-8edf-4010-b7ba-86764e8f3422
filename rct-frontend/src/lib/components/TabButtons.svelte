<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { currentTab } from '$lib/stores';
  import Button from './Button.svelte';
  
  export let activeTab: string;
  
  const dispatch = createEventDispatcher();
  
  // Use tag-based color or default to primary color
  $: instanceColor = $currentTab?.tags.includes('inbound') ? 'var(--color-instance-inbound)' : 
                    $currentTab?.tags.includes('outbound') ? 'var(--color-instance-outbound)' : 
                    'var(--color-primary)';
  
  function setActiveTab(tab: string) {
    dispatch('tabChange', tab);
  }
  
  const tabs = [
    { id: 'rent-calculator', label: 'Rent Calculator', icon: 'fas fa-calculator' },
    { id: 'arrangement-planner', label: 'Arrangement Planner', icon: 'fas fa-calendar-alt' },
    { id: 'account-charges', label: 'Account Charges', icon: 'fas fa-money-bill-wave' }
  ];
</script>

<!-- Tab Navigation -->
<div class="tab-navigation">
  <div class="tab-buttons" style="border-bottom-color: {instanceColor}; --instance-color: {instanceColor}">
    {#each tabs as tab}
      <Button
        variant="ghost"
        size="md"
        className="tab-button {activeTab === tab.id ? 'active' : ''}"
        icon={tab.icon}
        on:click={() => setActiveTab(tab.id)}
      >
        {tab.label}
      </Button>
    {/each}
  </div>
</div>

<style lang="less">
  .tab-navigation {
    margin-bottom: 0;
    position: relative;
  }

  .tab-buttons {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 0.5rem 0.5rem 0 0;
    border-bottom: calc(var(--thick-border-width) + 1px) solid var(--color-primary);
    padding: 0;
    margin: 0;
    // padding: 0.5rem;
    // gap: 0.25rem;
    // box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    
    :global(.tab-button) {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background-color: transparent;
      border: none;      
      color: #4b5563;
      font-weight: 500;
      font-size: 0.875rem;
      cursor: pointer;
      transition: none;
      position: relative;
      z-index: 1;
      border: var(--thick-border-width) solid transparent;
      border-radius: 0.375rem 0.375rem 0 0;
      border-bottom: none;
    }

    :global(.tab-button:hover) {
      background-color: #ffffff;
      color: #1f2937;
    }

    :global(.tab-button.active) {
      background-color: #ffffff;
      font-weight: 700;
      color: var(--instance-color);
      border-top: 3px solid var(--instance-color);
      border-left: 3px solid var(--instance-color);
      border-right: 3px solid var(--instance-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(3px);
    }

    :global(.tab-button.active)::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      right: 0;
      height: 5px;
      background-color: #ffffff;
      z-index: 2;
    }
  }


  /* Responsive Design */
  @media (max-width: 768px) {
    .tab-buttons {
      gap: 0.125rem;
      padding: 0.375rem;
    }
  }
</style>