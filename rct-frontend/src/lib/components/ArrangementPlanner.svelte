<script lang="ts">
  import { updateArrangementPlannerData, currentInstanceData, errorMessage, activeTabId } from '$lib/stores';
  import { userPreferences, updateUserPreferences, calendarPreferenceLabels, type CalendarPreference } from '$lib/stores/userPreferences';
  import { onDestroy } from 'svelte';
  import OutputGroup from './OutputGroup.svelte';
  import FormGroup from './FormGroup.svelte';
  import SyncDataSection from './SyncDataSection.svelte';
  import Button from './Button.svelte';
  import DataTable from './DataTable.svelte';
  
  export let isFullView: boolean = false;
  
  // Local notification state
  let notificationMessage = '';
  let notificationType: 'error' | 'success' | 'info' = 'info';
  let showNotification = false;
  
  // Local state for form inputs
  let weeksToNextPay = 0;
  let paymentDue = 0;
  let timeframe = 0;
  // Timeframe is now direct numeric input (no unit conversion needed) - always monthly
  
  // Synced data from Rent Calculator
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;
  $: weeklyRent = rentCalculatorData?.weeklyRent || 0;
  $: currentBalance = rentCalculatorData?.currentBalance || 0;
  $: tenantWeeklyPayment = rentCalculatorData?.tenantWeeklyPayment || 0;
  $: benefitsHbWeeklyPayment = rentCalculatorData?.benefitsHbWeeklyPayment || 0;
  $: reference = rentCalculatorData?.reference || '';
  
  // Subscribe to current instance data for arrangement planner
  $: if ($currentInstanceData) {
    const data = $currentInstanceData.arrangementPlanner;
    weeksToNextPay = data.weeksToNextPay;
    paymentDue = data.paymentDue;
    timeframe = data.timeframe ?? 0;
  }
  
  // Calculation functions
  function calculateMonthlyRent(): number {
    return weeklyRent * 52 / 12;
  }
  
  function calculateMonthlyShortfallSurplus(): number {
    // Get this from rent calculator calculations
    const tenantMonthlyPayment = rentCalculatorData?.tenantMonthlyPayment || 0;
    const apaHbMonthlyPayment = rentCalculatorData?.apaHbMonthlyPayment || 0;
    const tpdMonthlyPayment = rentCalculatorData?.tpdMonthlyPayment || 0;
    
    const totalMonthlyPayments = tenantMonthlyPayment + apaHbMonthlyPayment + tpdMonthlyPayment + 
           (tenantWeeklyPayment * 52 / 12) + (benefitsHbWeeklyPayment * 52 / 12);
    
    return totalMonthlyPayments - calculateMonthlyRent();
  }


  function getShortfallSurplusLabel(value: number): string {
    if (value > 0) return 'Monthly Surplus';
    if (value < 0) return 'Monthly Shortfall';
    return 'Monthly Balance';
  }
  
  // Calculation 14: Months to clear (current profile)
  function calculateMonthsToClear(): number {
    const mss = calculateMonthlyShortfallSurplus();
    const arrearsAfterNext = calculateArrearsAfterNextPayment();
    if (mss <= 0 || arrearsAfterNext <= 0) {
      return 0;
    }
    return Math.ceil(arrearsAfterNext / mss);
  }
  
  // Calculation 16: Arrears after next payment
  function calculateArrearsAfterNextPayment(): number {
    const regularWeeklyPayments = tenantWeeklyPayment + benefitsHbWeeklyPayment;
    return currentBalance + (weeklyRent * weeksToNextPay) - (regularWeeklyPayments * weeksToNextPay) - paymentDue;
  }
  
  // Calculation 17: Additional payment needed (on top of current arrangements)
  function calculateAdditionalPaymentNeeded(): number {
    if (timeframe <= 0) return 0; // Return 0 if no timeframe set
    
    const arrears = calculateArrearsAfterNextPayment();
    const absolutePaymentNeeded = arrears / timeframe;
    
    // Monthly formula: (Arrears after next payment / Timeframe) - Monthly Shortfall/Surplus
    const monthlyShortfallSurplus = calculateMonthlyShortfallSurplus();
    return absolutePaymentNeeded - monthlyShortfallSurplus;
  }
  
  // Helper function to show notifications
  function showUserNotification(message: string, type: 'error' | 'success' | 'info' = 'info') {
    notificationMessage = message;
    notificationType = type;
    showNotification = true;
    
    // Auto-hide notification after 5 seconds
    setTimeout(() => {
      showNotification = false;
    }, 5000);
  }
  
  // Helper function to calculate monthly payment dates
  function getMonthlyPaymentWeeks(startDate: Date, totalWeeks: number): Set<number> {
    const monthlyWeeks = new Set<number>();
    
    // Use proper monthly intervals (4.33 weeks = 52 weeks / 12 months)
    // This ensures payments occur every 4.33 weeks (approximately 30.4 days) consistently
    const monthlyInterval = 52 / 12; // 4.33 weeks
    
    // Start with week 0 (first payment)
    monthlyWeeks.add(0);
    
    // Add subsequent monthly payments
    let currentWeek = monthlyInterval;
    while (currentWeek < totalWeeks) {
      monthlyWeeks.add(Math.round(currentWeek));
      currentWeek += monthlyInterval;
    }
    
    return monthlyWeeks;
  }

  // Calculation 15: Weekly Projection (Starting from current week)
  function generateWeeklyProjection(): Array<{
    date: string;
    dateForAxis: string;
    dateForTooltip: string;
    balance: number;
    isThreshold: boolean;
    week: number;
    hasPayment: boolean;
    payments: Array<{amount: number, description: string}>;
  }> {
    // Ensure we're using current instance data
    if (!$currentInstanceData || !rentCalculatorData) {
      return [];
    }
    
    const projections = [];
    let currentProjectedBalance = currentBalance;
    
    // Separate weekly and monthly payments
    const tenantMonthlyPayment = rentCalculatorData?.tenantMonthlyPayment || 0;
    const apaHbMonthlyPayment = rentCalculatorData?.apaHbMonthlyPayment || 0;
    const tpdMonthlyPayment = rentCalculatorData?.tpdMonthlyPayment || 0;
    
    // Calculate total weekly payments (these continue as normal)
    let totalWeeklyPayments = tenantWeeklyPayment + benefitsHbWeeklyPayment;
    
    // Start from Monday just gone (or today if today is Monday)
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days, otherwise go back to Monday
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - daysToSubtract);
    
    // Generate projections based on view type
    // For table view, always use 3 months; for chart view, use selected chart period
    const periodToUse = showChart ? chartPeriodMonths : tablePeriodMonths;
    const totalWeeks = Math.ceil(periodToUse * 4.33); // Convert months to weeks
    
    // Calculate which weeks have monthly payments
    const monthlyPaymentWeeks = getMonthlyPaymentWeeks(startDate, totalWeeks);
    for (let week = 0; week < totalWeeks; week++) {
      const projectionDate = new Date(startDate);
      projectionDate.setDate(startDate.getDate() + (week * 7));
      
      const payments: Array<{amount: number, description: string}> = [];
      
      // For week 0 (current week), show current balance
      // For future weeks, calculate projected balance
      if (week === 0) {
        // Current week - start with actual current balance
        currentProjectedBalance = currentBalance;
      } else {
        // Future weeks - calculate projection
        currentProjectedBalance = currentProjectedBalance + weeklyRent - totalWeeklyPayments;
      }
      
      // Apply Calculator monthly payments
      const calculatorMonthlyTotal = tenantMonthlyPayment + apaHbMonthlyPayment + tpdMonthlyPayment;
      if (calculatorMonthlyTotal > 0) {
        if (weeksToNextPay === 0) {
          // If expecting payments today, apply on normal schedule
          if (monthlyPaymentWeeks.has(week)) {
            currentProjectedBalance -= calculatorMonthlyTotal;
            if (tenantMonthlyPayment > 0) {
              payments.push({amount: tenantMonthlyPayment, description: 'Tenant (Monthly)'});
            }
            if (apaHbMonthlyPayment > 0) {
              payments.push({amount: apaHbMonthlyPayment, description: 'APA'});
            }
            if (tpdMonthlyPayment > 0) {
              payments.push({amount: tpdMonthlyPayment, description: 'TPD'});
            }
          }
        } else {
          // If delaying payments, apply starting from weeksToNextPay
          const adjustedWeek = week - weeksToNextPay;
          const adjustedStartDate = new Date(startDate);
          adjustedStartDate.setDate(startDate.getDate() + (weeksToNextPay * 7));
          const adjustedMonthlyWeeks = getMonthlyPaymentWeeks(adjustedStartDate, totalWeeks - weeksToNextPay);

          if (adjustedWeek >= 0 && adjustedMonthlyWeeks.has(adjustedWeek)) {
            currentProjectedBalance -= calculatorMonthlyTotal;
            if (tenantMonthlyPayment > 0) {
              payments.push({amount: tenantMonthlyPayment, description: 'Tenant (Monthly)'});
            }
            if (apaHbMonthlyPayment > 0) {
              payments.push({amount: apaHbMonthlyPayment, description: 'APA'});
            }
            if (tpdMonthlyPayment > 0) {
              payments.push({amount: tpdMonthlyPayment, description: 'TPD'});
            }
          }
        }
      }
      
      // Apply Payment Plan amounts
      if (paymentDue > 0) {
        if (weeksToNextPay === 0) {
          // If starting immediately, apply on normal monthly schedule
          if (monthlyPaymentWeeks.has(week)) {
            currentProjectedBalance -= paymentDue;
            payments.push({amount: paymentDue, description: 'Payment Plan'});
          }
        } else {
          // Calculate payment weeks starting from weeksToNextPay
          const adjustedWeek = week - weeksToNextPay;
          const adjustedStartDate = new Date(startDate);
          adjustedStartDate.setDate(startDate.getDate() + (weeksToNextPay * 7));
          const paymentPlanWeeks = getMonthlyPaymentWeeks(adjustedStartDate, totalWeeks - weeksToNextPay);
          
          // Apply payments on the calculated schedule
          if (adjustedWeek >= 0 && paymentPlanWeeks.has(adjustedWeek)) {
            currentProjectedBalance -= paymentDue;
            payments.push({amount: paymentDue, description: 'Payment Plan'});
          }
        }
      }
      
      const hasPayment = payments.length > 0;
      
      // Check if this crosses threshold
      const fourWeeksThreshold = weeklyRent * 4;
      const eightWeeksThreshold = weeklyRent * 8;
      const isThreshold = currentProjectedBalance >= fourWeeksThreshold || currentProjectedBalance >= eightWeeksThreshold;
      
      projections.push({
        date: projectionDate.toLocaleDateString('en-GB', { 
          day: '2-digit', 
          month: '2-digit', 
          year: 'numeric' 
        }),
        dateForAxis: projectionDate.toLocaleDateString('en-GB', { 
          day: '2-digit', 
          month: 'short' 
        }),
        dateForTooltip: projectionDate.toLocaleDateString('en-GB', { 
          day: '2-digit', 
          month: '2-digit', 
          year: '2-digit' 
        }),
        balance: currentProjectedBalance,
        isThreshold,
        week,
        hasPayment,
        payments
      });
    }
    
    return projections;
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function evaluateMathExpression(value: string): number {
    // Remove leading/trailing spaces and commas
    const cleaned = value.trim().replace(/,/g, '');
    
    // If it's just a number, parse it directly
    if (/^\d*\.?\d+$/.test(cleaned)) {
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    // Try to evaluate as a mathematical expression
    try {
      // Only allow safe mathematical expressions (numbers, +, -, *, /, ., spaces, parentheses)
      if (!/^[\d+\-*/().\s]+$/.test(cleaned)) {
        // If it contains invalid characters, try parsing as regular number
        const parsed = parseFloat(cleaned);
        return isNaN(parsed) ? 0 : parsed;
      }
      
      // Use Function constructor to safely evaluate mathematical expressions
      const result = new Function('return ' + cleaned)();
      return isNaN(result) ? 0 : result;
    } catch (error) {
      // If evaluation fails, try parsing as regular number
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
  }
  
  function sanitizeNumericInput(value: string): number {
    return evaluateMathExpression(value);
  }
  
  function handleInputChange() {
    updateArrangementPlannerData({
      weeksToNextPay,
      paymentDue,
      timeframe
    });
  }
  
  function handleFormGroupInput(event: CustomEvent) {
    const { id, value } = event.detail;
    
    switch(id) {
      case 'weeksToNextPay':
        // Only allow whole numbers, remove any decimal input
        const weeksValue = value.toString().replace(/[^\d]/g, '');
        weeksToNextPay = Math.max(0, parseInt(weeksValue) || 0);
        break;
      case 'paymentDue':
        paymentDue = sanitizeNumericInput(value);
        break;
      case 'timeframe':
        // Allow decimal numbers for timeframe
        timeframe = Math.max(0, sanitizeNumericInput(value));
        break;
    }
    
    handleInputChange();
  }

  
  function getBalanceClass(balance: number): string {
    const fourWeeksThreshold = weeklyRent * 4;
    const eightWeeksThreshold = weeklyRent * 8;
    
    if (balance >= eightWeeksThreshold) return 'balance-critical';
    if (balance >= fourWeeksThreshold) return 'balance-warning';
    return balance > 0 ? 'balance-debt' : 'balance-credit';
  }
  
  // Chart view toggle
  let showChart = false;
  
  // Projection periods (in months)
  let tablePeriodMonths = 3; // Fixed at 3 months for table
  let chartPeriodMonths = 3; // Variable for chart (default to 3 months)
  
  // Sync data visibility toggle
  let showSyncData = false;
  
  // Tooltip state
  let tooltipVisible = false;
  let tooltipX = 0;
  let tooltipY = 0;
  let tooltipContent = '';
  let tooltipColor = '#1f2937';
  let hoveredNodeIndex = -1;
  
  // Chart data - made reactive to all input dependencies
  $: chartData = (() => {
    // Explicitly depend on all variables that should trigger updates
    const deps = [
      currentBalance, weeklyRent, tenantWeeklyPayment, benefitsHbWeeklyPayment,
      weeksToNextPay, paymentDue, tablePeriodMonths, chartPeriodMonths, showChart, timeframe,
      rentCalculatorData?.tenantMonthlyPayment,
      rentCalculatorData?.apaHbMonthlyPayment,
      rentCalculatorData?.tpdMonthlyPayment,
      rentCalculatorData?.reference // Add reference to trigger updates when switching tabs
    ];
    
    const projections = generateWeeklyProjection();
    if (!projections || projections.length === 0) {
      return {
        projections: [],
        minValue: 0,
        maxValue: 1000,
        range: 1000
      };
    }
    
    const values = projections.map((p: { balance: number }) => p.balance);
    const minValue = Math.min(...values, 0);
    const maxValue = Math.max(...values, weeklyRent * 8);
    const range = Math.max(maxValue - minValue, 100); // Ensure minimum range
    const padding = range * 0.1;
    
    return {
      projections,
      minValue: minValue - padding,
      maxValue: maxValue + padding,
      range: range + (padding * 2)
    };
  })();
  
  function getChartY(value: number, chartData: any, chartHeight: number): number {
    if (!chartData || chartData.range === 0) return chartHeight / 2;
    return chartHeight - ((value - chartData.minValue) / chartData.range) * chartHeight;
  }
  
  function getThresholdY(threshold: number, chartData: any, chartHeight: number): number {
    return getChartY(threshold, chartData, chartHeight);
  }

  // Tooltip functions
  function showTooltip(event: MouseEvent, projection: any, nodeIndex: number) {
    console.log('Tooltip show triggered', projection);
    hoveredNodeIndex = nodeIndex;
    
    // Use mouse position for simpler, closer positioning
    const mouseX = event.clientX;
    const mouseY = event.clientY;
    
    // Estimate tooltip width (approximate based on content)
    const estimatedTooltipWidth = 320; // Conservative estimate matching max-width
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    
    // Position tooltip to the right by default, but switch to left if it would go off screen
    // Use a more aggressive threshold to switch earlier (when past 60% of screen width)
    if (mouseX > windowWidth * 0.6) {
      // Position to the left of the cursor, very close
      tooltipX = mouseX - estimatedTooltipWidth - 5;
    } else {
      // Position to the right of the cursor, very close
      tooltipX = mouseX + 5;
    }
    
    // Position tooltip above the cursor, very close
    if (mouseY - 35 < 0) {
      // Position below the cursor if too close to top
      tooltipY = mouseY + 10;
    } else {
      // Position above the cursor
      tooltipY = mouseY - 25;
    }
    
    const isCurrentWeek = projection.week === 0;
    const hasPayment = projection.hasPayment;
    
    // Determine tooltip color based on balance thresholds (same logic as circle color)
    if (projection.balance >= weeklyRent * 8) {
      tooltipColor = 'var(--color-red)'; // Red - 8+ weeks arrears
    } else if (projection.balance >= weeklyRent * 4) {
      tooltipColor = 'var(--color-yellow)'; // Yellow/Orange - 4+ weeks arrears
    } else if (projection.balance > 0) {
      tooltipColor = 'var(--color-secondary)'; // Blue - In debt but under 4 weeks
    } else {
      tooltipColor = 'var(--color-green)'; // Green - In credit
    }
    
    // Build payment details for tooltip
    let paymentDetails = '';
    if (hasPayment && projection.payments.length > 0) {
      const paymentSummary = projection.payments.map(p => `${p.description}: ${formatCurrency(p.amount)}`).join(', ');
      paymentDetails = ` - Payments: ${paymentSummary}`;
    }
    
    tooltipContent = `${projection.dateForTooltip}${isCurrentWeek ? ' (Current Week)' : ''}${paymentDetails}\nBalance: ${formatCurrency(projection.balance)}`;
    tooltipVisible = true;
    console.log('Tooltip content:', tooltipContent, 'Position:', tooltipX, tooltipY, 'Visible:', tooltipVisible, 'Color:', tooltipColor);
  }

  function hideTooltip() {
    console.log('Tooltip hide triggered');
    tooltipVisible = false;
    hoveredNodeIndex = -1;
  }

  // Outlook calendar export functions
  function createOutlookEvent(projection: any, actionType: string = 'Follow-up') {
    // Get current tab data directly to avoid stale state
    const currentData = $currentInstanceData;
    const currentRentData = currentData?.rentCalculator;
    
    if (!currentData || !currentRentData) {
      showUserNotification('Unable to create calendar event - no case data available', 'error');
      return;
    }
    
    const tenantRef = currentRentData.reference || 'Unknown';
    const balance = projection.balance;
    const currentWeeklyRent = currentRentData.weeklyRent || 0;
    
    // Data validation - ensure we have the correct data for this tab
    console.log('Calendar Event Data Check:', {
      tenantRef,
      balance,
      currentWeeklyRent,
      projectionDate: projection.date,
      activeTabId: $activeTabId || 'none'
    });
    
    const projectionDate = new Date(projection.date.split('/').reverse().join('-')); // Convert DD/MM/YYYY to YYYY-MM-DD
    
    // Set the event time to 9:00 AM
    const startTime = new Date(projectionDate);
    startTime.setHours(9, 0, 0, 0);
    
    // Set duration to 30 minutes
    const endTime = new Date(startTime);
    endTime.setMinutes(startTime.getMinutes() + 30);
    
    // Determine action based on balance thresholds using current data
    let suggestedAction = '';
    let priority = 'Normal';
    
    if (balance >= currentWeeklyRent * 8) {
      suggestedAction = 'Court proceedings consideration - 8+ weeks arrears';
      priority = 'High';
    } else if (balance >= currentWeeklyRent * 4) {
      suggestedAction = 'NSP/NOSP procedures - 4+ weeks arrears';
      priority = 'High';
    } else if (balance > 0) {
      suggestedAction = 'Contact tenant regarding arrears';
      priority = 'Normal';
    } else {
      suggestedAction = 'Monitor account status';
      priority = 'Low';
    }
    
    const subject = `${actionType}: ${tenantRef} - £${balance.toFixed(2)} projected balance`;
    const body = `Rent Collection Action Required\n\n` +
                `Tenant Reference: ${tenantRef}\n` +
                `Projected Date: ${projection.date}\n` +
                `Projected Balance: £${balance.toFixed(2)}\n` +
                `Weekly Rent: £${currentWeeklyRent.toFixed(2)}\n\n` +
                `Suggested Action: ${suggestedAction}\n\n` +
                `Generated: ${new Date().toLocaleString()}\n` +
                `Tab ID: ${$activeTabId || 'unknown'}\n` +
                `Generated from Rent Collection Toolkit`;
    
    // Create different options for the user to choose from
    const options: Array<{ name: string; url: string; download?: string }> = [];
    
    // Option 1: Modern Outlook (ms-outlook protocol)
    const modernOutlookUrl = `ms-outlook://calendar/compose?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}&startdt=${startTime.toISOString()}&enddt=${endTime.toISOString()}`;
    options.push({ name: 'Modern Outlook App', url: modernOutlookUrl });
    
    // Option 2: Classic Outlook (outlook protocol) 
    const classicOutlookUrl = `outlook://calendar/compose?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}&startdt=${startTime.toISOString()}&enddt=${endTime.toISOString()}`;
    options.push({ name: 'Classic Outlook App', url: classicOutlookUrl });
    
    // Option 3: Web Outlook
    const webOutlookUrl = new URL('https://outlook.live.com/calendar/0/deeplink/compose');
    webOutlookUrl.searchParams.set('subject', subject);
    webOutlookUrl.searchParams.set('body', body);
    webOutlookUrl.searchParams.set('startdt', startTime.toISOString());
    webOutlookUrl.searchParams.set('enddt', endTime.toISOString());
    options.push({ name: 'Web Outlook', url: webOutlookUrl.toString() });
    
    // Option 4: ICS file download
    const icsContent = createICSFile(subject, body, startTime, endTime);
    const icsBlob = new Blob([icsContent], { type: 'text/calendar' });
    const icsUrl = URL.createObjectURL(icsBlob);
    options.push({ name: 'Download .ics file', url: icsUrl, download: `${tenantRef}-${projection.date.replace(/\//g, '-')}.ics` });
    
    // Get user's preferred calendar option
    const userPreferredOption = $userPreferences.calendarPreference;
    
    // If user preference is "ask every time", show menu immediately
    if (userPreferredOption === 'ask-every-time') {
      showOutlookMenu(options, tenantRef, projection.date, 0);
      return;
    }
    
    // Find the preferred option in the list or default to first
    let preferredIndex = 0;
    if (userPreferredOption) {
      const preferredOptionIndex = options.findIndex(opt => {
        switch (userPreferredOption) {
          case 'modern-outlook': return opt.name === 'Modern Outlook App';
          case 'classic-outlook': return opt.name === 'Classic Outlook App';
          case 'web-outlook': return opt.name === 'Web Outlook';
          case 'ics-download': return opt.name === 'Download .ics file';
          default: return false;
        }
      });
      if (preferredOptionIndex !== -1) {
        preferredIndex = preferredOptionIndex;
      }
    }
    
    // Try the preferred option automatically with enhanced error handling
    const tryPreferredOption = () => {
      const preferredOption = options[preferredIndex];
      
      // For URL-based options (Outlook apps and web)
      if (!preferredOption.download) {
        try {
          const newWindow = window.open(preferredOption.url, '_blank');
          
          // Check if popup was blocked
          if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            throw new Error('Popup blocked or failed to open');
          }
          
          // For protocol handlers (outlook:), check if it worked after a delay
          if (preferredOption.url.startsWith('outlook:') || preferredOption.url.startsWith('outlookweb:')) {
            setTimeout(() => {
              // If the window is still open after 1 second, the protocol handler likely failed
              if (newWindow && !newWindow.closed) {
                newWindow.close();
                console.warn('Calendar protocol handler may have failed, showing menu');
                showUserNotification('Calendar app may not be installed. Please select another option.', 'error');
                showOutlookMenu(options, tenantRef, projection.date, preferredIndex);
              }
            }, 1000);
          }
        } catch (error) {
          console.warn('Failed to open calendar URL, showing menu:', error);
          showUserNotification('Failed to open calendar. Please select an option from the menu.', 'error');
          showOutlookMenu(options, tenantRef, projection.date, preferredIndex);
        }
      } else {
        // For ICS file download
        const link = document.createElement('a');
        link.href = preferredOption.url;
        link.download = preferredOption.download;
        link.style.display = 'none';
        document.body.appendChild(link);
        
        try {
          link.click();
          
          // Clean up after a short delay to ensure download starts
          setTimeout(() => {
            document.body.removeChild(link);
            if (preferredOption.url.startsWith('blob:')) {
              URL.revokeObjectURL(preferredOption.url);
            }
          }, 100);
        } catch (error) {
          document.body.removeChild(link);
          console.warn('ICS download failed, showing menu:', error);
          showUserNotification('Failed to download calendar file. Please try another option.', 'error');
          showOutlookMenu(options, tenantRef, projection.date, preferredIndex);
        }
      }
    };
    
    tryPreferredOption();
  }

  // Helper function to create ICS file content
  function createICSFile(subject: string, body: string, startTime: Date, endTime: Date): string {
    const formatDate = (date: Date): string => {
      return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    };
    
    return [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Rent Collection Toolkit//Calendar Event//EN',
      'BEGIN:VEVENT',
      `UID:${Date.now()}@rentcollection.local`,
      `DTSTART:${formatDate(startTime)}`,
      `DTEND:${formatDate(endTime)}`,
      `SUMMARY:${subject}`,
      `DESCRIPTION:${body.replace(/\n/g, '\\n')}`,
      'STATUS:TENTATIVE',
      'BEGIN:VALARM',
      'TRIGGER:-PT15M',
      'ACTION:DISPLAY',
      `DESCRIPTION:Reminder: ${subject}`,
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');
  }
  
  // Helper function to show Outlook options menu
  function showOutlookMenu(options: Array<{ name: string; url: string; download?: string }>, tenantRef: string, date: string, preferredIndex: number = 0) {
    const currentPreference = $userPreferences.calendarPreference;
    const isAskEveryTime = currentPreference === 'ask-every-time';
    
    const menuHtml = `
      <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                  background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                  z-index: 10000; border: 1px solid var(--border); min-width: 320px;">
        <h3 style="margin: 0 0 1rem 0; color: #374151;">Add to Calendar</h3>
        <p style="margin: 0 0 1.5rem 0; color: #6b7280; font-size: 0.875rem;">Choose how to add this event to your calendar:</p>
        <div style="display: flex; flex-direction: column; gap: 0.75rem;">
          ${options.map((option, index) => `
            <button onclick="handleOutlookOption(${index})" 
                    style="padding: 0.75rem 1rem; border: 1px solid ${!isAskEveryTime && index === preferredIndex ? 'var(--color-primary)' : '#d1d5db'}; border-radius: 6px; 
                           background: ${!isAskEveryTime && index === preferredIndex ? 'rgba(2, 85, 130, 0.05)' : 'white'}; cursor: pointer; text-align: left; transition: background 0.2s;
                           font-size: 0.875rem; position: relative;"
                    onmouseover="this.style.background='#f9fafb'" 
                    onmouseout="this.style.background='${!isAskEveryTime && index === preferredIndex ? 'rgba(2, 85, 130, 0.05)' : 'white'}'">
              ${option.name}${!isAskEveryTime && index === preferredIndex ? ' <span style="color: var(--color-primary); font-size: 0.75rem; font-weight: 600;">(Current Default)</span>' : ''}
            </button>
          `).join('')}
        </div>
        <div style="margin: 1.5rem 0 1rem 0; padding: 1rem; background: #f8fafc; border-radius: 6px; border: 1px solid #e2e8f0;">
          <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; font-size: 0.875rem; color: #374151;">
            <input type="checkbox" id="setAsDefault" style="margin: 0;">
            <span>Use this selection as my default calendar preference</span>
          </label>
          ${isAskEveryTime ? '<p style="margin: 0.5rem 0 0 0; font-size: 0.75rem; color: #6b7280; font-style: italic;">Currently set to ask every time</p>' : ''}
        </div>
        <div style="display: flex; gap: 0.75rem;">
          <button onclick="closeOutlookMenu()" 
                  style="flex: 1; padding: 0.5rem 1rem; background: #f3f4f6; border: 1px solid #d1d5db; 
                         border-radius: 4px; cursor: pointer; font-size: 0.875rem;">
            Cancel
          </button>
        </div>
      </div>
      <div onclick="closeOutlookMenu()" 
           style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                  background: rgba(0,0,0,0.5); z-index: 9999;"></div>
    `;
    
    const menuDiv = document.createElement('div');
    menuDiv.id = 'outlook-menu';
    menuDiv.innerHTML = menuHtml;
    document.body.appendChild(menuDiv);
    
    // Store options globally for the click handlers
    window.outlookOptions = options;
    window.currentPreferredIndex = preferredIndex;
  }

  function getActionTypeForBalance(balance: number): string {
    if (balance >= weeklyRent * 8) {
      return 'Legal Action Review';
    } else if (balance >= weeklyRent * 4) {
      return 'Escalation Action';
    } else if (balance > 0) {
      return 'Tenant Contact';
    } else {
      return 'Account Review';
    }
  }


  // DataTable columns and rows for payment schedule table
  $: paymentScheduleColumns = [
    { key: 'label', label: 'Payment Type', type: 'label' as const, align: 'left' as const, width: '1fr' },
    { key: 'amount', label: 'Amount', type: 'currency' as const, align: 'right' as const, width: '0.8fr' },
    { key: 'frequency', label: 'Frequency', type: 'text' as const, align: 'left' as const, width: '0.8fr' }
  ];

  $: paymentScheduleRows = [
    // Weekly payments (continue as normal)
    ...(tenantWeeklyPayment > 0 ? [{
      label: 'Tenant (Weekly)',
      amount: tenantWeeklyPayment,
      frequency: 'Weekly'
    }] : []),
    ...(benefitsHbWeeklyPayment > 0 ? [{
      label: 'Benefits HB (Weekly)',
      amount: benefitsHbWeeklyPayment,
      frequency: 'Weekly'
    }] : []),
    // Monthly payments from Calculator - timing controlled by weeksToNextPay
    ...((rentCalculatorData?.tenantMonthlyPayment || 0) > 0 ? [{
      label: 'Tenant (Monthly)',
      amount: rentCalculatorData?.tenantMonthlyPayment || 0,
      frequency: weeksToNextPay === 0 ? 'Monthly' : `Monthly (starts week ${weeksToNextPay})`
    }] : []),
    ...((rentCalculatorData?.apaHbMonthlyPayment || 0) > 0 ? [{
      label: 'APA',
      amount: rentCalculatorData?.apaHbMonthlyPayment || 0,
      frequency: weeksToNextPay === 0 ? 'Monthly' : `Monthly (starts week ${weeksToNextPay})`
    }] : []),
    ...((rentCalculatorData?.tpdMonthlyPayment || 0) > 0 ? [{
      label: 'TPD',
      amount: rentCalculatorData?.tpdMonthlyPayment || 0,
      frequency: weeksToNextPay === 0 ? 'Monthly' : `Monthly (starts week ${weeksToNextPay})`
    }] : []),
    // Payment Planning amount when starting immediately (weeksToNextPay === 0)
    ...(paymentDue > 0 && weeksToNextPay === 0 ? [{
      label: 'Payment Plan (Monthly)',
      amount: paymentDue,
      frequency: 'Monthly'
    }] : [])
  ];

  // DataTable columns and rows for projection table
  $: projectionColumns = [
    { key: 'date', label: 'Date (Monday)', type: 'custom' as const, align: 'left' as const, width: '1fr' },
    { key: 'balance', label: 'Projected Balance', type: 'currency' as const, align: 'right' as const, width: '188px' },
    { key: 'status', label: 'Status', type: 'text' as const, align: 'left' as const, width: '167px' },
    { key: 'actions', label: 'Cal', type: 'custom' as const, align: 'center' as const, width: '44px' }
  ];

  $: projectionRows = (() => {
    // Explicitly depend on all variables that should trigger updates
    const deps = [
      currentBalance, weeklyRent, tenantWeeklyPayment, benefitsHbWeeklyPayment,
      weeksToNextPay, paymentDue, tablePeriodMonths, chartPeriodMonths, showChart, timeframe,
      rentCalculatorData?.tenantMonthlyPayment,
      rentCalculatorData?.apaHbMonthlyPayment,
      rentCalculatorData?.tpdMonthlyPayment,
      rentCalculatorData?.reference // Add reference to trigger updates when switching tabs
    ];
    
    return generateWeeklyProjection().map(projection => {
    // Status string logic
    let status = '';
    if (projection.balance >= weeklyRent * 8) {
      status = '8+ weeks arrears';
    } else if (projection.balance >= weeklyRent * 4) {
      status = '4+ weeks arrears';
    } else if (projection.balance > 0) {
      status = 'In debt';
    } else if (projection.balance < 0) {
      status = 'In credit';
    } else {
      status = 'Balanced';
    }

    // Indicators for date cell
    let dateHtml = `${projection.date}`;
    if (projection.week === 0) {
      dateHtml += ' <span class="week-indicator current">Current Week</span>';
    }
    if (projection.hasPayment && projection.payments.length > 0) {
      // Show each payment with its amount
      projection.payments.forEach(payment => {
        dateHtml += ` <span class="payment-indicator"><i class="fas fa-money-bill-wave"></i> ${formatCurrency(payment.amount)}</span>`;
      });
    }

    // Row class
    const rowClass = [
      getBalanceClass(projection.balance),
      projection.week === 0 ? 'current-week' : '',
      projection.hasPayment ? 'has-payment' : ''
    ].filter(Boolean).join(' ');

    // Action button HTML with click handler
    const actionType = getActionTypeForBalance(projection.balance);
    const actionsHtml = projection.week > 0 && projection.balance > 0 ? // Don't show for current week or credit balances
      `<button class="outlook-btn" onclick="window.createOutlookEvent_${projection.week}()" title="Add to Outlook Calendar">
        <i class="fas fa-calendar-plus"></i>
      </button>` : '';

    return {
      date_html: dateHtml,
      balance: projection.balance,
      status,
      actions_html: actionsHtml,
      _rowClass: rowClass,
      _projection: projection // Store projection data for the click handler
    };
  });
  })();

  // Set up global functions for calendar export (needed for HTML onclick handlers)
  $: if (typeof window !== 'undefined') {
    // Clean up previous functions first to prevent stale data
    const projections = generateWeeklyProjection();
    
    // Remove old functions for this reference to prevent cross-tab contamination
    for (let week = 0; week < 52; week++) {
      delete window[`createOutlookEvent_${week}`];
    }
    
    // Set up new functions for current projections
    projections.forEach(projection => {
      if (projection.week > 0 && projection.balance > 0) {
        window[`createOutlookEvent_${projection.week}`] = () => {
          const actionType = getActionTypeForBalance(projection.balance);
          createOutlookEvent(projection, actionType);
        };
      }
    });
    
    // Global functions for the outlook menu (only set once, don't duplicate)
    if (!window.handleOutlookOption) {
      window.handleOutlookOption = (index: number) => {
        const option = window.outlookOptions[index];
      
      // Check if user wants to set as default
      const setAsDefaultCheckbox = document.getElementById('setAsDefault') as HTMLInputElement;
      if (setAsDefaultCheckbox && setAsDefaultCheckbox.checked) {
        // Map option name to preference value
        let preferenceValue: CalendarPreference = 'modern-outlook'; // Explicitly type as CalendarPreference
        switch (option.name) {
          case 'Modern Outlook App':
            preferenceValue = 'modern-outlook';
            break;
          case 'Classic Outlook App':
            preferenceValue = 'classic-outlook';
            break;
          case 'Web Outlook':
            preferenceValue = 'web-outlook';
            break;
          case 'Download .ics file':
            preferenceValue = 'ics-download';
            break;
          default:
            // Handle unexpected option names gracefully, maybe log an error or set a default
            console.error(`Unknown calendar option: ${option.name}`);
            preferenceValue = 'ask-every-time'; // Fallback to a safe default
            break;
        }
        
        // Update user preferences
        updateUserPreferences({ calendarPreference: preferenceValue });
      }
      
      if (option.download) {
        // Download ICS file
        const link = document.createElement('a');
        link.href = option.url;
        link.download = option.download;
        link.click();
      } else {
        // Try to open URL
        const link = document.createElement('a');
        link.href = option.url;
        link.target = '_blank';
        link.click();
      }
      window.closeOutlookMenu(); // Call with window prefix
      };
      
      window.closeOutlookMenu = () => {
        const menu = document.getElementById('outlook-menu');
        if (menu) {
          menu.remove();
        }
        window.outlookOptions = []; // Set to empty array to clear
      };
    }
  }

  // Cleanup global functions when component is destroyed to prevent memory leaks
  onDestroy(() => {
    if (typeof window !== 'undefined') {
      // Clean up all calendar event functions
      for (let week = 0; week < 52; week++) {
        delete (window as any)[`createOutlookEvent_${week}`];
      }
      // Clean up menu functions
      delete (window as any).handleOutlookOption;
      delete (window as any).closeOutlookMenu;
      delete (window as any).outlookOptions;
    }
  });
</script>

<div class="arrangement-planner">
  <!-- Notification -->
  {#if showNotification}
    <div class="notification notification-{notificationType}" class:notification-visible={showNotification}>
      <i class="fas fa-{notificationType === 'error' ? 'exclamation-circle' : notificationType === 'success' ? 'check-circle' : 'info-circle'}"></i>
      <span>{notificationMessage}</span>
      <button class="notification-close" on:click={() => showNotification = false}>
        <i class="fas fa-times"></i>
      </button>
    </div>
  {/if}
  <div class="planner-grid">
    <!-- LEFT COLUMN -->
    <div class="planner-left">
      <!-- Left side: Input Fields and Calculations -->
      <div class="planner-left-column">
        <!-- Payment Planning Grid -->
        <div class="input-section">
          <div class="section-header">
            <h4>Payment Planning</h4>
          </div>
          <div class="planning-grid">
            <!-- Top Row -->
            <FormGroup
              id="weeksToNextPay"
              label="Weeks to next payment"
              value={weeksToNextPay}
              placeholder="0.00"
              type="number"
              step="1"
              on:input={handleFormGroupInput}
            />
            <FormGroup
              id="paymentDue"
              label="Payment amount"
              value={paymentDue}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
            
            <!-- Middle Row -->
            <OutputGroup 
              label="Arrears after next payment"
              value={formatCurrency(calculateArrearsAfterNextPayment())}
              showCurrency={true}
            />
            <FormGroup
              id="timeframe"
              label="Timeframe to clear debt"
              value={timeframe}
              placeholder="0.00"
              type="number"
              step="0.1"
              on:input={handleFormGroupInput}
            />
            
            <!-- Bottom Row -->
            <OutputGroup 
              label="Months to clear debt"
              value="{calculateMonthsToClear()}"
            />
            <OutputGroup 
              label="Additional payment reqd."
              value={formatCurrency(calculateAdditionalPaymentNeeded())}
              showCurrency={true}
            />
          </div>
        </div>
      </div>
      <!-- Right side: Payment Schedule -->
      <div class="planner-right-column">
        
        <div class="payment-schedule-section">
          <h5>Payment Schedule</h5>
          {#if paymentScheduleRows.length > 0}
            <DataTable
              columns={paymentScheduleColumns}
              rows={paymentScheduleRows}
              showHeader={true}
              hoverEffect={false}
              className="payment-schedule-table"
            />
          {:else if paymentDue === 0}
            <div class="schedule-placeholder">
              <i class="fas fa-calendar-plus"></i>
              <p>Enter payment details above to generate a payment schedule</p>
              <small>Set "Payment Due" and "Weeks to Next Pay" to see projected payment dates</small>
            </div>
          {/if}
          {#if paymentDue > 0}
            <p><strong>Payment Plan:</strong> 
              {#if weeksToNextPay === 0}
                {formatCurrency(paymentDue)} monthly (starting now)
              {:else}
                {formatCurrency(paymentDue)} monthly starting in {weeksToNextPay} week{weeksToNextPay > 1 ? 's' : ''}
              {/if}
            </p>
          {/if}
          {#if weeksToNextPay > 0 && ((rentCalculatorData?.tenantMonthlyPayment || 0) > 0 || (rentCalculatorData?.apaHbMonthlyPayment || 0) > 0 || (rentCalculatorData?.tpdMonthlyPayment || 0) > 0)}
            <p><small><strong>Note:</strong> Calculator monthly payments will also start in {weeksToNextPay} week{weeksToNextPay > 1 ? 's' : ''}.</small></p>
          {/if}
        </div>
      </div>
    </div>
    <!-- RIGHT COLUMN -->
    <div class="planner-right">
      <div class="projection-section">
        <div class="projection-header">
          <h4>Weekly Projection</h4>
          <div class="projection-controls">
            {#if showChart}
              <div class="period-control">
                <label for="chart-period">Period:</label>
                <select 
                  id="chart-period" 
                  bind:value={chartPeriodMonths}
                  class="period-select"
                >
                  <option value={3}>3 months</option>
                  <option value={4}>4 months</option>
                  <option value={5}>5 months</option>
                  <option value={6}>6 months</option>
                  <option value={7}>7 months</option>
                  <option value={8}>8 months</option>
                  <option value={9}>9 months</option>
                  <option value={10}>10 months</option>
                  <option value={11}>11 months</option>
                  <option value={12}>1 year</option>
                </select>
              </div>
            {:else}
              <div class="period-info">
                <span class="period-label">3 months (fixed)</span>
              </div>
            {/if}
            <div class="view-toggle">
              <button 
                class="toggle-btn" 
                class:active={!showChart}
                on:click={() => showChart = false}
              >
                Table
              </button>
              <button 
                class="toggle-btn" 
                class:active={showChart}
                on:click={() => showChart = true}
              >
                Chart
              </button>
            </div>
          </div>
        </div>
        {#if showChart}
          <!-- Chart View -->
          <div class="chart-container">
            {#if weeklyRent > 0}
              {@const chartWidth = 600}
              {@const chartHeight = 300}
              {@const padding = 30}
              {#if chartData.projections.length > 0}
                {@const pathData = chartData.projections.length > 1 ? chartData.projections.map((p: { balance: number }, i: number) => {
                  const x = padding + (i / Math.max(chartData.projections.length - 1, 1)) * chartWidth;
                  const y = getChartY(p.balance, chartData, chartHeight) + padding;
                  return i === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
                }).join(' ') : ''}
                <svg width="100%" height="{chartHeight + padding * 2}" viewBox="0 0 {chartWidth + padding * 2} {chartHeight + padding * 2}">
                  <!-- Background -->
                  <rect x="{padding}" y="{padding}" width="{chartWidth}" height="{chartHeight}" fill="#f8fafc" stroke="var(--border)" stroke-width="1"/>
                  <!-- Threshold lines -->
                  <!-- 8 weeks threshold -->
                  {#if weeklyRent * 8 >= chartData.minValue && weeklyRent * 8 <= chartData.maxValue}
                    {@const y8 = getThresholdY(weeklyRent * 8, chartData, chartHeight) + padding}
                    <line x1="{padding}" y1="{y8}" x2="{chartWidth + padding}" y2="{y8}" stroke="var(--color-red)" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="{padding + 5}" y="{y8 - 5}" font-size="12" fill="var(--color-red)" font-weight="600">8 weeks arrears</text>
                  {/if}
                  <!-- 4 weeks threshold -->
                  {#if weeklyRent * 4 >= chartData.minValue && weeklyRent * 4 <= chartData.maxValue}
                    {@const y4 = getThresholdY(weeklyRent * 4, chartData, chartHeight) + padding}
                    <line x1="{padding}" y1="{y4}" x2="{chartWidth + padding}" y2="{y4}" stroke="var(--color-yellow)" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="{padding + 5}" y="{y4 - 5}" font-size="12" fill="var(--color-yellow)" font-weight="600">4 weeks arrears</text>
                  {/if}
                  <!-- Zero line -->
                  {#if 0 >= chartData.minValue && 0 <= chartData.maxValue}
                    {@const y0 = getThresholdY(0, chartData, chartHeight) + padding}
                    <line x1="{padding}" y1="{y0}" x2="{chartWidth + padding}" y2="{y0}" stroke="var(--color-secondary)" stroke-width="1" stroke-dasharray="3,3"/>
                    <text x="{padding + 5}" y="{y0 - 5}" font-size="12" fill="#6b7280">Balance</text>
                  {/if}
                  <!-- Data line -->
                  <path d="{pathData}" fill="none" stroke="var(--color-primary)" stroke-width="3" stroke-linejoin="round" stroke-linecap="round"/>
                  <!-- Data points -->
                  {#each chartData.projections as projection, i}
                    {@const x = padding + (i / Math.max(chartData.projections.length - 1, 1)) * chartWidth}
                    {@const y = getChartY(projection.balance, chartData, chartHeight) + padding}
                    {@const pointColor = projection.balance >= weeklyRent * 8 ? 'var(--color-red)' : projection.balance >= weeklyRent * 4 ? 'var(--color-yellow)' : projection.balance > 0 ? 'var(--color-secondary)' : 'var(--color-green)'}
                    {@const isCurrentWeek = projection.week === 0}
                    {@const hasPayment = projection.hasPayment}
                    <!-- Current week indicator -->
                    {#if isCurrentWeek}
                      <circle cx="{x}" cy="{y}" r="8" fill="none" stroke="#8b5cf6" stroke-width="3" stroke-dasharray="2,2"/>
                    {/if}
                    <!-- Payment indicator -->
                    {#if hasPayment && projection.payments.length > 0}
                      <circle cx="{x}" cy="{y}" r="10" fill="none" stroke="#059669" stroke-width="2"/>
                      {#if projection.payments.length === 1}
                        <!-- Single payment - show amount -->
                        <text x="{x}" y="{y - 15}" font-size="9" fill="#059669" text-anchor="middle" font-weight="bold">{formatCurrency(projection.payments[0].amount)}</text>
                      {:else}
                        <!-- Multiple payments - show count -->
                        <text x="{x}" y="{y - 15}" font-size="10" fill="#059669" text-anchor="middle" font-weight="bold">{projection.payments.length}×</text>
                      {/if}
                    {/if}
                    <!-- Shadow circle for hover effect -->
                    {#if hoveredNodeIndex === i}
                      <circle cx="{x + 1}" cy="{y + 1}" r="{isCurrentWeek ? 8 : 7}" fill="rgba(0, 0, 0, 0.2)" opacity="0.6"/>
                    {/if}
                    <circle cx="{x}" cy="{y}" r="{hoveredNodeIndex === i ? (isCurrentWeek ? 7 : 6) : (isCurrentWeek ? 6 : 5)}" fill="{pointColor}" stroke="white" stroke-width="2" 
                            class="chart-data-point"/>
                    <!-- Tooltip on hover -->
                    <circle 
                      cx="{x}" 
                      cy="{y}" 
                      r="15" 
                      fill="transparent" 
                      stroke="transparent" 
                      class="chart-tooltip"
                      role="button"
                      tabindex="0"
                      aria-label="Show balance details for {projection.dateForTooltip}"
                      on:mouseenter={(e) => showTooltip(e, projection, i)}
                      on:mouseleave={hideTooltip}
                    />
                  {/each}
                  <!-- Y-axis labels -->
                  {#each Array(5 + 1) as _, i}
                    {@const value = chartData.minValue + (chartData.range / 5) * i}
                    {@const y = chartHeight + padding - (i / 5) * chartHeight}
                    <text x="{padding - 15}" y="{y + 4}" font-size="11" fill="#6b7280" text-anchor="end">{formatCurrency(value)}</text>
                  {/each}
                  <!-- X-axis labels -->
                  {#each chartData.projections as projection, i}
                    {@const shouldShowLabel = chartPeriodMonths >= 7 ? 
                      (i % 4 === 0) : // Show every 4 weeks for 7+ month periods
                      (i % 2 === 0)} <!-- Show every 2 weeks for shorter periods -->
                    {#if shouldShowLabel}
                      {@const x = padding + (i / Math.max(chartData.projections.length - 1, 1)) * chartWidth}
                      <text x="{x}" y="{chartHeight + padding + 20}" font-size="11" fill="#6b7280" text-anchor="middle">{projection.dateForAxis}</text>
                    {/if}
                  {/each}
                </svg>
              {/if}
            {:else}
              <div class="chart-placeholder">
                <p>Enter weekly rent to generate projection chart</p>
              </div>
            {/if}
          </div>
          <!-- Custom Tooltip -->
          {#if tooltipVisible}
            <div 
              class="custom-tooltip" 
              style="left: {tooltipX + 10}px; top: {tooltipY - 40}px; background-color: {tooltipColor};"
            >
              {#each tooltipContent.split('\n') as line}
                <div>{line}</div>
              {/each}
            </div>
          {/if}
        {:else}
          <!-- Table View -->
          <div class="projection-table-container">
            <DataTable
              columns={projectionColumns}
              rows={projectionRows}
              showHeader={true}
              hoverEffect={true}
              className="projection-table"
            />
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  .arrangement-planner {
    padding: calc(var(--gap) * 2);
    height: 100%;
    overflow-y: auto;
    
  }

  .planner-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--gap);
    align-items: flex-start;
  }

  .planner-left {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap);
  }

  .planner-right {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .planner-left-column {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .planner-right-column {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .planner-content {
    display: none;
  }

  /* Sync Section */
  .sync-section {
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .sync-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .sync-header h4 {
    margin: 0;
    color: #111827;
  }

  /* Input Section */
  .input-section {
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--gap);
  }

  .section-header h4 {
    margin: 0;
  }


  .planning-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap);
  }


  .timeframe-calculation-pair {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--gap);
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 0.375rem;
    border: 1px solid var(--border);
  }

  .timeframe-input-group {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .timeframe-input-group :global(.form-group) {
    flex: 1;
    margin-bottom: 0;
  }

  .timeframe-unit-select {
    padding: var(--cell-padding);
    border: 1px solid var(--color-primary);
    border-radius: 0.375rem;
    background: white;
    font-size: var(--value-size);
    color: #374151;
    cursor: pointer;
    transition: border-color 0.15s ease;
    height: 40px;
    min-width: 80px;
  }

  .timeframe-unit-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  /* Output Section */
  .output-section {
    background-color: var(--color-primary-light);
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .output-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--gap);
  }


  /* Projection Section */
  .projection-section {
    background-color: white;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
    gap: var(--gap);
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .projection-table-container {
    overflow-x: auto;
  }

  /* Payment Planning Styles */



  .period-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .period-control label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    white-space: nowrap;
  }

  .period-info {
    display: flex;
    align-items: center;
  }

  .period-label {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.875rem;
    font-style: italic;
  }

  .period-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background: white;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: border-color 0.15s ease;
  }

  .period-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  /* Chart View Styles */
  .projection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .projection-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .view-toggle {
    display: flex;
    background: #f3f4f6;
    border-radius: 8px;
    padding: 2px;
  }

  .toggle-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .toggle-btn.active {
    background: var(--color-primary);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .toggle-btn:hover:not(.active) {
    background: var(--border);
    color: #374151;
  }

  .chart-container {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--border);
    position: relative;
  }

  .chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
    font-style: italic;
  }

  .chart-tooltip {
    cursor: pointer;
  }

  .chart-tooltip:hover {
    opacity: 0.7;
  }

  .chart-data-point {
    transition: r 0.2s ease;
  }

  .custom-tooltip {
    position: fixed !important;
    background: #1f2937; /* Default fallback, will be overridden by inline style */
    color: white !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.375rem !important;
    font-size: var(--label-size) !important;
    pointer-events: none !important;
    z-index: 9999 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
    white-space: normal !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    max-width: 320px !important;
  }

  .custom-tooltip div {
    margin: 0.125rem 0;
  }

  /* Outlook Calendar Button */
  :global(.outlook-btn) {
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 3px;
    padding: 0.25rem 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
  }

  :global(.outlook-btn:hover) {
    background: color-mix(in srgb, var(--color-primary) 85%, black 15%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  :global(.outlook-btn:active) {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  }

  :global(.outlook-btn i) {
    font-size: 0.625rem;
  }

  /* Payment Schedule Section */
  .payment-schedule-section {    
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.375rem;
    border: 1px solid var(--border);
  }

  .payment-schedule-section h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .payment-schedule-section p {
    margin-top: 10px;
  }

  .schedule-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem 1rem;
    color: #6b7280;
    background: white;
    border-radius: 0.375rem;
    border: 2px dashed #d1d5db;
  }

  .schedule-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    color: #9ca3af;
  }

  .schedule-placeholder p {
    margin: 0 0 0.5rem 0;
    font-weight: 500;
    color: #374151;
  }

  .schedule-placeholder small {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.4;
  }

  /* Compact styling for payment schedule table */
  :global(.payment-schedule-table .data-table-container) {
    background: white;
    border-radius: 0.25rem;
  }

  :global(.payment-schedule-table .data-cell) {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  :global(.payment-schedule-table .data-cell.type-label) {
    font-weight: 500;
  }
  
  /* Notification Styles */
  .notification {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.875rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(400px);
    transition: transform 0.3s ease-in-out;
    z-index: 1000;
    max-width: 400px;
  }
  
  .notification-visible {
    transform: translateX(0);
  }
  
  .notification-error {
    background-color: var(--color-red);
    color: white;
  }
  
  .notification-success {
    background-color: var(--color-green);
    color: white;
  }
  
  .notification-info {
    background-color: var(--color-primary);
    color: white;
  }
  
  .notification i {
    font-size: 1rem;
  }
  
  .notification-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 0;
    margin-left: auto;
    font-size: 1rem;
    transition: color 0.2s;
  }
  
  .notification-close:hover {
    color: white;
  }

  /* Responsive Design */
  @media (max-width: 900px) {
    .planner-grid {
      grid-template-columns: 1fr;
    }
    .planner-left, .planner-right {
      width: 100%;
    }
    .planner-left {
      grid-template-columns: 1fr;
    }
  }
</style>
