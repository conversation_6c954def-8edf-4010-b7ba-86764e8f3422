<script lang="ts">
  export let title: string;
  export let icon: string = '';
  export let variant: 'default' | 'success' | 'warning' | 'danger' = 'default';
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let loading: boolean = false;
  export let clickable: boolean = false;
  
  const variantClasses = {
    default: 'border-gray-200 bg-white',
    success: 'border-green-200 bg-green-50',
    warning: 'border-yellow-200 bg-yellow-50',
    danger: 'border-red-200 bg-red-50'
  };
  
  const sizeClasses = {
    small: 'p-3',
    medium: 'p-4',
    large: 'p-6'
  };
  
  const iconColors = {
    default: 'text-gray-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600'
  };
</script>

<div 
  class="metric-card {variantClasses[variant]} {sizeClasses[size]}"
  class:clickable
  class:loading
  on:click
  on:keydown
  role={clickable ? 'button' : undefined}
  tabindex={clickable ? 0 : undefined}
>
  <div class="metric-card-header">
    <h3 class="metric-title">
      {#if icon}
        <i class="fas {icon} metric-icon"></i>
      {/if}
      {title}
    </h3>
    {#if loading}
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
    {/if}
  </div>
  
  <div class="metric-card-content">
    <slot />
  </div>
</div>

<style>
  .metric-card {
    background-color: #ffffff;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease;
  }
  
  .metric-card.clickable {
    cursor: pointer;
  }
  
  .metric-card.clickable:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  
  .metric-card.loading {
    opacity: 0.7;
    pointer-events: none;
  }
  
  .metric-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--gap);
    background-color: #f9fafb;
    border-bottom: 1px solid var(--border);
    font-weight: 600;
    color: #374151;
    font-size: var(--section-header-size);
    position: relative;
  }
  
  .metric-icon {
    color: var(--color-primary);
    margin-right: calc(var(--gap) / 2);
  }
  
  .metric-title {
    margin: 0;
    font-size: var(--section-header-size);
    font-weight: 600;
    flex: 1;
    display: flex;
    align-items: center;
    gap: calc(var(--gap) / 2);
  }
  
  .loading-spinner {
    color: var(--color-grey);
  }
  
  .metric-card-content {
    padding: calc(var(--gap) * 2);
    flex: 1;
  }
</style>