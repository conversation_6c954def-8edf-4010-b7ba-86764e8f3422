<script lang="ts">
  import { currentInstanceData, currentUser, authToken } from '$lib/stores';
  import { settings } from '$lib/stores/settings';
  import Modal from './Modal.svelte';
  import Button from './Button.svelte';
  import TagInput from './TagInput.svelte';
  import type { CaseData } from '$lib/utils/api';
  
  export let isOpen = false;
  
  // Edit functionality state
  let editingNoteId: string | null = null;
  let editContent = '';
  let isUpdating = false;
  
  // Define note structure similar to SessionNote
  interface CaseNote {
    id: string;
    tenantReference: string;
    instanceType?: 'inbound' | 'outbound';
    timestamp: Date;
    content: string;
    balance?: number;
    tags?: string[];
    caseId?: string;
  }
  
  // API cases - loaded from backend
  let apiCases: CaseData[] = [];
  let isLoadingCases = false;
  let casesLoadedForCurrentSession = false;
  
  // Load cases when modal opens for the first time in this session
  $: if (isOpen && !casesLoadedForCurrentSession && !isLoadingCases) {
    loadCases();
  }

  // Clear cases and reset session flag when modal closes
  $: if (!isOpen) {
    apiCases = [];
    casesLoadedForCurrentSession = false;
  }
  
  async function loadCases() {
    isLoadingCases = true;
    try {
      const { getCases } = await import('$lib/utils/api');
      const { authToken } = await import('$lib/stores');
      const { get } = await import('svelte/store');
      const token = get(authToken);
      
      if (!token) {
        console.error('CaseLog: No authentication token available');
        apiCases = [];
        return;
      }
      
      console.log('CaseLog: Loading cases from API');
      apiCases = await getCases(token);
      console.log('CaseLog: Successfully loaded', apiCases.length, 'cases');
      casesLoadedForCurrentSession = true;
    } catch (error) {
      console.error('CaseLog: Failed to load cases from API:', error);
      apiCases = [];
      // Show user-friendly error message
      if (error instanceof Error) {
        alert(`Failed to load cases: ${error.message}`);
      } else {
        alert('Failed to load cases. Please check your connection and try again.');
      }
    } finally {
      isLoadingCases = false;
    }
  }

  // Manual refresh function
  async function refreshCases() {
    casesLoadedForCurrentSession = false; // Reset flag to allow fresh load
    apiCases = []; // Clear the grid immediately for better UX
    await loadCases();
  }
  
  // Convert CaseNote to CaseData for API calls
  function convertNoteToCase(note: CaseNote): CaseData {
    // Find the matching case in apiCases
    const caseData = apiCases.find(c => c.id === note.caseId || c.reference === note.tenantReference);
    if (caseData) {
      return caseData;
    }
    // Fallback - create a minimal CaseData structure
    return {
      id: note.id,
      tenantId: '',
      userId: '',
      reference: note.tenantReference,
      notes: note.content,
      currentBalance: note.balance || 0,
      weeklyRent: 0,
      tenantMonthlyPayment: 0,
      apaHbMonthlyPayment: 0,
      tpdMonthlyPayment: 0,
      tenantWeeklyPayment: 0,
      benefitsHbWeeklyPayment: 0,
      weeksToNextPay: 0,
      paymentDue: 0,
      timeframe: 0,
      useWeeklyFrequency: false,
      grossWeeklyRent: 0,
      rentComponent: 0,
      nonUcServiceChargeTotal: 0,
      grossWeeklyRentOverridden: false,
      createdAt: note.timestamp.toISOString(),
      updatedAt: note.timestamp.toISOString()
    };
  }

  // Helper function to check if a note can be edited (within same calendar day)
  function canEditNote(caseData: CaseData): boolean {
    const now = new Date();
    const updatedAt = new Date(caseData.updatedAt);
    
    // Check if both dates are on the same calendar day
    const nowDateString = now.toDateString();
    const updatedAtDateString = updatedAt.toDateString();
    
    return nowDateString === updatedAtDateString;
  }
  
  // Start editing a note
  function startEdit(caseData: CaseData) {
    editingNoteId = caseData.id;
    editContent = caseData.notes;
  }
  
  // Cancel editing
  function cancelEdit() {
    editingNoteId = null;
    editContent = '';
  }
  
  // Save edited note
  async function saveEdit(caseData: CaseData) {
    if (!editContent.trim()) {
      alert('Note content cannot be empty');
      return;
    }
    
    isUpdating = true;
    try {
      const { updateCaseNotes } = await import('$lib/utils/api');
      const { authToken } = await import('$lib/stores');
      const { get } = await import('svelte/store');
      const token = get(authToken);
      
      if (!token) {
        alert('You must be logged in to update notes');
        return;
      }
      
      console.log('Updating case notes for case ID:', caseData.id);
      await updateCaseNotes(caseData.id, { notes: editContent }, token);
      
      // Update the local data after successful API call
      const caseIndex = apiCases.findIndex(c => c.id === caseData.id);
      if (caseIndex !== -1) {
        apiCases[caseIndex].notes = editContent;
        apiCases[caseIndex].updatedAt = new Date().toISOString();
        apiCases = [...apiCases]; // Trigger reactivity
      }
      
      cancelEdit();
      alert('Note updated successfully');
    } catch (error) {
      console.error('Failed to update note:', error);
      if (error instanceof Error) {
        alert(`Failed to update note: ${error.message}`);
      } else {
        alert('Failed to update note. Please check your connection and try again.');
      }
    } finally {
      isUpdating = false;
    }
  }

  // Removed loadMockCases function - all data should come from API
  
  // Convert CaseData to CaseNote format for display
  function caseToNote(caseData: CaseData): CaseNote {
    return {
      id: caseData.id,
      tenantReference: caseData.reference,
      timestamp: new Date(caseData.updatedAt),
      content: caseData.notes,
      balance: caseData.currentBalance,
      tags: [], // Cases don't have tags in current structure
      caseId: caseData.id
    };
  }
  
  // Get current tenant reference and balance for filtering and display
  $: currentReference = $currentInstanceData?.rentCalculator?.reference || '';
  $: currentBalance = $currentInstanceData?.rentCalculator?.currentBalance || 0;
  $: weeklyRent = $currentInstanceData?.rentCalculator?.weeklyRent || 0;
  
  // Escalation policy logic
  function getEscalationLevel(balance: number, weeklyRent: number): { level: string, levelDisplay: string, description: string, style: string } {
    if (balance <= 0) {
      return { level: 'NONE', levelDisplay: 'None', description: 'No action required', style: 'color: var(--color-green);' };
    }
    
    const weeksInArrears = weeklyRent > 0 ? balance / weeklyRent : 0;
    
    if (weeksInArrears >= 12) {
      return { 
        level: 'LEGAL', 
        levelDisplay: 'Legal Action',
        description: 'Court proceedings required', 
        style: 'color: var(--color-red); font-weight: bold; text-decoration: underline;' 
      };
    } else if (weeksInArrears >= 8) {
      return { 
        level: 'NSP', 
        levelDisplay: 'NSP',
        description: 'Serve Notice of Seeking Possession', 
        style: 'color: var(--color-red); font-weight: bold;' 
      };
    } else if (weeksInArrears >= 4) {
      return { 
        level: 'CONTACT_REQUIRED', 
        levelDisplay: 'Contact Required',
        description: 'Urgent tenant contact needed', 
        style: 'color: var(--color-orange); font-weight: bold;' 
      };
    } else if (balance > 0) {
      return { 
        level: 'APA', 
        levelDisplay: 'APA',
        description: 'Request direct payments', 
        style: 'color: var(--color-yellow);' 
      };
    }

    return { level: 'NONE', levelDisplay: 'None', description: 'No action required', style: 'color: var(--color-green);' };
  }
  
  $: escalation = getEscalationLevel(currentBalance, weeklyRent);
  
  // Convert API cases to notes format
  $: allNotes = apiCases.map(caseToNote).sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
  
  // Column filter options
  let filterDateTime = '';
  let filterTenantRef = '';
  let filterBalanceMin: number | undefined;
  let filterBalanceMax: number | undefined;
  let filterContent = '';
  let filterTags: string[] = [];

  // Note: availableTags not currently used but kept for potential tag functionality
  
  // Pagination
  let currentPage = 1;
  let itemsPerPage = 10;
  
  $: totalPages = Math.ceil(filteredNotes.length / itemsPerPage);
  $: paginatedNotes = filteredNotes.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  function goToPage(page: number) {
    currentPage = Math.max(1, Math.min(page, totalPages));
  }
  
  function nextPage() {
    if (currentPage < totalPages) {
      currentPage++;
    }
  }
  
  function prevPage() {
    if (currentPage > 1) {
      currentPage--;
    }
  }
  
  // Reset to first page when filters change
  $: {
    filterDateTime;
    filterTenantRef;
    filterBalanceMin;
    filterBalanceMax;
    filterContent;
    filterTags;
    currentPage = 1;
  }
  
  // Filtered notes
  $: filteredNotes = allNotes.filter(note => {
    // Filter by Date/Time
    if (filterDateTime) {
      const noteDate = new Date(note.timestamp).toISOString().slice(0, 10); // YYYY-MM-DD
      if (!noteDate.includes(filterDateTime)) {
        return false;
      }
    }

    // Filter by Tenant Ref
    if (filterTenantRef && !note.tenantReference.toLowerCase().includes(filterTenantRef.toLowerCase())) {
      return false;
    }
    
    // Filter by Balance
    if (filterBalanceMin !== undefined && note.balance !== undefined && note.balance < filterBalanceMin) {
      return false;
    }
    if (filterBalanceMax !== undefined && note.balance !== undefined && note.balance > filterBalanceMax) {
      return false;
    }

    // Filter by Content
    if (filterContent && !note.content.toLowerCase().includes(filterContent.toLowerCase())) {
      return false;
    }

    // Filter by Tags
    if (filterTags.length > 0) {
      // Note tags are already stored as tag IDs, so directly compare with selected filter tag IDs
      const noteTagIds = note.tags || [];
      
      const hasMatchingTag = filterTags.some(selectedTagId => noteTagIds.includes(selectedTagId));
      if (!hasMatchingTag) {
        return false;
      }
    }
    
    return true;
  });
  
  function formatTimestamp(date: Date): string {
    // Ensure we have a proper Date object and format in local timezone  
    const localDate = new Date(date);
    return localDate.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  }

  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  

  let tagColorMap: Map<string, string> = new Map();
  $: {
    tagColorMap = new Map($settings.tags.map(tag => [tag.name.toLowerCase(), tag.color]));
  }

  function getTagColor(tagName: string): string {
    return tagColorMap.get(tagName.toLowerCase()) || '#6b7280'; // Default gray if no color found
  }

  function openCaseDetail(note: CaseNote) {
    // Find the original case data
    const caseData = apiCases.find(c => c.id === note.caseId);
    if (!caseData) return;
    
    // Close the case log modal
    isOpen = false;

    // Create a new case detail tab
    createCaseDetailTab(caseData);
  }
  
  async function createCaseDetailTab(caseData: CaseData) {
    const { caseDetailTabs, activeCaseDetailTabId } = await import('$lib/stores');
    
    const newTabId = `case-${caseData.id}`;
    const newTab = {
      id: newTabId,
      type: 'case-detail' as const,
      caseId: caseData.id,
      tenantReference: caseData.reference,
      name: `Case: ${caseData.reference}`,
      createdAt: new Date(),
      lastModified: new Date()
    };
    
    caseDetailTabs.update((tabs: any[]) => [...tabs, newTab]);
    activeCaseDetailTabId.set(newTabId);
  }
  
  async function loadCaseFromNote(note: CaseNote) {
    // If note has a caseId, try to load the full case data
    if (note.caseId) {
      const caseData = apiCases.find(c => c.id === note.caseId);
      if (caseData) {
        openCaseDetail(note);
      }
    }
  }

  function handleOpenAnalytics() {
    // Close the case log modal
    isOpen = false;
    
    // Create analytics tab
    createAnalyticsTab();
  }
  
  async function createAnalyticsTab() {
    const { analyticsTabs, activeAnalyticsTabId } = await import('$lib/stores');
    
    const newTabId = 'analytics-' + Date.now();
    const newTab = {
      id: newTabId,
      type: 'analytics' as const,
      name: 'Analytics',
      createdAt: new Date(),
      lastModified: new Date()
    };
    
    analyticsTabs.update((tabs: any[]) => [...tabs, newTab]);
    activeAnalyticsTabId.set(newTabId);
  }

  function resetFilters() {
    filterDateTime = '';
    filterTenantRef = '';
    filterBalanceMin = undefined;
    filterBalanceMax = undefined;
    filterContent = '';
    filterTags = [];
    currentPage = 1;
  }

  function exportNotes() {
    const exportData = filteredNotes.map(note => ({
      'Tenant Reference': note.tenantReference,
      'Timestamp': formatTimestamp(note.timestamp),
      'Content': note.content
    }));
    
    const csvContent = [
      Object.keys(exportData[0]).join(','),
      ...exportData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `caselog-${currentReference || 'all'}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }
</script>

<Modal bind:isOpen title="Case Log for {$currentUser?.email || 'Unknown User'}" size="xl">
  <svelte:fragment slot="icon">
    <i class="fas fa-folder-open"></i>
  </svelte:fragment>
  
  <svelte:fragment slot="header-actions">
    <Button 
      variant="secondary"
      size="sm"
      icon="fas fa-sync-alt"
      disabled={isLoadingCases}
      on:click={refreshCases}
      title="Refresh case list"
    >
      Refresh
    </Button>
    <Button 
      variant="primary"
      size="sm"
      icon="fas fa-chart-bar"
      on:click={handleOpenAnalytics}
    >
      Analytics
    </Button>
  </svelte:fragment>
  
  <!-- Current Balance and Escalation Policy Display -->
  {#if currentReference}
    <div class="balance-escalation-section">
      <div class="balance-info">
        <div class="balance-display">
          <span class="balance-label">Current Balance:</span>
          <span class="balance-value" style="color: {currentBalance > 0 ? 'var(--color-red)' : currentBalance < 0 ? 'var(--color-green)' : '#111827'}">
            {formatCurrency(currentBalance)}
          </span>
        </div>
        <div class="tenant-ref-display">
          <span class="ref-label">Ref:</span>
          <span class="ref-value">{currentReference}</span>
        </div>
      </div>
      
      {#if escalation.level !== 'NONE'}
        <div class="escalation-policy">
          <div class="escalation-header">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Suggested Action</span>
          </div>
          <div class="escalation-action" style="{escalation.style}">
            <strong>{escalation.levelDisplay}</strong> - {escalation.description}
          </div>
        </div>
      {:else}
        <div class="escalation-policy no-action">
          <div class="escalation-header">
            <i class="fas fa-check-circle"></i>
            <span>Account Status</span>
          </div>
          <div class="escalation-action" style="{escalation.style}">
            {escalation.description}
          </div>
        </div>
      {/if}
    </div>
  {/if}
  
  <!-- Notes Display -->
  <div class="notes-section">
    <div class="notes-header">
      <div class="notes-header-actions full-width">
        <div class="pagination-info-header">
          Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredNotes.length)}-{Math.min(currentPage * itemsPerPage, filteredNotes.length)} of {filteredNotes.length} entries
        </div>
        <div class="pagination-controls-header">
          <button 
            class="pagination-btn" 
            on:click={prevPage} 
            disabled={currentPage === 1}
          >
            Previous
          </button>
          
          <div class="page-numbers">
            {#each Array(totalPages) as _, i}
              {@const pageNum = i + 1}
              {#if pageNum === 1 || pageNum === totalPages || (pageNum >= currentPage - 2 && pageNum <= currentPage + 2)}
                <button 
                  class="page-btn" 
                  class:active={pageNum === currentPage}
                  on:click={() => goToPage(pageNum)}
                >
                  {pageNum}
                </button>
              {:else if pageNum === currentPage - 3 || pageNum === currentPage + 3}
                  <span class="page-ellipsis">...</span>
              {/if}
            {/each}
          </div>
          
          <button 
            class="pagination-btn" 
            on:click={nextPage} 
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
        <div class="notes-header-right-actions">
            <Button
                variant="secondary"
                size="sm"
                icon="fas fa-download"
                on:click={exportNotes}
            >
                Export CSV
            </Button>
            <Button
                variant="secondary"
                size="sm"
                icon="fas fa-sync-alt"
                on:click={resetFilters}
            >
                Reset Filters
            </Button>
        </div>
      </div>
    </div>
    
    <div class="notes-table-container">
      <table class="notes-table">
        <thead>
          <tr>
            <th>Date/Time</th>
            <th>Tenant Ref</th>
            <th>Balance</th>
            <th>Content</th>
            <th>Tags</th>
            <th>Actions</th>
          </tr>
          <tr class="filter-row">
            <td>
              <input type="date" bind:value={filterDateTime} placeholder="Filter date" />
            </td>
            <td>
              <input type="text" bind:value={filterTenantRef} placeholder="Filter reference" />
            </td>
            <td class="balance-filter-cell">
              <input type="number" bind:value={filterBalanceMin} placeholder="Min" class="balance-filter-input"/>
              <input type="number" bind:value={filterBalanceMax} placeholder="Max" class="balance-filter-input"/>
            </td>
            <td>
              <input type="text" bind:value={filterContent} placeholder="Filter content" />
            </td>
            <td>
              <TagInput bind:selectedTags={filterTags} placeholder="Filter tags..." />
            </td>
            <td></td>
          </tr>
        </thead>
        <tbody>
          {#each paginatedNotes as note (note.id)}
            <tr
              class="clickable-row"
              on:click={() => loadCaseFromNote(note)}
              title="Click to load case or open case details in new tab"
            >
              <td>{formatTimestamp(note.timestamp)}</td>
              <td>{note.tenantReference}</td>
              <td>
                {#if note.balance !== undefined}
                  <span class="balance-cell" style="color: {note.balance > 0 ? 'var(--color-red)' : note.balance < 0 ? 'var(--color-green)' : '#111827'}">
                    {formatCurrency(note.balance)}
                  </span>
                {/if}
              </td>
              <td class="content-cell">
                {#if editingNoteId === note.id}
                  <textarea
                    bind:value={editContent}
                    class="edit-textarea"
                    rows="3"
                    disabled={isUpdating}
                    on:click|stopPropagation
                  ></textarea>
                {:else}
                  {note.content}
                {/if}
              </td>
              <td>
                {#if note.tags && note.tags.length > 0}
                  <div class="note-tags-table">
                    {#each note.tags as tag}
                      <span class="tag" style="background-color: {getTagColor(tag)};">{tag}</span>
                    {/each}
                  </div>
                {/if}
              </td>
              <td class="actions-cell">
                {#if editingNoteId === note.id}
                  <div class="edit-actions">
                    <button 
                      class="action-btn save-btn" 
                      on:click|stopPropagation={() => saveEdit(convertNoteToCase(note))}
                      disabled={isUpdating}
                    >
                      {isUpdating ? 'Saving...' : 'Save'}
                    </button>
                    <button 
                      class="action-btn cancel-btn" 
                      on:click|stopPropagation={cancelEdit}
                      disabled={isUpdating}
                    >
                      Cancel
                    </button>
                  </div>
                {:else if canEditNote(convertNoteToCase(note))}
                  <button 
                    class="action-btn edit-btn" 
                    on:click|stopPropagation={() => startEdit(convertNoteToCase(note))}
                    title="Edit note (available until end of day)"
                  >
                    Edit
                  </button>
                {:else}
                  <span class="edit-expired" title="Edit period expired (same day limit)">
                    Edit expired
                  </span>
                {/if}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
    
    {#if isLoadingCases}
      <div class="loading-state-overlay">
        <div class="loading-spinner"></div>
        <h4>Loading cases...</h4>
        <p>Please wait while we fetch your case history.</p>
      </div>
    {:else if filteredNotes.length === 0}
      <div class="empty-state-overlay">
        <div class="empty-icon">📭</div>
        <h4>No cases found</h4>
        {#if !$authToken}
          <p>Please log in to view cases.</p>
        {:else}
          <p>No cases match your current filters. Try adjusting your filters.</p>
        {/if}
      </div>
    {/if}
    
    <!-- Pagination Controls - Bottom section for larger screens -->
    {#if totalPages > 1}
      <div class="pagination-section-bottom">
        <div class="pagination-info">
          Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredNotes.length)}-{Math.min(currentPage * itemsPerPage, filteredNotes.length)} of {filteredNotes.length} entries
        </div>
        <div class="pagination-controls">
          <button 
            class="pagination-btn" 
            on:click={prevPage} 
            disabled={currentPage === 1}
          >
            Previous
          </button>
          
          <div class="page-numbers">
            {#each Array(totalPages) as _, i}
              {@const pageNum = i + 1}
              {#if pageNum === 1 || pageNum === totalPages || (pageNum >= currentPage - 2 && pageNum <= currentPage + 2)}
                <button 
                  class="page-btn" 
                  class:active={pageNum === currentPage}
                  on:click={() => goToPage(pageNum)}
                >
                  {pageNum}
                </button>
              {:else if pageNum === currentPage - 3 || pageNum === currentPage + 3}
                <span class="page-ellipsis">...</span>
              {/if}
            {/each}
          </div>
          
          <button 
            class="pagination-btn" 
            on:click={nextPage} 
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      </div>
    {/if}
  </div>
  
</Modal>

<style>
  /* User Indicator Section */
  .user-indicator {
    background: var(--color-primary);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 12px rgba(2, 85, 130, 0.2);
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .user-info i {
    font-size: 2rem;
    opacity: 0.9;
  }

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .user-details strong {
    font-size: 1.1rem;
    font-weight: 600;
  }

  .user-details small {
    opacity: 0.8;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .privacy-notice {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
  }

  .privacy-notice i {
    font-size: 0.875rem;
  }

  /* Balance and Escalation Section */
  .balance-escalation-section {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 2px solid var(--border);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .balance-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .balance-display {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .balance-label {
    font-weight: 600;
    color: #374151;
    font-size: 1.1rem;
  }

  .balance-value {
    font-weight: 700;
    font-size: 1.5rem;
    font-family: monospace;
  }

  .tenant-ref-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .ref-label {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ref-value {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 1rem;
  }

  .escalation-policy {
    background: white;
    border: 2px solid #f59e0b;
    border-radius: 8px;
    padding: 1rem;
    min-width: 280px;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
  }

  .escalation-policy.no-action {
    border-color: #10b981;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.1);
  }

  .escalation-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .escalation-header i {
    font-size: 1rem;
    color: #f59e0b;
  }

  .escalation-policy.no-action .escalation-header i {
    color: #10b981;
  }

  .escalation-action {
    font-size: 1rem;
    line-height: 1.4;
  }

  /* Notes Header */
  .notes-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
  }

  .notes-header h4 {
    margin: 0;
    color: var(--color-primary);
  }

  .notes-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
  }


  .empty-state-overlay, .loading-state-overlay {
    position: absolute;
    top: 100px; /* Adjust based on header height */
    left: 0;
    right: 0;
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .empty-state { /* Keep original empty-state for standalone use if needed */
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .empty-state p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
  }

  /* Notes Table Styles */
  .notes-table-container {
    min-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--border);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .notes-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    color: #374151;
  }

  .notes-table thead {
    background: var(--color-primary-light);
    position: sticky;
    top: 0; /* Changed to 0 to stick the whole header */
    z-index: 10;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
  }

  .notes-table th {
    padding: 0.75rem 1rem; /* Adjusted padding */
    text-align: left;
    font-weight: 600;
    color: var(--color-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--color-primary);
  }

  /* Filter Row Styles */
  .filter-row td {
    padding: 0.5rem 1rem; /* Smaller padding for filter cells */
    border-bottom: 1px solid var(--border);
  }

  .filter-row input,
  .filter-row select {
    width: 100%;
    padding: 0.3rem 0.5rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 0.8rem;
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
  }

  .filter-row select {
    appearance: none; /* Remove default select arrow */
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%236b7280%22%20d%3D%22M287%2C197.9L159.3%2C68.9c-1.8-1.8-4.7-1.8-6.5%2C0L5.4%2C197.9c-1.8%2C1.8-1.8%2C4.7%2C0%2C6.5s4.7%2C1.8%2C6.5%2C0l143.2-143.2l143.2%2C143.2c1.8%2C1.8%2C4.7%2C1.8%2C6.5%2C0S288.8%2C199.7%2C287%2C197.9z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 0.6em auto;
    padding-right: 1.5rem;
  }

  .filter-row input:focus,
  .filter-row select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary-light);
  }

  .balance-filter-cell {
    display: flex;
    gap: 0.5rem;
  }

  .balance-filter-input {
    flex: 1;
  }

  .filter-row :global(.tag-input-container) {
    width: 100%;
  }

  .filter-row :global(.tag-input-wrapper) {
    border: 1px solid var(--border);
    border-radius: 4px;
    box-shadow: none;
    min-height: unset;
    padding: 0.1rem 0.25rem;
  }

  .filter-row :global(.tag-input-wrapper:focus-within) {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary-light);
    transform: none;
  }

  .filter-row :global(.tag-input) {
    height: 28px;
    padding: 0.2rem 0.25rem;
    font-size: 0.8rem;
  }

  .filter-row :global(.tag-chip) {
    padding: 0.1rem 0.3rem;
    font-size: 0.65rem;
  }

  .filter-row :global(.tag-remove i) {
    font-size: 0.5rem;
  }

  .notes-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border);
    vertical-align: top;
  }

  .notes-table tbody tr:last-child td {
    border-bottom: none;
  }

  .notes-table tbody tr:nth-child(even) {
    background: #fdfdfe;
  }

  .notes-table tbody tr:hover {
    background: #f0f8ff;
  }

  .notes-table tbody tr.clickable-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .notes-table tbody tr.clickable-row:hover {
    background: #e0f2fe;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .balance-cell {
    font-weight: 600;
    font-family: monospace;
  }

  .note-tags-table {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-top: 0.25rem;
  }

  .tag {
    /* background-color set dynamically */
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.65rem; /* Slightly smaller for table */
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    white-space: nowrap;
  }


  .notes-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-grow: 1; /* Allow actions to take available space */
    justify-content: space-between; /* Distribute items */
  }

  .notes-header-actions.full-width {
    width: 100%;
  }

  .notes-header-right-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  /* Pagination Styles - Now in header */
  .pagination-info-header {
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap; /* Prevent wrapping */
  }

  .pagination-controls-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .pagination-btn {
    padding: 0.5rem 1rem;
    background: #ffffff;
    border: 2px solid var(--border);
    border-radius: 8px;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .pagination-btn:hover:not(:disabled) {
    background: var(--color-primary);
    color: #ffffff;
    border-color: var(--color-primary);
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f3f4f6;
    color: #9ca3af;
  }

  .page-numbers {
    display: flex;
    gap: 0.25rem;
    align-items: center;
  }

  .page-btn {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    background: #ffffff;
    border: 2px solid var(--border);
    border-radius: 8px;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .page-btn:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
  }

  .page-btn.active {
    background: var(--color-primary);
    color: #ffffff;
    border-color: var(--color-primary);
  }

  .page-ellipsis {
    padding: 0 0.5rem;
    color: #9ca3af;
    font-weight: 500;
  }

  /* Remove old pagination section */
  .pagination-section-bottom {
    display: none;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .user-indicator {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem;
    }

    .user-info {
      flex-direction: column;
      align-items: flex-start;
      text-align: left;
      gap: 0.75rem;
    }

    .user-details strong {
      font-size: 1rem;
    }

    .privacy-notice {
      align-self: stretch;
      justify-content: center;
      padding: 0.75rem;
    }

    .notes-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }


    .notes-table-container {
      overflow-x: auto; /* Allow horizontal scroll for table on small screens */
    }

    .notes-table {
      min-width: 600px; /* Ensure table doesn't shrink too much */
    }

    .notes-table th,
    .notes-table td {
      padding: 0.5rem 0.75rem;
    }

    .pagination-controls {
      flex-direction: column;
      gap: 1rem;
    }

    .page-numbers {
      justify-content: center;
      flex-wrap: wrap;
    }

    .pagination-btn {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
    }

    .page-btn {
      width: 2rem;
      height: 2rem;
      font-size: 0.75rem;
    }

    .balance-escalation-section {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
      padding: 1rem;
    }

    .balance-info {
      align-items: center;
      text-align: center;
    }

    .balance-display {
      justify-content: center;
      flex-wrap: wrap;
    }

    .tenant-ref-display {
      justify-content: center;
    }

    .escalation-policy {
      min-width: unset;
      text-align: center;
    }
  }

  /* Edit functionality styles */
  .actions-cell {
    min-width: 120px;
    text-align: center;
  }

  .edit-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
  }

  .action-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    border: 1px solid;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .edit-btn {
    background: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
  }

  .edit-btn:hover {
    background: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
  }

  .save-btn {
    background: var(--color-green);
    color: white;
    border-color: var(--color-green);
  }

  .save-btn:hover:not(:disabled) {
    background: #059669;
    border-color: #059669;
  }

  .cancel-btn {
    background: #f3f4f6;
    color: #374151;
    border-color: #d1d5db;
  }

  .cancel-btn:hover:not(:disabled) {
    background: #e5e7eb;
    border-color: #9ca3af;
  }

  .action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .edit-expired {
    font-size: 0.75rem;
    color: #9ca3af;
    font-style: italic;
  }

  .edit-textarea {
    width: 100%;
    min-width: 200px;
    max-width: 400px;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    resize: vertical;
  }

  .edit-textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
  }

  .content-cell {
    max-width: 300px;
    word-wrap: break-word;
  }
</style>