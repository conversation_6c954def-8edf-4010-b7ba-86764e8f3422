<script lang="ts">
  import WebsitePage from './WebsitePage.svelte';
  
  export let title: string;
  export let lastUpdated: string = '';
  export let showTableOfContents: boolean = true;
  
  let tocItems: { id: string; text: string; level: number }[] = [];
  let contentElement: HTMLElement;
  
  function generateTableOfContents() {
    if (!contentElement) return;
    
    const headings = contentElement.querySelectorAll('h2, h3, h4');
    tocItems = Array.from(headings).map((heading, index) => {
      const id = `section-${index + 1}`;
      heading.id = id;
      
      return {
        id,
        text: heading.textContent || '',
        level: parseInt(heading.tagName.charAt(1))
      };
    });
  }
  
  function scrollToSection(id: string) {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
  
  let mounted = false;
  import { onMount } from 'svelte';
  
  onMount(() => {
    mounted = true;
    setTimeout(generateTableOfContents, 100);
  });
</script>

<WebsitePage {title} subtitle="Legal information and policies" heroBackground="var(--color-primary)">
  <div class="legal-page">
    {#if showTableOfContents && tocItems.length > 0}
      <aside class="table-of-contents">
        <h3>Table of Contents</h3>
        <nav>
          <ul>
            {#each tocItems as item}
              <li class="toc-level-{item.level}">
                <button 
                  type="button"
                  on:click={() => scrollToSection(item.id)}
                  class="toc-link"
                >
                  {item.text}
                </button>
              </li>
            {/each}
          </ul>
        </nav>
      </aside>
    {/if}
    
    <main class="legal-content" bind:this={contentElement}>
      {#if lastUpdated}
        <div class="last-updated">
          <p><strong>Last updated:</strong> {lastUpdated}</p>
        </div>
      {/if}
      
      <div class="content-body">
        <slot />
      </div>
      
      <div class="legal-footer">
        <p>
          <strong>Contact Information:</strong><br>
          If you have any questions about this {title.toLowerCase()}, please contact us at 
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
      </div>
    </main>
  </div>
</WebsitePage>

<style>
  .legal-page {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 3rem;
    align-items: start;
  }

  .table-of-contents {
    position: sticky;
    top: 2rem;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
  }

  .table-of-contents h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-primary);
    margin: 0 0 1rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .table-of-contents nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .table-of-contents nav li {
    margin-bottom: 0.5rem;
  }

  .toc-link {
    display: block;
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #6b7280;
    cursor: pointer;
    text-decoration: none;
    transition: color 0.2s ease;
    line-height: 1.4;
  }

  .toc-link:hover {
    color: var(--color-primary);
  }

  .toc-level-2 {
    padding-left: 0;
  }

  .toc-level-3 {
    padding-left: 1rem;
  }

  .toc-level-4 {
    padding-left: 2rem;
  }

  .toc-level-3 .toc-link,
  .toc-level-4 .toc-link {
    font-size: 0.8125rem;
  }

  .legal-content {
    max-width: none;
  }

  .last-updated {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .last-updated p {
    margin: 0;
    font-size: 0.875rem;
    color: #0369a1;
  }

  .content-body {
    line-height: 1.7;
  }

  .content-body :global(h2) {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--color-primary);
    margin: 3rem 0 1.5rem 0;
    padding-top: 1rem;
    border-top: 2px solid #e5e7eb;
  }

  .content-body :global(h2:first-child) {
    margin-top: 0;
    border-top: none;
    padding-top: 0;
  }

  .content-body :global(h3) {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-primary);
    margin: 2.5rem 0 1rem 0;
  }

  .content-body :global(h4) {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin: 2rem 0 0.75rem 0;
  }

  .content-body :global(p) {
    font-size: 1rem;
    color: #374151;
    margin: 0 0 1.5rem 0;
  }

  .content-body :global(ul),
  .content-body :global(ol) {
    padding-left: 1.5rem;
    margin: 0 0 1.5rem 0;
  }

  .content-body :global(li) {
    font-size: 1rem;
    color: #374151;
    margin-bottom: 0.75rem;
    line-height: 1.6;
  }

  .content-body :global(strong) {
    font-weight: 600;
    color: var(--color-primary);
  }

  .content-body :global(a) {
    color: var(--color-primary);
    text-decoration: underline;
    transition: color 0.2s ease;
  }

  .content-body :global(a:hover) {
    color: var(--color-secondary);
  }

  .content-body :global(blockquote) {
    border-left: 4px solid var(--color-primary);
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #6b7280;
  }

  .content-body :global(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .content-body :global(th),
  .content-body :global(td) {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .content-body :global(th) {
    background: #f9fafb;
    font-weight: 600;
    color: var(--color-primary);
  }

  .legal-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 0.75rem;
    padding: 2rem;
  }

  .legal-footer p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.6;
  }

  .legal-footer a {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 500;
  }

  .legal-footer a:hover {
    text-decoration: underline;
  }

  /* Mobile responsive */
  @media (max-width: 1024px) {
    .legal-page {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .table-of-contents {
      position: static;
      order: -1;
    }
  }

  @media (max-width: 768px) {
    .table-of-contents {
      padding: 1rem;
    }

    .content-body :global(h2) {
      font-size: 1.5rem;
      margin: 2rem 0 1rem 0;
    }

    .content-body :global(h3) {
      font-size: 1.25rem;
      margin: 1.5rem 0 0.75rem 0;
    }

    .legal-footer {
      padding: 1.5rem;
    }
  }
</style>