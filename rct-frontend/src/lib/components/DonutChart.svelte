<script lang="ts">
  export let data: Array<{label: string, value: number, color: string}> = [];
  export let size: number = 120;
  export let strokeWidth: number = 20;
  export let showLabels: boolean = true;
  export let showValues: boolean = true;
  export let showPercentages: boolean = true;
  export let centerText: string = '';
  export let centerValue: string = '';
  
  $: total = data.reduce((sum, item) => sum + item.value, 0);
  $: radius = (size - strokeWidth) / 2;
  $: circumference = 2 * Math.PI * radius;
  $: center = size / 2;
  
  // Calculate arc data
  $: arcs = data.reduce((acc, item, index) => {
    const percentage = total > 0 ? (item.value / total) * 100 : 0;
    const strokeDasharray = (percentage / 100) * circumference;
    const previousPercentage = acc.reduce((sum, arc) => sum + arc.percentage, 0);
    const strokeDashoffset = circumference - (previousPercentage / 100) * circumference;
    
    acc.push({
      ...item,
      percentage,
      strokeDasharray,
      strokeDashoffset
    });
    
    return acc;
  }, [] as Array<{label: string, value: number, color: string, percentage: number, strokeDasharray: number, strokeDashoffset: number}>);
</script>

<div class="donut-chart-container">
  <div class="donut-chart">
    <svg width={size} height={size} class="donut-svg">
      <!-- Background circle -->
      <circle
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="#f1f5f9"
        stroke-width={strokeWidth}
      />
      
      <!-- Data arcs -->
      {#each arcs as arc, index}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={arc.color}
          stroke-width={strokeWidth}
          stroke-dasharray="{arc.strokeDasharray} {circumference}"
          stroke-dashoffset={arc.strokeDashoffset}
          transform="rotate(-90 {center} {center})"
          class="donut-arc"
          style="transition: stroke-dasharray 0.5s ease {index * 0.1}s;"
        />
      {/each}
      
      <!-- Center text -->
      {#if centerText || centerValue}
        <text
          x={center}
          y={center}
          text-anchor="middle"
          dominant-baseline="central"
          class="center-text"
        >
          {#if centerValue}
            <tspan x={center} dy="-0.3em" class="center-value">{centerValue}</tspan>
            <tspan x={center} dy="1.2em" class="center-label">{centerText}</tspan>
          {:else}
            {centerText}
          {/if}
        </text>
      {/if}
    </svg>
  </div>
  
  {#if showLabels && data.length > 0}
    <div class="donut-legend">
      {#each arcs as arc}
        <div class="legend-item">
          <div class="legend-color" style="background-color: {arc.color}"></div>
          <span class="legend-label">{arc.label}</span>
          <div class="legend-values">
            {#if showValues}
              <span class="legend-value">{arc.value}</span>
            {/if}
            {#if showPercentages}
              <span class="legend-percentage">({Math.round(arc.percentage)}%)</span>
            {/if}
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .donut-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .donut-chart {
    position: relative;
  }
  
  .donut-svg {
    transform: rotate(0deg);
  }
  
  .donut-arc {
    stroke-linecap: round;
  }
  
  .center-text {
    font-family: inherit;
    font-size: 0.875rem;
    fill: var(--color-text);
  }
  
  .center-value {
    font-size: 1.25rem;
    font-weight: 600;
    fill: var(--color-text);
  }
  
  .center-label {
    font-size: 0.75rem;
    fill: var(--color-grey);
  }
  
  .donut-legend {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }
  
  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .legend-label {
    flex: 1;
    color: var(--color-text);
  }
  
  .legend-values {
    display: flex;
    gap: 0.25rem;
    font-size: 0.75rem;
  }
  
  .legend-value {
    font-weight: 600;
    color: var(--color-text);
  }
  
  .legend-percentage {
    color: var(--color-grey);
  }
</style>