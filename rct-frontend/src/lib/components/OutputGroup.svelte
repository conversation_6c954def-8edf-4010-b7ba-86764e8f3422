<script lang="ts">
  export let label: string;
  export let value: string | number;
  export let color: string = '#111827'; // Default text color
  export let className: string = '';
  export let spanTwoColumns: boolean = false;
  export let prefix: string = '';
  export let suffix: string = '';
  export let isHighlighted: boolean = false;
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let showCurrency: boolean = false;
  
  // Check if value is a currency value (contains £ or is formatted currency)
  $: isCurrencyValue = showCurrency || (typeof value === 'string' && value.includes('£'));
  
  // Format the display value - remove £ if it's there since we'll show it separately
  $: cleanValue = typeof value === 'string' ? value.replace('£', '') : value;
  $: displayValue = `${prefix}${cleanValue}${suffix}`;
  
  // Determine if value indicates negative/positive for automatic color coding
  $: isNegative = typeof value === 'number' && value < 0;
  $: isPositive = typeof value === 'number' && value > 0;
  
  // Auto color for financial values if no explicit color is provided
  $: autoColor = color === '#111827' && typeof value === 'number' 
    ? (isNegative ? '#dc2626' : isPositive ? '#059669' : '#111827')
    : color;
</script>

<div 
  class="output-group {className} size-{size}" 
  class:span-two={spanTwoColumns}
  class:highlighted={isHighlighted}
>
  <div class="output-label">{label}</div>
  {#if isCurrencyValue}
    <div class="currency-output">
      <span class="currency-symbol">£</span>
      <div 
        class="output-value currency-value" 
        style="color: {autoColor}"
      >
        {displayValue}
      </div>
    </div>
  {:else}
    <div 
      class="output-value" 
      style="color: {autoColor}"
    >
      {displayValue}
    </div>
  {/if}
</div>

<style>
  .output-group {
    display: flex;
    flex-direction: column;
    /* justify-content: space-between; */
    align-items: flex-start;
    box-sizing: border-box;
  }

  .output-group:hover {
    border-color: #9ca3af;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .output-group.highlighted {
    background-color: #f0f9ff;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .output-group.span-two {
    grid-column: span 2;
  }

  .output-label {
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--label-size);
    margin-bottom: 0.25rem;
    line-height: 1.25;
  }

  .output-value {
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-weight: 600;
    color: #111827;
    line-height: 1.5;
    word-break: break-word;
    font-variant-numeric: tabular-nums;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: calc(var(--cell-padding) * 1.5) var(--cell-padding);
    background-color: #f8fafc;
    font-size: var(--value-size);
    width: 100%;
    /* min-height: 43px; */
    display: flex;
    align-items: center;
  }

  /* Currency Output Styling */
  .currency-output {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .currency-symbol {
    position: absolute;
    left: 0.75rem;
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--value-size);
    z-index: 1;
    pointer-events: none;
  }

  .currency-value {
    padding-left: 2rem !important;
  }

  /* Size variants */
  .size-small .output-label {
    font-size: 0.8rem;
  }


  .size-small {
    padding: 0.5rem;
  }


  .size-large .output-label {
    font-size: 1rem;
  }


  .size-large {
    padding: 1rem;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .output-group {
      padding: 0.5rem;
    }

    .size-large .output-value {
      font-size: 1rem;
    }
  }
</style>