<script lang="ts">
  export let title: string = '';
  export let layout: 'single' | 'two-column' | 'three-column' | 'full-width' = 'single';
  export let background: 'white' | 'light' | 'primary' = 'white';
  export let padding: 'small' | 'medium' | 'large' = 'medium';
  export let maxWidth: string = '1200px';
  
  $: sectionClass = `content-section layout-${layout} bg-${background} padding-${padding}`;
</script>

<section class={sectionClass}>
  <div class="section-container" style="max-width: {maxWidth}">
    {#if title}
      <div class="section-header">
        <h2>{title}</h2>
      </div>
    {/if}
    
    <div class="section-content">
      <slot />
    </div>
  </div>
</section>

<style>
  .content-section {
    width: 100%;
  }

  .section-container {
    margin: 0 auto;
    padding: 0 2rem;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;
  }

  .section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
  }

  /* Layout variations */
  .layout-single .section-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .layout-two-column .section-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
  }

  .layout-three-column .section-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .layout-full-width .section-content {
    width: 100%;
  }

  /* Background variations */
  .bg-white {
    background: white;
  }

  .bg-light {
    background: #f9fafb;
  }

  .bg-primary {
    background: var(--color-primary);
    color: white;
  }

  .bg-primary .section-header h2 {
    color: white;
  }

  /* Padding variations */
  .padding-small {
    padding: 2rem 0;
  }

  .padding-medium {
    padding: 4rem 0;
  }

  .padding-large {
    padding: 6rem 0;
  }

  /* Content styling */
  .section-content :global(h3) {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: var(--color-primary);
  }

  .bg-primary .section-content :global(h3) {
    color: white;
  }

  .section-content :global(h4) {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    color: var(--color-primary);
  }

  .bg-primary .section-content :global(h4) {
    color: white;
  }

  .section-content :global(p) {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
    color: #374151;
  }

  .bg-primary .section-content :global(p) {
    color: rgba(255, 255, 255, 0.9);
  }

  .section-content :global(ul) {
    margin: 0 0 1.5rem 0;
    padding-left: 1.5rem;
  }

  .section-content :global(li) {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 0.5rem;
    color: #374151;
  }

  .bg-primary .section-content :global(li) {
    color: rgba(255, 255, 255, 0.9);
  }

  .section-content :global(.feature-list) {
    list-style: none;
    padding: 0;
  }

  .section-content :global(.feature-list li) {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
  }

  .bg-primary .section-content :global(.feature-list li) {
    border-bottom-color: rgba(255, 255, 255, 0.2);
  }

  .section-content :global(.feature-list li:last-child) {
    border-bottom: none;
  }

  .section-content :global(.feature-list i) {
    color: var(--color-primary);
    margin-top: 0.125rem;
    font-size: 1.1rem;
    flex-shrink: 0;
  }

  .bg-primary .section-content :global(.feature-list i) {
    color: white;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .section-container {
      padding: 0 1rem;
    }

    .section-header {
      margin-bottom: 2rem;
    }

    .section-header h2 {
      font-size: 2rem;
    }

    .layout-two-column .section-content,
    .layout-three-column .section-content {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .padding-small {
      padding: 1.5rem 0;
    }

    .padding-medium {
      padding: 3rem 0;
    }

    .padding-large {
      padding: 4rem 0;
    }
  }
</style>