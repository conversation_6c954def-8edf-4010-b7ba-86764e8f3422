<script lang="ts">
  import { updateNotes, currentInstanceData, activeTabId, updateRentCalculatorData, authToken } from '$lib/stores';
  import { userPreferences } from '$lib/stores/userPreferences';
  import { onMount } from 'svelte';
  import FormGroup from './FormGroup.svelte';
  import Button from './Button.svelte';
  import StandardResponses from './StandardResponses.svelte';
  import TalkingPoints from './TalkingPoints.svelte';
  import TagInput from './TagInput.svelte';
  import { searchCaseByReference } from '$lib/utils/api';
  
  export let tenantReference: string = '';
  
  let notes = '';
  let selectedTags: string[] = [];
  let notesFocused = false;
  let notesTextarea: HTMLTextAreaElement | null = null;
  let showSuccessToast = false;
  let showBalanceToast = false;
  let notesVisible = true;
  let windowWidth = 0;
  
  // State management for save operations
  let isSaving = false;
  let lastSaveTime = 0;
  let showDuplicateToast = false;
  let isSearching = false;
  let showSearchToast = false;
  let searchToastMessage = '';
  let searchToastType: 'success' | 'error' = 'success';
  
  onMount(() => {
    // Load notes visibility from localStorage
    const savedNotesVisible = localStorage.getItem('notesVisible');
    if (savedNotesVisible !== null) {
      notesVisible = savedNotesVisible === 'true';
    }

    // Set initial window width
    windowWidth = window.innerWidth;

    // Add resize listener
    const handleResize = () => {
      windowWidth = window.innerWidth;
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  });

  // Reactive statement to add/remove global class for notes sidebar mode
  $: if (typeof document !== 'undefined') {
    // Check if sidebar mode is active and screen is wide enough (> 900px)
    const isScreenWideEnough = windowWidth > 900;
    const isNotesSidebarActive = notesVisible && $userPreferences.notesPosition === 'sidebar' && isScreenWideEnough;

    if (isNotesSidebarActive) {
      document.body.classList.add('notes-sidebar-active');
    } else {
      document.body.classList.remove('notes-sidebar-active');
    }
  }
  

  // Get rent calculator data from current instance
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;

  // Subscribe to current instance data to get notes
  $: if ($currentInstanceData) {
    notes = $currentInstanceData.notes;
  }
  
  function handleNotesChange() {
    updateNotes(notes);
  }
  

  async function saveNotes() {
    if (!notes.trim() || isSaving) {
      return;
    }
    
    // Get current reference from rent calculator data
    const currentReference = rentCalculatorData?.reference || tenantReference || 'Unknown';
    
    // Check time-based cooldown (3 seconds between saves for same tenant)
    const now = Date.now();
    if (now - lastSaveTime < 3000) {
      showDuplicateNotification();
      return;
    }
    
    isSaving = true;
    
    try {
      // Copy to clipboard
      await navigator.clipboard.writeText(notes);
      
      // Update the current instance notes
      updateNotes(notes);
      
      // Save case notes to backend if we have a token
      const token = $authToken;
      if (token && $currentInstanceData) {
        await saveCaseNotesToBackend(notes, token);
      } else {
        console.log('No auth token available - notes saved locally only');
      }
      
      // Update last save time
      lastSaveTime = now;
      
      // Show success feedback
      console.log('Notes saved to case');
      showSuccessNotification();
      
      // Clear tags after saving (keep notes for user to manually clear)
      selectedTags = [];
      
    } catch (error) {
      console.error('Failed to save notes:', error);
      // Still update local notes even if backend fails
      updateNotes(notes);
      lastSaveTime = now;
      showSuccessNotification();
      selectedTags = [];
    } finally {
      isSaving = false;
    }
  }
  
  async function saveCaseNotesToBackend(notesContent: string, token: string) {
    try {
      const { getCases, createCase, updateCaseNotes } = await import('$lib/utils/api');
      
      // Get current case data
      const currentData = $currentInstanceData;
      if (!currentData) return;
      
      const reference = currentData.rentCalculator.reference;
      if (!reference) {
        console.log('No reference found - cannot save case notes');
        return;
      }
      
      // Try to find existing case by reference
      const cases = await getCases(token);
      const existingCase = cases.find(c => c.reference === reference);
      
      if (existingCase) {
        // Update existing case notes
        await updateCaseNotes(existingCase.id, { notes: notesContent }, token);
        console.log('Updated existing case notes for:', reference);
      } else {
        // Create new case with notes
        const newCase = {
          reference: currentData.rentCalculator.reference,
          currentBalance: currentData.rentCalculator.currentBalance,
          weeklyRent: currentData.rentCalculator.weeklyRent,
          tenantMonthlyPayment: currentData.rentCalculator.tenantMonthlyPayment,
          apaHbMonthlyPayment: currentData.rentCalculator.apaHbMonthlyPayment,
          tpdMonthlyPayment: currentData.rentCalculator.tpdMonthlyPayment,
          tenantWeeklyPayment: currentData.rentCalculator.tenantWeeklyPayment,
          benefitsHbWeeklyPayment: currentData.rentCalculator.benefitsHbWeeklyPayment,
          weeksToNextPay: currentData.arrangementPlanner.weeksToNextPay,
          paymentDue: currentData.arrangementPlanner.paymentDue,
          timeframe: currentData.arrangementPlanner.timeframe,
          useWeeklyFrequency: currentData.arrangementPlanner.useWeeklyFrequency || false,
          grossWeeklyRent: currentData.accountCharges.grossWeeklyRent,
          rentComponent: currentData.accountCharges.rentComponent,
          nonUcServiceChargeFormula: currentData.accountCharges.nonUcServiceChargeFormula || '',
          nonUcServiceChargeTotal: currentData.accountCharges.nonUcServiceChargeTotal || 0,
          grossWeeklyRentOverridden: currentData.accountCharges.grossWeeklyRentOverridden || false,
          notes: notesContent
        };
        
        await createCase(newCase, token);
        console.log('Created new case with notes for:', reference);
      }
    } catch (error) {
      console.error('Failed to save case notes to backend:', error);
      throw error;
    }
  }
  
  function clearNotes() {
    notes = '';
    selectedTags = [];
    updateNotes('');
  }

  function showSuccessNotification() {
    showSuccessToast = true;
    // Auto-hide after 3 seconds
    setTimeout(() => {
      showSuccessToast = false;
    }, 3000);
  }

  function showDuplicateNotification() {
    showDuplicateToast = true;
    // Auto-hide after 3 seconds
    setTimeout(() => {
      showDuplicateToast = false;
    }, 3000);
  }

  let referenceToastIsError = false;
  
  function showReferenceCopiedNotification(isError = false) {
    referenceToastIsError = isError;
    showBalanceToast = true;
    // Auto-hide after 3 seconds
    setTimeout(() => {
      showBalanceToast = false;
      referenceToastIsError = false;
    }, 3000);
  }

  function handleTagsChanged(event: CustomEvent) {
    selectedTags = event.detail;
  }
  


  function handleReferenceInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newRef = target?.value.trim() ?? '';
    updateRentCalculatorData({ reference: newRef });
  }

  function handleNameInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newName = target?.value.trim() ?? '';
    updateRentCalculatorData({ name: newName });
  }

  async function searchByReference() {
    const reference = rentCalculatorData?.reference?.trim();
    if (!reference || !$authToken) {
      return;
    }

    isSearching = true;
    
    try {
      const caseData = await searchCaseByReference(reference, $authToken);
      
      if (caseData.name) {
        updateRentCalculatorData({ name: caseData.name });
        showSearchToastMessage('Case found and name populated', 'success');
      } else {
        showSearchToastMessage('Case found but no name available', 'success');
      }
    } catch (error) {
      console.error('Search failed:', error);
      showSearchToastMessage('No case found with that reference', 'error');
    } finally {
      isSearching = false;
    }
  }

  function showSearchToastMessage(message: string, type: 'success' | 'error') {
    searchToastMessage = message;
    searchToastType = type;
    showSearchToast = true;
    setTimeout(() => {
      showSearchToast = false;
    }, 3000);
  }

  async function handleReferenceKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === 'Return') {
      event.preventDefault();
      // Copy reference to clipboard
      await copyReferenceNumber();
      // Also search for the reference
      await searchByReference();
    }
  }

  async function copyReferenceNumber() {
    const referenceNumber = rentCalculatorData?.reference || '';
    
    if (!referenceNumber.trim()) {
      console.log('❌ No reference number to copy');
      showReferenceCopiedNotification(true);
      return;
    }
    
    console.log('Attempting to copy reference number:', referenceNumber);
    
    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
      try {
        await navigator.clipboard.writeText(referenceNumber);
        console.log('✅ Reference number copied to clipboard via Clipboard API:', referenceNumber);
        showReferenceCopiedNotification();
        return;
      } catch (error) {
        console.error('❌ Clipboard API failed:', error);
        // Fall through to legacy method
      }
    }
    
    // Fallback to legacy method
    try {
      const textArea = document.createElement('textarea');
      textArea.value = referenceNumber;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (result) {
        console.log('✅ Reference number copied to clipboard via legacy method:', referenceNumber);
        showReferenceCopiedNotification();
      } else {
        console.error('❌ Legacy copy method failed');
        showReferenceCopiedNotification(true);
      }
    } catch (error) {
      console.error('❌ Failed to copy reference number with fallback method:', error);
      showReferenceCopiedNotification(true);
    }
  }

  async function copyReference() {
    const ref = rentCalculatorData?.reference || '';
    if (ref) {
      try {
        await navigator.clipboard.writeText(ref);
        console.log('Reference copied to clipboard');
      } catch (error) {
        console.error('Failed to copy reference:', error);
      }
    }
  }

  // Export function to append text to notes (called from StandardResponses)
  export function appendToNotes(text: string) {
    notes = notes ? `${notes}\n\n${text}` : text;
    updateNotes(notes);
  }

  function handleStandardResponse(event: CustomEvent) {
    const { text, title } = event.detail;
    appendToNotes(text); // Insert only the text content, excluding the title/header
    // Focus textarea, position cursor at end, and scroll to bottom
    // Use longer delay to ensure blur handling completes first
    setTimeout(() => {
      if (notesTextarea) {
        notesFocused = true; // Ensure quick responses stay visible
        notesTextarea.focus();
        notesTextarea.setSelectionRange(notesTextarea.value.length, notesTextarea.value.length);
        notesTextarea.scrollTop = notesTextarea.scrollHeight;
      }
    }, 150);
  }

  function handleNotesFocus() {
    notesFocused = true;
  }
  function handleNotesBlur(e: FocusEvent) {
    // Only hide if focus is not moving to a child of the quick responses
    // Use setTimeout to allow click on quick response
    setTimeout(() => {
      const active = document.activeElement as HTMLElement;
      const quick = document.getElementById('quick-responses-bar');
      if (!quick || !quick.contains(active)) {
        notesFocused = false;
      }
    }, 100);
  }

  // For Escape key and close button
  function handleQuickResponsesClose() {
    notesFocused = false;
    // Optionally, return focus to textarea
    const textarea = document.querySelector('.notes-textarea') as HTMLTextAreaElement;
    if (textarea) textarea.blur();
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' && notesFocused) {
      handleQuickResponsesClose();
    }
  }

  // Attach keydown listener when quick responses are open
  $: if (notesFocused) {
    window.addEventListener('keydown', handleKeyDown);
  } else {
    window.removeEventListener('keydown', handleKeyDown);
  }

  function hideNotes() {
    notesVisible = false;
    notesFocused = false; // Also hide quick responses
    // Save to localStorage
    localStorage.setItem('notesVisible', 'false');
  }

  function showNotes() {
    notesVisible = true;
    // Save to localStorage
    localStorage.setItem('notesVisible', 'true');
  }

</script>

<!-- Quick Responses Bar (slide up above notes section) -->
{#if notesFocused && notesVisible && $userPreferences.showQuickResponses}
  <div id="quick-responses-bar" class="quick-responses-bar" class:sidebar-mode={$userPreferences.notesPosition === 'sidebar'} class:vertical-mode={$userPreferences.notesPosition === 'sidebar' && windowWidth > 900} tabindex="-1">
    <StandardResponses 
      notesContent={notes}
      on:responseSelected={handleStandardResponse}
      verticalLayout={$userPreferences.notesPosition === 'sidebar' && windowWidth > 900}
    />
    <button class="close-quick-bar-btn" title="Close quick responses" on:click={handleQuickResponsesClose}>
      <i class="fas fa-times"></i>
    </button>
  </div>
{/if}

<!-- Success Toast Notification -->
{#if showSuccessToast}
  <div class="success-toast">
    <i class="fas fa-check-circle"></i>
    Note saved to Case Log and copied to clipboard
  </div>
{/if}

<!-- Search Toast Notification -->
{#if showSearchToast}
  <div class="search-toast {searchToastType}">
    <i class="fas {searchToastType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
    {searchToastMessage}
  </div>
{/if}

<!-- Reference Copied Toast Notification -->
{#if showBalanceToast}
  <div class="success-toast balance-toast" class:error-toast={referenceToastIsError}>
    <i class="fas {referenceToastIsError ? 'fa-exclamation-triangle' : 'fa-copy'}"></i>
    {referenceToastIsError ? 'Failed to copy reference to clipboard' : 'Reference number copied to clipboard'}
  </div>
{/if}

<!-- Duplicate Note Toast Notification -->
{#if showDuplicateToast}
  <div class="success-toast duplicate-toast">
    <i class="fas fa-info-circle"></i>
    Duplicate note detected - not saved
  </div>
{/if}

<!-- Notes Section -->
{#if notesVisible}
  <div class="notes-section-fixed" class:slide-out={!notesVisible} class:sidebar-mode={$userPreferences.notesPosition === 'sidebar'}>
    <!-- Hide Button -->
    <button class="hide-notes-btn" class:sidebar-mode={$userPreferences.notesPosition === 'sidebar'} title="Hide notes" on:click={hideNotes}>
      <i class="fas fa-{$userPreferences.notesPosition === 'sidebar' ? 'chevron-right' : 'chevron-down'}"></i>
    </button>
    
    <div class="notes-row">
      <div class="notes-left">
        <textarea
          bind:this={notesTextarea}
          bind:value={notes}
          on:input={handleNotesChange}
          on:focus={handleNotesFocus}
          on:blur={handleNotesBlur}
          placeholder="Enter your notes here..."
          rows="6"
          class="notes-textarea"
        ></textarea>
      </div>
      <div class="notes-middle">
        <TalkingPoints 
          notesContent={notes}
          on:pointSelected={(event) => {
            // For now, just highlight the talking point
            // Could potentially copy to clipboard or show in a popup
            console.log('Talking point selected:', event.detail.point);
          }}
        />
      </div>
      <div class="notes-right">
        <div class="reference-name-section form-row">
          <div class="reference-field">
            <label for="reference-input" class="inline-label">Ref #</label>
            <div class="reference-input-container">
              <input
                id="reference-input"
                class="inline-input"
                type="text"
                value={rentCalculatorData?.reference || ''}
                placeholder="Enter reference"
                on:input={handleReferenceInput}
                on:keydown={handleReferenceKeyDown}
              />
              {#if isSearching}
                <div class="search-spinner">
                  <i class="fas fa-spinner fa-spin"></i>
                </div>
              {/if}
            </div>
          </div>
          <div class="name-field">
            <label for="name-input" class="inline-label">Name</label>
            <div class="reference-input-container">
            <input
              id="name-input"
              class="inline-input"
              type="text"
              value={rentCalculatorData?.name || ''}
              placeholder="Enter name (optional)"
              on:input={handleNameInput}
            />
            </div>
          </div>
        </div>
        <div class="tags-section form-row">
          <label class="inline-label">Tags</label>
          <TagInput
            bind:selectedTags 
            on:tagsChanged={handleTagsChanged}
            placeholder="Add tags to categorise this note..."
            dropdownDirection="up"
          />
        </div>
        <div class="notes-actions">
          <Button
            variant="primary"
            size="md"
            icon="fas fa-save"
            disabled={!notes.trim() || isSaving}
            on:click={saveNotes}
          >
            {isSaving ? 'Saving...' : 'Save Case'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            icon="fas fa-trash"
            disabled={!notes.trim()}
            on:click={clearNotes}
          >
            Clear Notes
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Floating Circle Button (when notes are hidden) -->
{#if !notesVisible}
  <button class="floating-notes-btn" class:sidebar-mode={$userPreferences.notesPosition === 'sidebar'} title="Show notes" on:click={showNotes}>
    <i class="fas fa-pencil-alt"></i>
  </button>
{/if}

<style lang="less">
  .notes-section-fixed {
    position: fixed;
    left: 200px;
    bottom: 0;
    width: calc(100% - 200px);
    height: 210px;
    background: #fff;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
    z-index: 120;
    display: flex;
    align-items: stretch;
    padding: 1rem 2rem 1rem 2rem;
    border-top: 2px solid var(--border);
    transition: left 300ms ease-in-out, width 300ms ease-in-out, box-shadow 0.3s, transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideInNotes 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .notes-section-fixed.slide-out {
    transform: translateY(100%);
  }

  /* Sidebar mode styles */
  .notes-section-fixed.sidebar-mode {
    position: fixed;
    top: 70px; /* Space for collapse/hide control */
    right: 20px; /* 20px margin from edge */
    bottom: 0;
    left: auto;
    width: 400px;
    height: auto;
    animation: slideInSidebar 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-top: none;
    border-left: 2px solid var(--border);
    box-shadow: -2px 0 16px rgba(0,0,0,0.08);
    padding: 4rem 1.5rem 1.5rem 1.5rem; /* Extra top padding for hide button */
    z-index: 120;
  }

  .notes-section-fixed.sidebar-mode .notes-row {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 1.5rem;
  }

  .notes-section-fixed.sidebar-mode .notes-textarea {
    flex: 1;
    min-height: 200px;
    height: auto;
    resize: none;
  }

  .notes-section-fixed.sidebar-mode .notes-left {
    order: 1;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .notes-section-fixed.sidebar-mode .notes-middle {
    order: 2;
    max-height: 200px;
    overflow-y: auto;
  }

  .notes-section-fixed.sidebar-mode .notes-right {
    order: 3;
    height: auto;
  }

  .notes-section-fixed.sidebar-mode.slide-out {
    transform: translateX(100%);
  }

  @keyframes slideInSidebar {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }

  /* Sidebar navigation adjustments for bottom mode only */
  :global(.sidebar-collapsed) .notes-section-fixed:not(.sidebar-mode) {
    left: 60px;
    width: calc(100% - 60px);
  }

  /* Header navigation mode - full width for bottom mode only */
  :global(.header-nav-mode) .notes-section-fixed:not(.sidebar-mode) {
    left: 0;
    width: 100%;
  }

  /* Sidebar mode positioning adjustments for different navigation modes */
  :global(.sidebar-collapsed) .notes-section-fixed.sidebar-mode {
    /* Sidebar mode maintains fixed right positioning regardless of navigation mode */
    right: 0px;
  }

  :global(.header-nav-mode) .notes-section-fixed.sidebar-mode {
    /* Sidebar mode maintains fixed right positioning regardless of navigation mode */
    right: 0px;
  }
  .notes-row {
    display: grid;
    grid-template-columns: 1.2fr 0.6fr 1fr;
    width: 100%;
    height: 100%;
    gap: 1rem;
  }

  .reference-name-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    align-items: end;
  }

  .reference-field {
    display: flex;
    flex-direction: column;
  }

  .name-field {
    display: flex;
    flex-direction: column;
  }

  /* Sidebar mode adjustments for reference-name-section */
  .notes-section-fixed.sidebar-mode .reference-name-section {
    display: flex;
    flex-direction: column;
    gap: .75rem;
  }

  .notes-section-fixed.sidebar-mode .reference-field,
  .notes-section-fixed.sidebar-mode .name-field {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
  }

  .notes-section-fixed.sidebar-mode .tags-section {
    flex-direction: row;
    align-items: center;
  }

  .notes-section-fixed.sidebar-mode .inline-label {
    min-width: 50px;
    width: 50px;
    text-align: left;
    flex-shrink: 0;
  }

  .notes-section-fixed.sidebar-mode .reference-input-container {
    flex: 1;
  }

  .notes-left {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .notes-middle {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 177px;
    min-width: 0; /* Prevents overflow in grid */
  }
  .notes-right {
    display: grid;
    grid-template-rows: auto auto auto;
    gap: 0.75rem;
    align-items: start;
    height: 100%;
    min-width: 0; /* Prevents overflow in grid */
  }
  .notes-textarea {
    width: 100%;
    height: 100%;
    // max-height: 177px;
    min-height: 120px;
    resize: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: var(--value-size);
    line-height: 1.6;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: 12px;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  }
  .notes-textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(2, 85, 130, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  .notes-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  .tags-section {
    /* Inherit .form-row horizontal flex layout */
    display: flex;
    align-items: center;
    gap: 0.75rem;
    // margin-bottom: 0.5rem;
  }
  .tags-label {
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--label-size);
  }
  .input-with-button {
    display: flex;
    gap: 0.5rem;
  }
  .quick-responses-bar {
    position: fixed;
    left: 200px;
    bottom: 188px;
    width: calc(100% - 200px);
    background: #fff;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.10), 0 -100px 100px 0 rgba(0,0,0,0.20);
    z-index: 110;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    align-items: flex-end;
    animation: slideUpQuick 0.5s cubic-bezier(0.22, 1, 0.36, 1.08);
    transition: left 300ms ease-in-out, width 300ms ease-in-out, box-shadow 0.3s;
  }


  /* Quick responses bar adjustments for bottom mode only */
  :global(.sidebar-collapsed) .quick-responses-bar:not(.sidebar-mode) {
    left: 60px;
    width: calc(100% - 60px);
  }

  /* Header navigation mode - full width for bottom mode only */
  :global(.header-nav-mode) .quick-responses-bar:not(.sidebar-mode) {
    left: 0;
    width: 100%;
  }

  /* Quick responses sidebar mode positioning adjustments for different navigation modes */
  :global(.sidebar-collapsed) .quick-responses-bar.sidebar-mode {
    /* Sidebar mode maintains fixed right positioning regardless of navigation mode */
    right: 0px;
  }

  :global(.header-nav-mode) .quick-responses-bar.sidebar-mode {
    /* Sidebar mode maintains fixed right positioning regardless of navigation mode */
    right: 0px;
  }

  /* Sidebar mode for quick responses */
  .quick-responses-bar.sidebar-mode {
    position: fixed;
    top: auto;
    bottom: 0;
    right: 20px; /* Match sidebar positioning */
    left: auto;
    width: 400px;
    height: auto;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-left: 2px solid var(--border);
    animation: slideUpSidebarQuick 0.5s cubic-bezier(0.22, 1, 0.36, 1.08);
    z-index: 110;
  }

  /* Vertical mode for quick responses in sidebar */
  .quick-responses-bar.vertical-mode {
    position: fixed;
    top: 70px; /* Align with notes sidebar */
    bottom: 0;
    right: 400px !important; /* Position to the left of notes sidebar (400px + 20px) */
    left: auto;
    width: 300px; /* Tall thin bar */
    height: auto;
    border-radius: 8px 0 0 8px;
    border: 2px solid var(--border);
    border-right: none;
    animation: slideInVerticalQuick 0.4s cubic-bezier(0.22, 1, 0.36, 1.08);
    box-shadow: -2px 0 16px rgba(0,0,0,0.08);
    padding: 1rem;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* Close button adjustment for vertical mode */
  .quick-responses-bar.vertical-mode .close-quick-bar-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  @keyframes slideUpSidebarQuick {
    0% {
      transform: translateY(100%) scaleY(0.98);
      opacity: 0;
    }
    60% {
      transform: translateY(-4px) scaleY(1.015);
      opacity: 1;
    }
    80% {
      transform: translateY(1.5px) scaleY(0.995);
    }
    100% {
      transform: translateY(0) scaleY(1);
      opacity: 1;
    }
  }

  @keyframes slideInVerticalQuick {
    0% {
      transform: translateX(100%) scaleX(0.98);
      opacity: 0;
    }
    60% {
      transform: translateX(-2px) scaleX(1.01);
      opacity: 1;
    }
    80% {
      transform: translateX(1px) scaleX(0.995);
    }
    100% {
      transform: translateX(0) scaleX(1);
      opacity: 1;
    }
  }
  @keyframes slideUpQuick {
    0% {
      transform: translateY(100%) scaleY(0.98);
      opacity: 0;
    }
    60% {
      transform: translateY(-4px) scaleY(1.015);
      opacity: 1;
    }
    80% {
      transform: translateY(1.5px) scaleY(0.995);
    }
    100% {
      transform: translateY(0) scaleY(1);
      opacity: 1;
    }
  }
  /* Responsive for mobile (sidebar 160px) */
  @media (max-width: 768px) {
    /* Force bottom position on mobile regardless of user preference */
    .notes-section-fixed.sidebar-mode {
      position: fixed;
      top: auto;
      bottom: 0;
      left: 160px;
      right: auto;
      width: calc(100% - 160px);
      height: 210px;
      border-left: none;
      border-top: 2px solid var(--border);
      box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
      animation: slideInNotes 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 1rem 0.5rem 0.5rem 0.5rem;
    }

    .notes-section-fixed.sidebar-mode .notes-row {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr auto auto;
      gap: 1rem;
      height: auto;
    }

    .notes-section-fixed.sidebar-mode .notes-textarea {
      min-height: 80px;
      height: 80px;
    }

    .notes-section-fixed.sidebar-mode.slide-out {
      transform: translateY(100%);
    }

    .notes-section-fixed {
      left: 160px;
      width: calc(100% - 160px);
      padding: 1rem 0.5rem 0.5rem 0.5rem;
      min-height: 200px;
      height: auto;
    }

    :global(.sidebar-collapsed) .notes-section-fixed {
      left: 60px;
      width: calc(100% - 60px);
    }

    /* Force bottom position for quick responses on mobile */
    .quick-responses-bar.sidebar-mode {
      position: fixed;
      top: auto;
      bottom: 188px;
      left: 160px;
      right: auto;
      width: calc(100% - 160px);
      border-left: none;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      animation: slideUpQuick 0.5s cubic-bezier(0.22, 1, 0.36, 1.08);
      z-index: 110;
    }

    .quick-responses-bar {
      left: 160px;
      width: calc(100% - 160px);
      padding: 0.25rem 0.5rem 0.25rem 0.5rem;
    }

    :global(.sidebar-collapsed) .quick-responses-bar {
      left: 60px;
      width: calc(100% - 60px);
    }
    .notes-row {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr auto auto;
      gap: 1rem;
      height: auto;
    }
    
    .notes-left {
      order: 1;
    }
    
    .notes-middle {
      order: 2;
      max-height: 150px;
      overflow-y: auto;
    }
    
    .notes-right {
      order: 3;
      grid-template-rows: auto auto auto;
    }
    
    .notes-textarea {
      min-height: 80px;
      height: 80px;
    }

    /* Floating button on mobile */
    .floating-notes-btn.sidebar-mode {
      bottom: 20px;
      top: auto;
      right: 20px;
      transform: none;
      animation: floatIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .floating-notes-btn.sidebar-mode:hover {
      transform: translateY(-2px) scale(1.05);
    }
  }
  .close-quick-bar-btn {
    position: absolute;
    top: -52px;
    right: .5rem;
    z-index: 20;
    background: #fff;
    border: 1px solid var(--border);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #6b7280;
    cursor: pointer;
    transition: background 0.15s, color 0.15s;
  }
  .close-quick-bar-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }
  .form-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    // margin-bottom: 0.5rem;
  }
  .inline-label {
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--label-size);
    margin-bottom: 0;
    white-space: nowrap;
  }
  .inline-input {
    flex: 1;
    font-size: var(--value-size);
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    border-radius: 8px;
    background: #fff;
    transition: border-color 0.2s;
    height: 44px;
    min-width: 0;
    box-sizing: border-box;
  }
  .inline-input:focus {
    border-color: var(--color-primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-light);
  }
  /* Style TagInput like an input using global selector */
  .tags-section :global(.tag-input-root),
  .tags-section :global(input),
  .tags-section :global(.tag-input) {
    flex: 1;
    font-size: var(--value-size);
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    border-radius: 8px;
    background: #fff;
    transition: border-color 0.2s;
    height: 44px;
    min-width: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  /* Ensure TagInput takes full width in sidebar mode */
  .notes-section-fixed.sidebar-mode .tags-section :global(.tag-input-root),
  .notes-section-fixed.sidebar-mode .tags-section :global(.tag-input) {
    width: 100%;
  }
  .tags-section :global(.tag-input-root:focus-within),
  .tags-section :global(input:focus),
  .tags-section :global(.tag-input:focus-within) {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary-light);
  }

  /* Success Toast Notification */
  .success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--color-green);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1000;
    animation: slideInToast 0.3s ease-out;
  }

  .success-toast i {
    font-size: 1rem;
  }

  /* Balance Toast - slightly different positioning and color */
  .balance-toast {
    top: 80px; /* Position below the success toast */
    background: var(--color-primary);
  }

  /* Duplicate Toast */
  .duplicate-toast {
    top: 140px; /* Position below the balance toast */
    background: var(--color-orange);
  }

  /* Error Toast */
  .error-toast {
    background: var(--color-red) !important;
  }

  @keyframes slideInToast {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInNotes {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  /* Hide Notes Button */
  .hide-notes-btn {
    position: absolute;
    top: 2.3rem;
    right: 2rem;
    z-index: 10;
    background: #fff;
    border: 1px solid var(--border);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .hide-notes-btn:hover {
    background: #f3f4f6;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  /* Hide button in sidebar mode */
  .hide-notes-btn.sidebar-mode {
    top: 1rem;
    left: 1.5rem;
    right: auto;
  }

  /* Floating Circle Button */
  .floating-notes-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 130;
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    animation: floatIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-notes-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
  }

  /* Floating button in sidebar mode */
  .floating-notes-btn.sidebar-mode {
    bottom: auto;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    animation: floatInSidebar 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-notes-btn.sidebar-mode:hover {
    transform: translateY(-50%) translateX(-2px) scale(1.05);
  }

  @keyframes floatInSidebar {
    from {
      transform: translateY(-50%) translateX(100px) scale(0.8);
      opacity: 0;
    }
    to {
      transform: translateY(-50%) translateX(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes floatIn {
    from {
      transform: translateY(100px) scale(0.8);
      opacity: 0;
    }
    to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  /* Force bottom mode on screens too narrow for sidebar */
  @media (max-width: 900px) {
    .notes-section-fixed.sidebar-mode {
      position: fixed;
      top: auto;
      bottom: 0;
      left: 160px;
      right: auto;
      width: calc(100% - 160px);
      height: 210px;
      border-left: none;
      border-top: 2px solid var(--border);
      box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
      animation: slideInNotes 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 1rem 2rem 1rem 2rem;
    }

    .notes-section-fixed.sidebar-mode .notes-row {
      grid-template-columns: 1.2fr 0.6fr 1fr;
      grid-template-rows: auto;
      gap: 1rem;
      height: 100%;
    }

    .notes-section-fixed.sidebar-mode .notes-textarea {
      min-height: 120px;
      height: auto;
      max-height: 177px;
    }

    .notes-section-fixed.sidebar-mode .notes-left {
      order: 0;
      flex: none;
      min-height: auto;
    }

    .notes-section-fixed.sidebar-mode .notes-middle {
      order: 0;
      max-height: 177px;
    }

    .notes-section-fixed.sidebar-mode .notes-right {
      order: 0;
      height: 100%;
    }

    .notes-section-fixed.sidebar-mode.slide-out {
      transform: translateY(100%);
    }

    .quick-responses-bar.sidebar-mode {
      position: fixed;
      top: auto;
      bottom: 188px;
      left: 160px;
      right: auto;
      width: calc(100% - 160px);
      border-left: none;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      animation: slideUpQuick 0.5s cubic-bezier(0.22, 1, 0.36, 1.08);
    }

    .floating-notes-btn.sidebar-mode {
      bottom: 20px;
      top: auto;
      right: 20px;
      transform: none;
      animation: floatIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .floating-notes-btn.sidebar-mode:hover {
      transform: translateY(-2px) scale(1.05);
    }
  }

  /* Tablet responsive adjustments */
  @media (max-width: 1024px) and (min-width: 901px) {
    .notes-section-fixed.sidebar-mode {
      width: 300px; /* Smaller sidebar width on tablets */
      padding: 1rem;
    }

    .quick-responses-bar.sidebar-mode {
      width: 300px; /* Match sidebar width */
    }

    .notes-section-fixed.sidebar-mode .notes-textarea {
      min-height: 150px;
    }

    .notes-section-fixed.sidebar-mode .notes-middle {
      max-height: 150px;
    }
  }

  /* Reference input container and search spinner */
  .reference-input-container {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
  }

  .search-spinner {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-primary);
    font-size: 0.875rem;
    pointer-events: none;
  }

  /* Search toast notification */
  .search-toast {
    position: fixed;
    top: 200px;
    right: 2rem;
    z-index: 9999;
    background: var(--color-primary);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideInToast 0.3s ease-out;
    font-size: 0.875rem;
    min-width: 250px;
  }

  .search-toast.error {
    background: var(--color-red);
  }

  .search-toast.success {
    background: var(--color-green);
  }

  .search-toast i {
    font-size: 1rem;
  }
</style> 