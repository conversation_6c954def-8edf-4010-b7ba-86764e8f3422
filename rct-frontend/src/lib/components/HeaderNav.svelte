<script lang="ts">
  import {
    logout,
    currentUser,
    currentTab,
    clearAllData,
    clearCurrentInstance,
    viewMode,
    tabInstances,
    activeTabId,
    createNewTab,
    closeTab,
    switchToTab,
    updateTabName,
    caseDetailTabs,
    activeCaseDetailTabId,
    switchToCaseDetailTab,
    switchToRentCalculatorTab,
    closeCaseDetailTab,
    settingsTabs,
    activeSettingsTabId,
    createSettingsTab,
    closeSettingsTab,
    switchToSettingsTab,
    profileTabs,
    activeProfileTabId,
    createProfileTab,
    closeProfileTab,
    switchToProfileTab,
    analyticsTabs,
    activeAnalyticsTabId,
    createAnalyticsTab,
    closeAnalyticsTab,
    switchToAnalyticsTab,
    allTabs,
    isLoading,
    errorMessage,
    isAuthenticated,
    isSuperuser,
    tabSaveStatus
  } from '$lib/stores';
  import { userPreferences, updateUserPreferences } from '$lib/stores/userPreferences';
  import { createEventDispatcher, onMount } from 'svelte';
  import Button from './Button.svelte';
  import Modal from './Modal.svelte';
  
  export let devModeEnabled: boolean = false;
  
  let showUserDropdown = false;
  let showViewOptionsDropdown = false;
  
  // Tab editing state
  let editingTabId: string | null = null;
  
  // Tab close confirmation state
  let showCloseConfirmation = false;
  let tabToClose: string | null = null;
  let tabTypeToClose: 'rent-calculator' | 'case-detail' | 'settings' | 'profile' | 'analytics' | null = null;
  let editingTabName = '';
  
  // Tab scroll state
  let tabsScrollContainer: HTMLElement;
  let canScrollLeft = false;
  let canScrollRight = false;
  
  // Check if we should show scroll arrows (5 or more tabs)
  $: shouldShowScrollArrows = $allTabs.length >= 5;
  
  const dispatch = createEventDispatcher();
  
  // Ensure tabs exist when component mounts
  onMount(() => {
    // Use reactive statement to handle tab creation without setTimeout
    const unsubscribe = tabInstances.subscribe(instances => {
      if (instances.length === 0 && $isAuthenticated) {
        console.log('HeaderNav: No tabs found after authentication, creating default tab');
        handleCreateNewTab();
      }
    });
    
    // Cleanup subscription
    return unsubscribe;
  });
  
  function handleExit() {
    if (confirm('Are you sure you want to exit? Any unsaved changes will be lost.')) {
      logout();
    }
  }
  
  
  function handleOpenCaseLog() {
    dispatch('caseLog');
  }


  // Tab management handlers
  function handleCreateNewTab() {
    createNewTab();
    scrollToEnd();
  }
  
  function handleTabClick(tabId: string) {
    if (editingTabId === tabId) return; // Don't switch if editing
    switchToTab(tabId);
  }

  function handleTabClose(event: Event, tabId: string) {
    event.stopPropagation(); // Prevent tab switching
    const tabStatus = $tabSaveStatus.get(tabId);
    const hasUnsavedChanges = tabStatus && !tabStatus.saved;
    
    if ($userPreferences.confirmTabClose && hasUnsavedChanges) {
      showTabCloseConfirmation(tabId, 'rent-calculator');
    } else {
      closeTab(tabId);
    }
  }

  // Rent Calculator Tab Handlers
  function handleRentCalculatorTabClick(tabId: string) {
    if (editingTabId === tabId) return; // Don't switch if editing
    switchToRentCalculatorTab(tabId);
  }

  function handleRentCalculatorTabClose(event: Event, tabId: string) {
    event.stopPropagation(); // Prevent tab switching
    const tabStatus = $tabSaveStatus.get(tabId);
    const hasUnsavedChanges = tabStatus && !tabStatus.saved;
    
    if ($userPreferences.confirmTabClose && hasUnsavedChanges) {
      showTabCloseConfirmation(tabId, 'rent-calculator');
    } else {
      closeTab(tabId);
    }
  }

  // Case Detail Tab Handlers
  function handleCaseDetailTabClick(tabId: string) {
    switchToCaseDetailTab(tabId);
  }

  function handleCaseDetailTabClose(event: Event, tabId: string) {
    event.stopPropagation(); // Prevent tab switching
    const tabStatus = $tabSaveStatus.get(tabId);
    const hasUnsavedChanges = tabStatus && !tabStatus.saved;
    
    if ($userPreferences.confirmTabClose && hasUnsavedChanges) {
      showTabCloseConfirmation(tabId, 'case-detail');
    } else {
      closeCaseDetailTab(tabId);
    }
  }

  // Settings Tab Handlers
  function handleSettingsTabClick(tabId: string) {
    switchToSettingsTab(tabId);
  }

  function handleSettingsTabClose(event: Event, tabId: string) {
    event.stopPropagation(); // Prevent tab switching
    const tabStatus = $tabSaveStatus.get(tabId);
    // If no save status exists, consider the tab saved (settings tabs don't typically have unsaved changes)
    const hasUnsavedChanges = tabStatus ? !tabStatus.saved : false;
    
    if ($userPreferences.confirmTabClose && hasUnsavedChanges) {
      showTabCloseConfirmation(tabId, 'settings');
    } else {
      closeSettingsTab(tabId);
    }
  }

  // Profile Tab Handlers
  function handleProfileTabClick(tabId: string) {
    switchToProfileTab(tabId);
  }

  function handleProfileTabClose(event: Event, tabId: string) {
    event.stopPropagation(); // Prevent tab switching
    const tabStatus = $tabSaveStatus.get(tabId);
    const hasUnsavedChanges = tabStatus && !tabStatus.saved;
    
    if ($userPreferences.confirmTabClose && hasUnsavedChanges) {
      showTabCloseConfirmation(tabId, 'profile');
    } else {
      closeProfileTab(tabId);
    }
  }

  // Analytics Tab Handlers
  function handleAnalyticsTabClick(tabId: string) {
    switchToAnalyticsTab(tabId);
  }

  function handleAnalyticsTabClose(event: Event, tabId: string) {
    event.stopPropagation(); // Prevent tab switching
    const tabStatus = $tabSaveStatus.get(tabId);
    const hasUnsavedChanges = tabStatus && !tabStatus.saved;
    
    if ($userPreferences.confirmTabClose && hasUnsavedChanges) {
      showTabCloseConfirmation(tabId, 'analytics');
    } else {
      closeAnalyticsTab(tabId);
    }
  }

  // Tab close confirmation functions
  function showTabCloseConfirmation(tabId: string, tabType: 'rent-calculator' | 'case-detail' | 'settings' | 'profile' | 'analytics') {
    tabToClose = tabId;
    tabTypeToClose = tabType;
    showCloseConfirmation = true;
  }

  function confirmTabClose() {
    if (tabToClose && tabTypeToClose) {
      switch (tabTypeToClose) {
        case 'rent-calculator':
          closeTab(tabToClose);
          break;
        case 'case-detail':
          closeCaseDetailTab(tabToClose);
          break;
        case 'settings':
          closeSettingsTab(tabToClose);
          break;
        case 'profile':
          closeProfileTab(tabToClose);
          break;
        case 'analytics':
          closeAnalyticsTab(tabToClose);
          break;
      }
    }
    cancelTabClose();
  }

  function cancelTabClose() {
    showCloseConfirmation = false;
    tabToClose = null;
    tabTypeToClose = null;
  }
  
  function startEditingTabName(event: Event, tabId: string, currentName: string) {
    event.stopPropagation();
    editingTabId = tabId;
    editingTabName = currentName;
  }
  
  function finishEditingTabName() {
    if (editingTabId && editingTabName.trim()) {
      updateTabName(editingTabId, editingTabName.trim());
    }
    editingTabId = null;
    editingTabName = '';
  }
  
  function cancelEditingTabName() {
    editingTabId = null;
    editingTabName = '';
  }
  
  function handleTabNameKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      finishEditingTabName();
    } else if (event.key === 'Escape') {
      cancelEditingTabName();
    }
  }
  
  
  // Helper function to format tab creation date/time
  function formatTabDateTime(createdAt: Date): string {
    const now = new Date();
    const created = new Date(createdAt);
    
    // Check if it's today
    const isToday = now.toDateString() === created.toDateString();
    
    if (isToday) {
      // Only show time if it's today
      return created.toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false
      });
    } else {
      // Show date and time if it's not today
      return created.toLocaleDateString('en-GB', { 
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      }) + ' ' + created.toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false
      });
    }
  }

  function handleSettings() {
    createSettingsTab();
    scrollToEnd();
  }

  function handleAnalytics() {
    createAnalyticsTab();
    scrollToEnd();
  }
  
  function handleClearAll() {
    if (confirm('Are you sure you want to clear all data in all tabs? This action cannot be undone.')) {
      clearAllData();
    }
  }
  
  
  function toggleUserDropdown() {
    showUserDropdown = !showUserDropdown;
    showViewOptionsDropdown = false; // Close other dropdown
  }

  function toggleViewOptionsDropdown() {
    showViewOptionsDropdown = !showViewOptionsDropdown;
    showUserDropdown = false; // Close other dropdown
  }
  
  function toggleViewMode() {
    const newMode = $viewMode === 'compact' ? 'full' : 'compact';
    viewMode.set(newMode);
  }

  function toggleDevMode() {
    dispatch('devMode');
  }


  function toggleNotesPosition() {
    const newPosition = $userPreferences.notesPosition === 'bottom' ? 'sidebar' : 'bottom';
    updateUserPreferences({ notesPosition: newPosition });
  }

  
  function handleOutsideClick(event: MouseEvent) {
    const target = event.target as Element;
    if (showUserDropdown && !target.closest('.user-profile')) {
      showUserDropdown = false;
    }
    if (showViewOptionsDropdown && !target.closest('.view-options-container')) {
      showViewOptionsDropdown = false;
    }
  }
  
  // Tab scroll functions
  function updateScrollArrows() {
    if (!tabsScrollContainer) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = tabsScrollContainer;
    canScrollLeft = scrollLeft > 0;
    canScrollRight = scrollLeft < scrollWidth - clientWidth - 1;
  }
  
  function scrollTabsLeft() {
    if (!tabsScrollContainer || !canScrollLeft) return;
    tabsScrollContainer.scrollBy({ left: -200, behavior: 'smooth' });
  }
  
  function scrollTabsRight() {
    if (!tabsScrollContainer || !canScrollRight) return;
    tabsScrollContainer.scrollBy({ left: 200, behavior: 'smooth' });
  }
  
  function initTabScrollArrows(element: HTMLElement) {
    tabsScrollContainer = element;
    updateScrollArrows();
    
    element.addEventListener('scroll', updateScrollArrows);
    
    const resizeObserver = new ResizeObserver(updateScrollArrows);
    resizeObserver.observe(element);
    
    return {
      destroy() {
        element.removeEventListener('scroll', updateScrollArrows);
        resizeObserver.disconnect();
      }
    };
  }
  
  function scrollToEnd() {
    if (!tabsScrollContainer) return;
    setTimeout(() => {
      tabsScrollContainer.scrollTo({ left: tabsScrollContainer.scrollWidth, behavior: 'smooth' });
    }, 100);
  }
</script>

<svelte:window on:click={handleOutsideClick} />

<header class="header-nav">
  <div class="header-content">
    <!-- Left section: Logo, Tabs, and Clear Buttons -->
    <div class="header-left">
      <!-- <img src="/Rent-Collection-Toolkit-Logo-White.svg" alt="Rent Collection Toolkit Logo" class="header-logo" on:click={() => window.location.href = '/app'} role="button" tabindex="0"> -->
      <img src="/RCT-Long.svg" alt="Rent Collection Toolkit Logo" class="header-logo" on:click={() => window.location.href = '/app'} role="button" tabindex="0">
      
      <!-- Tab Bar -->
      <div class="tab-bar">
        <div class="tabs-scroll-container" use:initTabScrollArrows>
          <div class="tabs-container">
            <!-- Rent Calculator Tabs -->
            {#each $tabInstances as tab (tab.id)}
              <div class="tab rent-calculator-tab"
                   class:active={$activeTabId === tab.id}
                   on:click={() => handleRentCalculatorTabClick(tab.id)}>

                <div class="tab-content">
                  {#if editingTabId === tab.id}
                    <input
                      class="tab-name-input"
                      bind:value={editingTabName}
                      on:keydown={handleTabNameKeydown}
                      on:blur={finishEditingTabName}
                      autofocus
                    />
                  {:else}
                    <div class="tab-info-container">
                      <span class="tab-name"
                            on:dblclick={(e) => startEditingTabName(e, tab.id, tab.name)}>
                        <i class="fas fa-calculator tab-icon-inline"></i>
                        {tab.name}
                      </span>
                      <span class="tab-datetime">
                        {formatTabDateTime(tab.createdAt)}
                      </span>
                    </div>
                  {/if}
                </div>

                {#if $tabInstances.length > 1 || ($allTabs.length > $tabInstances.length)}
                  <button class="tab-close"
                          on:click={(e) => handleRentCalculatorTabClose(e, tab.id)}
                          title="Close tab">
                    <i class="fas fa-times"></i>
                  </button>
                {/if}
              </div>
            {/each}

            <!-- Case Detail Tabs -->
            {#each $caseDetailTabs as tab (tab.id)}
              <div class="tab case-detail-tab"
                   class:active={$activeCaseDetailTabId === tab.id}
                   on:click={() => handleCaseDetailTabClick(tab.id)}>

                <div class="tab-content">
                  <div class="tab-info-container">
                    <span class="tab-name">
                      <i class="fas fa-folder-open tab-icon-inline"></i>
                      {tab.name}
                    </span>
                    <span class="tab-datetime">
                      {formatTabDateTime(tab.createdAt)}
                    </span>
                  </div>
                </div>

                {#if $allTabs.length > 1}
                  <button class="tab-close"
                          on:click={(e) => handleCaseDetailTabClose(e, tab.id)}
                          title="Close case detail tab">
                    <i class="fas fa-times"></i>
                  </button>
                {/if}
              </div>
            {/each}

            <!-- Settings Tabs -->
            {#each $settingsTabs as tab (tab.id)}
              <div class="tab settings-tab"
                   class:active={$activeSettingsTabId === tab.id}
                   on:click={() => handleSettingsTabClick(tab.id)}>

                <div class="tab-content">
                  <div class="tab-info-container">
                    <span class="tab-name">
                      <i class="fas fa-tags tab-icon-inline"></i>
                      {tab.name}
                    </span>
                    <span class="tab-datetime">
                      {formatTabDateTime(tab.createdAt)}
                    </span>
                  </div>
                </div>

                {#if $allTabs.length > 1}
                  <button class="tab-close"
                          on:click={(e) => handleSettingsTabClose(e, tab.id)}
                          title="Close settings tab">
                    <i class="fas fa-times"></i>
                  </button>
                {/if}
              </div>
            {/each}

            <!-- Profile Tabs -->
            {#each $profileTabs as tab (tab.id)}
              <div class="tab profile-tab"
                   class:active={$activeProfileTabId === tab.id}
                   on:click={() => handleProfileTabClick(tab.id)}>

                <div class="tab-content">
                  <div class="tab-info-container">
                    <span class="tab-name">
                      <i class="fas fa-user-cog tab-icon-inline"></i>
                      {tab.name}
                    </span>
                    <span class="tab-datetime">
                      {formatTabDateTime(tab.createdAt)}
                    </span>
                  </div>
                </div>

                {#if $allTabs.length > 1}
                  <button class="tab-close"
                          on:click={(e) => handleProfileTabClose(e, tab.id)}
                          title="Close profile tab">
                    <i class="fas fa-times"></i>
                  </button>
                {/if}
              </div>
            {/each}

            <!-- Analytics Tabs -->
            {#each $analyticsTabs as tab (tab.id)}
              <div class="tab analytics-tab"
                   class:active={$activeAnalyticsTabId === tab.id}
                   on:click={() => handleAnalyticsTabClick(tab.id)}>

                <div class="tab-content">
                  <div class="tab-info-container">
                    <span class="tab-name">
                      <i class="fas fa-chart-bar tab-icon-inline"></i>
                      {tab.name}
                    </span>
                    <span class="tab-datetime">
                      {formatTabDateTime(tab.createdAt)}
                    </span>
                  </div>
                </div>

                {#if $allTabs.length > 1}
                  <button class="tab-close"
                          on:click={(e) => handleAnalyticsTabClose(e, tab.id)}
                          title="Close analytics tab">
                    <i class="fas fa-times"></i>
                  </button>
                {/if}
              </div>
            {/each}
          </div>
        </div>
        
        <!-- Scroll arrows -->
        {#if shouldShowScrollArrows && canScrollLeft}
          <button 
            class="tab-scroll-arrow tab-scroll-arrow-left"
            on:click={scrollTabsLeft}
            title="Scroll tabs left"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
        {/if}
        
        {#if shouldShowScrollArrows && canScrollRight}
          <button 
            class="tab-scroll-arrow tab-scroll-arrow-right"
            on:click={scrollTabsRight}
            title="Scroll tabs right"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        {/if}
        
        <button class="new-tab-btn" 
                on:click={handleCreateNewTab}
                title="Create new tab">
          <i class="fas fa-plus"></i>
        </button>
      </div>

      <!-- Clear buttons next to tabs -->
      <!-- <div class="clear-buttons">
        <Button 
          variant="ghost-inverted"
          size="sm"
          className="header-nav-button header-nav-danger clear-button"
          icon="fas fa-trash-alt"
          on:click={handleClearAll}
        >
          Clear All
        </Button>
      </div> -->
    </div>


    <!-- Right section: Navigation and User -->
    <div class="header-right">
      <!-- Case Log button -->
      <Button 
        variant="ghost-inverted"
        size="sm"
        className="header-nav-button"
        icon="fas fa-folder-open"
        on:click={handleOpenCaseLog}
      >
        Case Log
      </Button>

      <!-- Options Dropdown -->
      <div class="view-options-container">
        <Button 
          variant="ghost-inverted"
          size="sm"
          className="header-nav-button view-options-button"
          icon="fas fa-cog"
          on:click={toggleViewOptionsDropdown}
        >
          Options
        </Button>
        
        {#if showViewOptionsDropdown}
          <div class="view-options-dropdown">
            <button class="dropdown-item" on:click={() => { handleAnalytics(); showViewOptionsDropdown = false; }}>
              <i class="fas fa-chart-bar"></i>
              Portfolio Analytics
            </button>
            <button class="dropdown-item" on:click={() => { handleSettings(); showViewOptionsDropdown = false; }}>
              <i class="fas fa-tags"></i>
              Tags & Templates
            </button>
            <button class="dropdown-item" on:click={() => { toggleViewMode(); showViewOptionsDropdown = false; }}>
              <i class="fas fa-{$viewMode === 'compact' ? 'expand' : 'compress'}-alt"></i>
              {$viewMode === 'compact' ? 'Full View' : 'Tab View'}
            </button>
            <button class="dropdown-item" on:click={() => { toggleNotesPosition(); showViewOptionsDropdown = false; }}>
              <i class="fas fa-{$userPreferences.notesPosition === 'bottom' ? 'arrow-down' : 'sidebar'}"></i>
              Notes Position: {$userPreferences.notesPosition === 'bottom' ? 'Bottom' : 'Sidebar'}
            </button>
            <button class="dropdown-item dev-mode-item {devModeEnabled ? 'active' : ''}" on:click={() => { toggleDevMode(); showViewOptionsDropdown = false; }}>
              <i class="fas fa-code"></i>
              Dev Mode {devModeEnabled ? '(On)' : '(Off)'}
            </button>
            {#if devModeEnabled}
              <hr class="dropdown-divider" />
              <button class="dropdown-item disabled" on:click={() => { showViewOptionsDropdown = false; }}>
                <i class="fas fa-database"></i>
                Generate Sample Analytics Data (Disabled)
              </button>
            {/if}
          </div>
        {/if}
      </div>

      <!-- User Profile -->
      {#if $currentUser}
        <div class="user-profile">
          <img src="/lambeth-council-logo.svg" alt="Lambeth Council" class="council-emblem-small">
          <button class="user-avatar" on:click={toggleUserDropdown} aria-label="Toggle user menu">
            {$currentUser.email.charAt(0).toUpperCase()}{$currentUser.email.split('@')[0].slice(-1).toUpperCase()}
          </button>
          
          {#if showUserDropdown}
            <div class="user-dropdown">
              <div class="user-info">
                <span class="user-name">{$currentUser.email}</span>
                <span class="user-org">{$currentUser.organisationName}</span>
              </div>
              <hr class="dropdown-divider" />
              <button class="dropdown-item profile-link" on:click={() => { createProfileTab(); scrollToEnd(); showUserDropdown = false; }}>
                <i class="fas fa-user-cog"></i>
                Profile Settings
              </button>
              {#if $isSuperuser}
                <button class="dropdown-item admin-link" on:click={() => { window.location.href = '/admin'; showUserDropdown = false; }}>
                  <i class="fas fa-users-cog"></i>
                  Admin Panel
                </button>
              {/if}
              <hr class="dropdown-divider" />
              <button class="dropdown-item logout-btn" on:click={handleExit}>Logout</button>
            </div>
          {/if}
        </div>
      {/if}
    </div>
  </div>
</header>

<!-- Tab Close Confirmation Modal -->
<Modal 
  bind:isOpen={showCloseConfirmation}
  title="Confirm Tab Close"
  size="small"
>
  <div slot="icon">
    <i class="fas fa-exclamation-triangle" style="color: var(--color-orange); margin-right: 0.5rem;"></i>
  </div>
  
  <p style="margin-bottom: 1.5rem;">
    Are you sure you want to close this tab? Any unsaved changes will be lost.
  </p>
  
  <div style="display: flex; gap: 0.75rem; justify-content: flex-end;">
    <Button variant="secondary" on:click={cancelTabClose}>
      Cancel
    </Button>
    <Button variant="primary" on:click={confirmTabClose}>
      Close Tab
    </Button>
  </div>
</Modal>

<style lang="less">
  .header-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background-color: var(--color-primary);
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.1);
    z-index: 1000;
    color: #ffffff;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .header-logo {
    height: 40px;
    max-width: 300px;
    width: auto;
    cursor: pointer;
    transition: opacity 150ms ease-in-out;
  }

  .header-logo:hover {
    opacity: 0.8;
  }


  .clear-buttons {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
  }

  :global(.clear-button) {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    border-color: transparent !important;
    opacity: 0.8;
    transition: opacity 150ms ease-in-out;
  }

  :global(.clear-button:hover) {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    border-color: transparent !important;
    opacity: 1;
  }

  :global(.header-nav-button.clear-button) {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    border-color: transparent !important;
  }

  :global(.header-nav-button.clear-button:hover) {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    border-color: transparent !important;
  }


  .header-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  :global(.header-nav-button) {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  :global(.header-nav-button:hover) {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
  }

  :global(.header-nav-danger) {
    background-color: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
  }

  :global(.header-nav-danger:hover) {
    background-color: rgba(239, 68, 68, 0.3) !important;
    border-color: rgba(239, 68, 68, 0.4) !important;
  }

  .user-profile {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.75rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 150ms ease-in-out;
    border: 2px solid rgba(255, 255, 255, 0.3);
    flex-shrink: 0;
  }

  .user-avatar:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }

  .council-emblem-small {
    height: 20px;
    width: auto;
    fill: white;
    opacity: 0.8;
    flex-shrink: 0;
  }

  .user-dropdown {
    position: absolute;
    top: 60px;
    right: 0;
    width: 300px;
    background-color: #ffffff;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    animation: slideIn 150ms ease-in-out;
    z-index: 1001;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .user-info {
    padding: 1rem;
    border-bottom: 1px solid var(--border);
  }

  .user-name {
    display: block;
    font-weight: 600;
    color: #111827;
    font-size: 0.875rem;
    word-break: break-all;
    overflow-wrap: break-word;
  }

  .user-org {
    display: block;
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    text-align: left;
    background: none;
    border: none;
    color: #374151;
    transition: background-color 150ms ease-in-out;
    font-size: 0.875rem;
  }

  .dropdown-item:hover {
    background-color: #f9fafb;
  }

  .profile-link {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0.5rem;
    text-decoration: none;
    color: #374151;
    cursor: pointer;
  }

  .profile-link:hover {
    background-color: #f9fafb;
    color: var(--color-primary);
  }

  .profile-link i {
    color: var(--color-primary);
    width: 16px;
  }

  .logout-btn {
    color: #dc2626;
    font-weight: 500;
  }

  .logout-btn:hover {
    background-color: #fef2f2;
  }

  .dropdown-divider {
    margin: 0;
    border: none;
    border-top: 1px solid var(--border);
  }

  /* View Options Dropdown */
  .view-options-container {
    position: relative;
  }

  .view-options-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    width: 200px;
    background-color: #ffffff;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    animation: slideIn 150ms ease-in-out;
    z-index: 1001;
  }

  .view-options-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    text-align: left;
    background: none;
    border: none;
    color: #374151;
    transition: background-color 150ms ease-in-out;
    font-size: var(--label-size);
    cursor: pointer;
    justify-content: flex-start;
  }

  .view-options-dropdown .dropdown-item:first-child {
    border-radius: 0.5rem 0.5rem 0 0;
  }

  .view-options-dropdown .dropdown-item:last-child {
    border-radius: 0 0 0.5rem 0.5rem;
  }

  .view-options-dropdown .dropdown-item:hover {
    background-color: #f9fafb;
  }

  .view-options-dropdown .dropdown-item i {
    width: 16px;
    color: #6b7280;
  }

  .view-options-dropdown .dev-mode-item.active {
    color: #8b5cf6;
    font-weight: 500;
  }

  .view-options-dropdown .dev-mode-item.active i {
    color: #8b5cf6;
  }

  /* Tab Bar Styles */
  .tab-bar {
    display: flex;
    align-items: center;
    margin-left: 1rem;
    margin-right: 1rem;
    gap: 0.5rem;
    position: relative;
  }

  .tabs-scroll-container {
    flex: 1;
    overflow-x: auto;
    scrollbar-width: none;
    max-width: 700px;
    position: relative;
  }

  .tabs-scroll-container::-webkit-scrollbar {
    display: none;
  }

  .tabs-container {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    min-height: 40px;
    width: max-content;
  }

  .tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.4rem 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 100px;
    max-width: 180px;
    position: relative;
    font-size: 0.8rem;
  }

  /* Tab Type Specific Styles */
  .tab.rent-calculator-tab {
    border-left: 3px solid #3b82f6;
  }

  .tab.case-detail-tab {
    border-left: 3px solid #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    color: white;
  }

  .tab.case-detail-tab:hover {
    background: rgba(245, 158, 11, 0.2);
  }

  .tab.case-detail-tab.active {
    // background: rgba(245, 158, 11, 0.3);
    background: #f59e0b;
    border-color: #f59e0b;
    .tab-info-container {
      color: white;
      .tab-icon-inline {
        color: white;
      }

    }
  }

  /* Settings Tab Styles */
  .tab.settings-tab {
    border-left: 3px solid #10b981;
    background: rgba(16, 185, 129, 0.1);
  }

  .tab.settings-tab:hover {
    background: rgba(16, 185, 129, 0.2);
  }

  .tab.settings-tab.active {
    background: #10b981;
    border-color: #10b981;
    .tab-info-container {
      color: white;
      .tab-icon-inline {
        color: white;
      }
    }
  }

  /* Profile Tab Styles */
  .tab.profile-tab {
    border-left: 3px solid #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
  }

  .tab.profile-tab:hover {
    background: rgba(139, 92, 246, 0.2);
  }

  .tab.profile-tab.active {
    background: #8b5cf6;
    border-color: #8b5cf6;
    .tab-info-container {
      color: white;
      .tab-icon-inline {
        color: white;
      }
    }
  }

  .tab-icon-inline {
    font-size: 0.75rem;
    margin-right: 0.1rem;
    opacity: 0.8;
  }

  .case-detail-tab .tab-icon-inline {
    color: #f59e0b;
  }

  .tab-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
  }

  .tab-info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    min-width: 0;
  }

  .tab:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
  }

  .tab.active {
    background: white;
    color: var(--color-primary);
    border-color: white;
    font-weight: 600;
  }

  .tab-name {
    font-weight: 600;
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none;
    width: 100%;
    line-height: 1.2;
  }

  .tab-datetime {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: 400;
    margin-top: 2px;
    line-height: 1;
  }

  .tab-name-input {
    background: transparent;
    border: none;
    color: inherit;
    font-size: 0.875rem;
    font-weight: 600;
    outline: none;
    padding: 0;
    width: 100%;
  }

  .tab.active .tab-name-input {
    color: var(--color-primary);
  }

  .tab-close {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    max-width: 20px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.1);
    border: none !important;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    flex-shrink: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }

  .tab-close:hover {
    background: rgba(239, 68, 68, 0.8);
    color: white;
  }

  .tab.active .tab-close {
    background: rgba(107, 114, 128, 0.1);
    color: var(--color-primary);
  }

  .tab.active .tab-close:hover {
    background: rgba(239, 68, 68, 0.1);
    color: rgba(239, 68, 68, 1);
  }

  .new-tab-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    flex-shrink: 0;
  }

  .new-tab-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    transform: scale(1.05);
  }
  
  /* Tab scroll arrows */
  .tab-scroll-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
    z-index: 10;
    padding: 0;
    margin: 0;
    flex-shrink: 0;
  }
  
  .tab-scroll-arrow:hover:not(.disabled) {
    background: white;
    color: var(--color-primary);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  
  
  .tab-scroll-arrow-left {
    left: 8px;
  }
  
  .tab-scroll-arrow-right {
    right: 48px; /* Account for new-tab-btn width + gap */
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .header-content {
      padding: 0 1rem;
    }

    .header-right {
      gap: 0.5rem;
    }

    :global(.header-nav-button) {
      padding: 0.4rem 0.5rem !important;
      font-size: 0.6rem !important;
    }

    /* Hide button text on smaller screens, show only icons */
    :global(.header-nav-button .button-text) {
      display: none;
    }

    .tab-bar {
      padding: 0 1rem;
    }

    .tab {
      min-width: 100px;
      max-width: 150px;
      padding: 0.4rem 0.75rem;
      font-size: 0.8rem;
    }

    .tab-name {
      font-size: 0.75rem;
    }

    .tab-datetime {
      font-size: 0.65rem;
    }

    .new-tab-btn {
      width: 32px;
      height: 32px;
      font-size: 0.8rem;
    }
  }

  @media (max-width: 768px) {
    .header-left {
      gap: 0.5rem;
    }

    .header-logo {
      height: 30px;
    }

    .instance-button {
      padding: 0.4rem 0.6rem;
      font-size: 0.75rem;
    }

    .clear-buttons {
      margin-left: 0.5rem;
      gap: 0.25rem;
    }

    .user-dropdown {
      right: -50px;
    }

    .tab {
      min-width: 80px;
      max-width: 120px;
      padding: 0.375rem 0.5rem;
    }

    .tab-close {
      width: 18px;
      height: 18px;
      font-size: 0.65rem;
    }
  }
</style>