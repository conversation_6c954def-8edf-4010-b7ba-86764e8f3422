<script lang="ts">
  export let id: string;
  export let label: string;
  export let value: string | number;
  export let placeholder: string = '';
  export let type: string = 'text';
  export let showCurrency: boolean = false;
  export let showFormula: boolean = false; // Enable formula calculation display
  export let disabled: boolean = false;
  export let step: string = '0.01';  // Default to 2 decimal places for numeric inputs
  export let hideLabel: boolean = false;
  export let isCompact: boolean = false;
  
  // Event dispatcher for input changes
  import { createEventDispatcher } from 'svelte';
  const dispatch = createEventDispatcher();
  
  // Track focus state to hide formula total when focused
  let isFocused = false;
  
  // Format value for display based on currency and disabled state
  $: displayValue = (() => {
    if (showCurrency && typeof value === 'number') {
      if (disabled) {
        // For readonly currency fields, format with commas and 2 decimal places
        return new Intl.NumberFormat('en-GB', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(value);
      } else if (type === 'number') {
        // For editable currency fields, just ensure 2 decimal places
        return parseFloat(value.toFixed(2));
      }
    }
    return value;
  })();
  
  // Math evaluation function for formulas
  function evaluateMathExpression(value: string): number {
    // Remove leading/trailing spaces and commas
    const cleaned = value.toString().trim().replace(/,/g, '');
    
    // If empty, return 0
    if (!cleaned) return 0;
    
    // If it's just a number, parse it directly
    if (/^\d*\.?\d+$/.test(cleaned)) {
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    // Try to evaluate as a mathematical expression
    try {
      // Only allow safe mathematical expressions (numbers, +, -, *, /, ., spaces, parentheses)
      if (!/^[\d+\-*/().\s]+$/.test(cleaned)) {
        // If it contains invalid characters, try parsing as regular number
        const parsed = parseFloat(cleaned);
        return isNaN(parsed) ? 0 : parsed;
      }
      
      // Use Function constructor to safely evaluate mathematical expressions
      const result = new Function('return ' + cleaned)();
      return isNaN(result) ? 0 : result;
    } catch (error) {
      // If evaluation fails, try parsing as regular number
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
  }

  // Check if value contains operators (making it a formula)
  $: hasOperators = showFormula && value && /[+\-*/]/.test(value.toString());
  
  // Calculate formula result
  $: formulaResult = hasOperators ? evaluateMathExpression(value.toString()) : 0;
  
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    dispatch('input', {
      id,
      value: target.value,
      event
    });
  }
  
  function handleFocus(event: FocusEvent) {
    isFocused = true;
    
    // Clear the field if it's a numeric input with value of 0
    const target = event.target as HTMLInputElement;
    if (type === 'number' && (value === 0 || value === '0')) {
      target.value = '';
    }
  }
  
  function handleBlur() {
    isFocused = false;
  }
</script>

<div class="form-group" class:has-formula={showFormula}>
  {#if !hideLabel}
    <label for={id}>{label}</label>
  {/if}
  {#if showCurrency}
    <div class="currency-input">
      <span class="currency-symbol">£</span>
      <input
        {id}
        {type}
        value={displayValue}
        {placeholder}
        {disabled}
        step={showCurrency && type === 'number' ? '0.01' : (type === 'number' ? step : undefined)}
        class:compact={isCompact}
        on:input={handleInput}
        on:focus={handleFocus}
        on:blur={handleBlur}
      />
      {#if hasOperators && !isFocused}
        <div class="formula-total">
          {new Intl.NumberFormat('en-GB', { style: 'currency', currency: 'GBP' }).format(formulaResult)}
        </div>
      {/if}
    </div>
  {:else}
    <input
      {id}
      {type}
      {value}
      {placeholder}
      {disabled}
      step={type === 'number' ? step : undefined}
      class:compact={isCompact}
      on:input={handleInput}
      on:focus={handleFocus}
      on:blur={handleBlur}
    />
    {#if hasOperators && showFormula && !isFocused}
      <div class="formula-total">
        {formulaResult}
      </div>
    {/if}
  {/if}
</div>

<style>
  .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    label {
      font-weight: var(--label-weight);
      color: var(--label-color);
      font-size: var(--label-size);
      &:empty {
        display: none;
      }
    }
    input:not([readonly]) {
      border-color: var(--color-primary);
      &:hover {
        border-color: var(--color-secondary);
      }
    }
  }

  /* Currency Input Styling */
  .currency-input {
    position: relative;
    display: flex;
    align-items: center;
  }

  .currency-symbol {
    position: absolute;
    left: 0.75rem;
    font-weight: var(--label-weight);
    color: var(--label-color);
    font-size: var(--value-size);
    z-index: 1;
    pointer-events: none;
  }

  .currency-input input {
    padding-left: 2rem !important;
    flex: 1;
    width: 100px;
  }

  /* Formula result overlay */
  .form-group.has-formula {
    position: relative;
  }

  .formula-total {
    position: absolute;
    top: .25rem; /* Align with input field */
    right: 0.25rem;
    display: flex;
    align-items: center;
    padding: calc(var(--cell-padding) * .9);
    background: rgba(248, 250, 252, 0.95);
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: var(--value-size);
    font-weight: 600;
    color: var(--color-primary);
    font-family: 'Martian Mono', 'Courier New', monospace;
    white-space: nowrap;
    z-index: 10;
    pointer-events: none;
    backdrop-filter: blur(2px);
  }

  /* Compact input styling */
  input.compact {
    padding: calc(var(--cell-padding) * 0.5) var(--cell-padding);
  }

  /* Remove spinners from number inputs */
  input[type="number"] {
    -moz-appearance: textfield;
    
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

</style>