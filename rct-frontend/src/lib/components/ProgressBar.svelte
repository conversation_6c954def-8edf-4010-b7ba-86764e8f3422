<script lang="ts">
  export let value: number = 0;
  export let max: number = 100;
  export let color: string = 'var(--color-primary)';
  export let backgroundColor: string = '#f1f5f9';
  export let height: string = '8px';
  export let showLabel: boolean = false;
  export let label: string = '';
  export let animated: boolean = false;
  
  $: percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  $: displayLabel = label || `${Math.round(percentage)}%`;
</script>

<div class="progress-container">
  {#if showLabel}
    <div class="progress-label">
      <span>{displayLabel}</span>
      <span class="progress-value">{value}/{max}</span>
    </div>
  {/if}
  
  <div 
    class="progress-bar" 
    style="height: {height}; background-color: {backgroundColor};"
  >
    <div 
      class="progress-fill"
      class:animated
      style="width: {percentage}%; background-color: {color};"
    ></div>
  </div>
</div>

<style>
  .progress-container {
    width: 100%;
  }
  
  .progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--color-text);
  }
  
  .progress-value {
    font-size: 0.75rem;
    color: var(--color-grey);
  }
  
  .progress-bar {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }
  
  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
  }
  
  .progress-fill.animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent
    );
    animation: shine 2s infinite;
  }
  
  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
</style>