<script lang="ts">
  import { settings, addTag, updateTag, deleteTag, addResponseTemplate, updateResponseTemplate, deleteResponseTemplate, addTalkingPoint, updateTalkingPoint, deleteTalkingPoint, resetToDefaults, type Tag, type ResponseTemplate, type TalkingPoint } from '$lib/stores/settings';
  import type { SettingsTab } from '$lib/stores';
  import Button from './Button.svelte';
  import FormGroup from './FormGroup.svelte';
  import Tabs from './Tabs.svelte';
  
  export let tab: SettingsTab;
  
  let activeTab: 'tags' | 'templates' | 'talking-points' = 'tags';

  $: tabsData = [
    {
      id: 'tags',
      label: 'Tags',
      icon: 'fas fa-tags',
      count: $settings.tags.length
    },
    {
      id: 'templates',
      label: 'Response Templates',
      icon: 'fas fa-comment-dots',
      count: $settings.responseTemplates.length
    },
    {
      id: 'talking-points',
      label: 'Talking Points',
      icon: 'fas fa-comments',
      count: $settings.talkingPoints.length
    }
  ];

  function handleTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId as 'tags' | 'templates' | 'talking-points';
  }

  // Tag management state
  let showAddTag = false;
  let editingTag: Tag | null = null;
  let newTag: Partial<Tag> = { name: '', color: '#059669', description: '' };
  
  // Template management state
  let showAddTemplate = false;
  let editingTemplate: ResponseTemplate | null = null;
  let newTemplate: Partial<ResponseTemplate> = { title: '', text: '', category: '' };
  
  // Talking point management state
  let showAddTalkingPoint = false;
  let editingTalkingPoint: TalkingPoint | null = null;
  let newTalkingPoint: Partial<TalkingPoint> = { title: '', content: '', category: '' };
  
  // Confirmation dialogs
  let confirmDeleteTag: Tag | null = null;
  let confirmDeleteTemplate: ResponseTemplate | null = null;
  let confirmDeleteTalkingPoint: TalkingPoint | null = null;
  let showResetConfirmation = false;
  
  // Get unique categories for talking points dropdown
  $: talkingPointCategories = [...new Set($settings.talkingPoints.map(t => t.category))].sort();
  
  function startAddingTag() {
    showAddTag = true;
    newTag = { name: '', color: '#059669', description: '' };
  }
  
  function cancelAddTag() {
    showAddTag = false;
    newTag = { name: '', color: '#059669', description: '' };
  }
  
  function saveNewTag() {
    if (newTag.name?.trim()) {
      addTag({
        name: newTag.name.trim(),
        color: newTag.color || '#059669',
        description: newTag.description?.trim() || undefined
      });
      cancelAddTag();
    }
  }
  
  function startEditingTag(tag: Tag) {
    editingTag = { ...tag };
  }
  
  function cancelEditTag() {
    editingTag = null;
  }
  
  function saveEditTag() {
    if (editingTag?.name.trim()) {
      updateTag(editingTag.id, {
        name: editingTag.name.trim(),
        color: editingTag.color,
        description: editingTag.description?.trim() || undefined
      });
      editingTag = null;
    }
  }
  
  function handleDeleteTag(tag: Tag) {
    deleteTag(tag.id);
    confirmDeleteTag = null;
  }
  
  // Template functions
  function startAddingTemplate() {
    showAddTemplate = true;
    newTemplate = { title: '', text: '', category: '' };
  }
  
  function cancelAddTemplate() {
    showAddTemplate = false;
    newTemplate = { title: '', text: '', category: '' };
  }
  
  function saveNewTemplate() {
    if (newTemplate.title?.trim() && newTemplate.text?.trim()) {
      addResponseTemplate({
        title: newTemplate.title.trim(),
        text: newTemplate.text.trim(),
        category: newTemplate.category?.trim() || 'General'
      });
      cancelAddTemplate();
    }
  }
  
  function startEditingTemplate(template: ResponseTemplate) {
    editingTemplate = { ...template };
  }
  
  function cancelEditTemplate() {
    editingTemplate = null;
  }
  
  function saveEditTemplate() {
    if (editingTemplate?.title.trim() && editingTemplate?.text.trim()) {
      updateResponseTemplate(editingTemplate.id, {
        title: editingTemplate.title.trim(),
        text: editingTemplate.text.trim(),
        category: editingTemplate.category?.trim() || 'General'
      });
      editingTemplate = null;
    }
  }
  
  function handleDeleteTemplate(template: ResponseTemplate) {
    deleteResponseTemplate(template.id);
    confirmDeleteTemplate = null;
  }
  
  // Talking point functions
  function startAddingTalkingPoint() {
    showAddTalkingPoint = true;
    newTalkingPoint = { title: '', content: '', category: '' };
  }
  
  function cancelAddTalkingPoint() {
    showAddTalkingPoint = false;
    newTalkingPoint = { title: '', content: '', category: '' };
  }
  
  function saveNewTalkingPoint() {
    if (newTalkingPoint.title?.trim() && newTalkingPoint.content?.trim()) {
      addTalkingPoint({
        title: newTalkingPoint.title.trim(),
        content: newTalkingPoint.content.trim(),
        category: newTalkingPoint.category?.trim() || 'General'
      });
      cancelAddTalkingPoint();
    }
  }
  
  function startEditingTalkingPoint(talkingPoint: TalkingPoint) {
    editingTalkingPoint = { ...talkingPoint };
  }
  
  function cancelEditTalkingPoint() {
    editingTalkingPoint = null;
  }
  
  function saveEditTalkingPoint() {
    if (editingTalkingPoint?.title.trim() && editingTalkingPoint?.content.trim()) {
      updateTalkingPoint(editingTalkingPoint.id, {
        title: editingTalkingPoint.title.trim(),
        content: editingTalkingPoint.content.trim(),
        category: editingTalkingPoint.category?.trim() || 'General'
      });
      editingTalkingPoint = null;
    }
  }
  
  function handleDeleteTalkingPoint(talkingPoint: TalkingPoint) {
    deleteTalkingPoint(talkingPoint.id);
    confirmDeleteTalkingPoint = null;
  }
  
  function confirmResetToDefaults() {
    showResetConfirmation = true;
  }
  
  function handleResetToDefaults() {
    resetToDefaults();
    showResetConfirmation = false;
  }
</script>

<div class="settings-tab-container">
  <div class="settings-header">
    <div class="header-content">
      <div class="header-title">
        <h1>Tags & Templates</h1>
        <p>Manage your tags, response templates, and talking points</p>
      </div>
      <div class="header-actions">
        <Button
          variant="outline"
          size="sm"
          icon="fas fa-undo"
          on:click={confirmResetToDefaults}
        >
          Reset to Defaults
        </Button>
      </div>
    </div>
  </div>

  <div class="settings-container">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <Tabs
        tabs={tabsData}
        bind:activeTab
        on:tabChange={handleTabChange}
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      {#if activeTab === 'tags'}
        <!-- Tags Tab Content -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Manage Tags</h2>
            <p>Create and manage tags for organizing your data</p>
          </div>
          <div class="section-action-header">
            <Button
              variant="primary"
              size="sm"
              icon="fas fa-plus"
              on:click={startAddingTag}
            >
              Add Tag
            </Button>
          </div>

          <!-- Add Tag Form -->
          {#if showAddTag}
            <div class="add-form">
              <h3>Add New Tag</h3>
              <div class="form-grid">
                <div class="form-group">
                  <label>Tag Name <span class="required">*</span></label>
                  <input type="text" bind:value={newTag.name} placeholder="Enter tag name" />
                </div>
                <div class="form-group">
                  <label>Color</label>
                  <input type="color" bind:value={newTag.color} />
                </div>
                <div class="form-group full-width">
                  <label>Description</label>
                  <textarea bind:value={newTag.description} placeholder="Optional description" rows="2"></textarea>
                </div>
              </div>
              <div class="form-actions">
                <Button variant="primary" on:click={saveNewTag}>Save Tag</Button>
                <Button variant="ghost" on:click={cancelAddTag}>Cancel</Button>
              </div>
            </div>
          {/if}

          <!-- Tags List -->
          <div class="items-list">
            {#each $settings.tags as tag (tag.id)}
              <div class="item-card">
                {#if editingTag && editingTag.id === tag.id}
                  <!-- Edit Mode -->
                  <div class="edit-form">
                    <div class="form-grid">
                      <div class="form-group">
                        <label>Tag Name <span class="required">*</span></label>
                        <input type="text" bind:value={editingTag.name} />
                      </div>
                      <div class="form-group">
                        <label>Color</label>
                        <input type="color" bind:value={editingTag.color} />
                      </div>
                      <div class="form-group full-width">
                        <label>Description</label>
                        <textarea bind:value={editingTag.description} rows="2"></textarea>
                      </div>
                    </div>
                    <div class="form-actions">
                      <Button variant="primary" size="sm" on:click={saveEditTag}>Save</Button>
                      <Button variant="ghost" size="sm" on:click={cancelEditTag}>Cancel</Button>
                    </div>
                  </div>
                {:else}
                  <!-- Display Mode -->
                  <div class="item-header">
                    <div class="tag-preview" style="background-color: {tag.color};">
                      {tag.name}
                    </div>
                    <div class="item-actions">
                      <Button variant="ghost" size="sm" icon="fas fa-edit" on:click={() => startEditingTag(tag)}>
                        Edit
                      </Button>
                      <Button variant="outline" size="sm" icon="fas fa-trash" on:click={() => confirmDeleteTag = tag}>
                        Delete
                      </Button>
                    </div>
                  </div>
                  {#if tag.description}
                    <p class="item-description">{tag.description}</p>
                  {/if}
                {/if}
              </div>
            {/each}
          </div>
        </div>
      {/if}

      {#if activeTab === 'templates'}
        <!-- Templates Tab Content -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Manage Response Templates</h2>
            <p>Create and manage response templates for quick note entry</p>
          </div>
          <div class="section-action-header">
            <Button
              variant="primary"
              size="sm"
              icon="fas fa-plus"
              on:click={startAddingTemplate}
            >
              Add Template
            </Button>
          </div>

          <!-- Add Template Form -->
          {#if showAddTemplate}
            <div class="add-form">
              <h3>Add New Response Template</h3>
              <div class="form-grid template-form">
                <div class="form-group">
                  <label>Title <span class="required">*</span></label>
                  <input type="text" bind:value={newTemplate.title} placeholder="Enter template title" />
                </div>
                <div class="form-group">
                  <label>Category</label>
                  <input type="text" bind:value={newTemplate.category} placeholder="Enter category" />
                </div>
                <div class="form-group full-width">
                  <label>Content <span class="required">*</span></label>
                  <textarea bind:value={newTemplate.text} placeholder="Enter template content" rows="4"></textarea>
                </div>
              </div>
              <div class="form-actions">
                <Button variant="primary" on:click={saveNewTemplate}>Save Template</Button>
                <Button variant="ghost" on:click={cancelAddTemplate}>Cancel</Button>
              </div>
            </div>
          {/if}

          <!-- Templates List -->
          <div class="items-list">
            {#each $settings.responseTemplates as template (template.id)}
              <div class="item-card template-card">
                {#if editingTemplate && editingTemplate.id === template.id}
                  <!-- Edit Mode -->
                  <div class="edit-form">
                    <div class="form-grid template-form">
                      <div class="form-group">
                        <label>Title <span class="required">*</span></label>
                        <input type="text" bind:value={editingTemplate.title} />
                      </div>
                      <div class="form-group">
                        <label>Category</label>
                        <input type="text" bind:value={editingTemplate.category} />
                      </div>
                      <div class="form-group full-width">
                        <label>Content <span class="required">*</span></label>
                        <textarea bind:value={editingTemplate.text} rows="4"></textarea>
                      </div>
                    </div>
                    <div class="form-actions">
                      <Button variant="primary" size="sm" on:click={saveEditTemplate}>Save</Button>
                      <Button variant="ghost" size="sm" on:click={cancelEditTemplate}>Cancel</Button>
                    </div>
                  </div>
                {:else}
                  <!-- Display Mode -->
                  <div class="item-header">
                    <div class="template-info">
                      <div class="template-title">{template.title}</div>
                      <div class="template-category">{template.category}</div>
                    </div>
                    <div class="item-actions">
                      <Button variant="ghost" size="sm" icon="fas fa-edit" on:click={() => startEditingTemplate(template)}>
                        Edit
                      </Button>
                      <Button variant="outline" size="sm" icon="fas fa-trash" on:click={() => confirmDeleteTemplate = template}>
                        Delete
                      </Button>
                    </div>
                  </div>
                  <div class="template-content">{template.text}</div>
                {/if}
              </div>
            {/each}
          </div>
        </div>
      {/if}

      {#if activeTab === 'talking-points'}
        <!-- Talking Points Tab Content -->
        <div class="settings-section">
          <div class="section-header">
            <h2>Manage Talking Points</h2>
            <p>Create and manage talking points for guidance during conversations</p>
          </div>
          <div class="section-action-header">
            <Button
              variant="primary"
              size="sm"
              icon="fas fa-plus"
              on:click={startAddingTalkingPoint}
            >
              Add Talking Point
            </Button>
          </div>

          <!-- Add Talking Point Form -->
          {#if showAddTalkingPoint}
            <div class="add-form">
              <h3>Add New Talking Point</h3>
              <div class="form-grid template-form">
                <div class="form-group">
                  <label>Title <span class="required">*</span></label>
                  <input type="text" bind:value={newTalkingPoint.title} placeholder="Enter talking point title" />
                </div>
                <div class="form-group">
                  <label>Category</label>
                  <select bind:value={newTalkingPoint.category}>
                    <option value="">Select category</option>
                    {#each talkingPointCategories as category}
                      <option value={category}>{category}</option>
                    {/each}
                    <option value="new">+ Add new category</option>
                  </select>
                </div>
                <div class="form-group full-width">
                  <label>Content <span class="required">*</span></label>
                  <textarea bind:value={newTalkingPoint.content} placeholder="Enter talking point content" rows="4"></textarea>
                </div>
              </div>
              <div class="form-actions">
                <Button variant="primary" on:click={saveNewTalkingPoint}>Save Talking Point</Button>
                <Button variant="ghost" on:click={cancelAddTalkingPoint}>Cancel</Button>
              </div>
            </div>
          {/if}

          <!-- Talking Points List -->
          <div class="items-list">
            {#each $settings.talkingPoints as talkingPoint (talkingPoint.id)}
              <div class="item-card talking-point-card">
                {#if editingTalkingPoint && editingTalkingPoint.id === talkingPoint.id}
                  <!-- Edit Mode -->
                  <div class="edit-form">
                    <div class="form-grid template-form">
                      <div class="form-group">
                        <label>Title <span class="required">*</span></label>
                        <input type="text" bind:value={editingTalkingPoint.title} />
                      </div>
                      <div class="form-group">
                        <label>Category</label>
                        <select bind:value={editingTalkingPoint.category}>
                          <option value="">Select category</option>
                          {#each talkingPointCategories as category}
                            <option value={category}>{category}</option>
                          {/each}
                        </select>
                      </div>
                      <div class="form-group full-width">
                        <label>Content <span class="required">*</span></label>
                        <textarea bind:value={editingTalkingPoint.content} rows="4"></textarea>
                      </div>
                    </div>
                    <div class="form-actions">
                      <Button variant="primary" size="sm" on:click={saveEditTalkingPoint}>Save</Button>
                      <Button variant="ghost" size="sm" on:click={cancelEditTalkingPoint}>Cancel</Button>
                    </div>
                  </div>
                {:else}
                  <!-- Display Mode -->
                  <div class="item-header">
                    <div class="talking-point-info">
                      <div class="talking-point-title">{talkingPoint.title}</div>
                      <div class="talking-point-category">{talkingPoint.category}</div>
                    </div>
                    <div class="item-actions">
                      <Button variant="ghost" size="sm" icon="fas fa-edit" on:click={() => startEditingTalkingPoint(talkingPoint)}>
                        Edit
                      </Button>
                      <Button variant="outline" size="sm" icon="fas fa-trash" on:click={() => confirmDeleteTalkingPoint = talkingPoint}>
                        Delete
                      </Button>
                    </div>
                  </div>
                  <div class="talking-point-content">{talkingPoint.content}</div>
                {/if}
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Confirmation Dialogs -->
{#if confirmDeleteTag}
  <div class="modal-overlay" on:click={() => confirmDeleteTag = null}>
    <div class="modal-content" on:click|stopPropagation>
      <h3>Delete Tag</h3>
      <p>Are you sure you want to delete the tag "{confirmDeleteTag.name}"?</p>
      <div class="modal-actions">
        <Button variant="danger" on:click={() => handleDeleteTag(confirmDeleteTag)}>Delete</Button>
        <Button variant="ghost" on:click={() => confirmDeleteTag = null}>Cancel</Button>
      </div>
    </div>
  </div>
{/if}

{#if confirmDeleteTemplate}
  <div class="modal-overlay" on:click={() => confirmDeleteTemplate = null}>
    <div class="modal-content" on:click|stopPropagation>
      <h3>Delete Response Template</h3>
      <p>Are you sure you want to delete the template "{confirmDeleteTemplate.title}"?</p>
      <div class="modal-actions">
        <Button variant="danger" on:click={() => handleDeleteTemplate(confirmDeleteTemplate)}>Delete</Button>
        <Button variant="ghost" on:click={() => confirmDeleteTemplate = null}>Cancel</Button>
      </div>
    </div>
  </div>
{/if}

{#if confirmDeleteTalkingPoint}
  <div class="modal-overlay" on:click={() => confirmDeleteTalkingPoint = null}>
    <div class="modal-content" on:click|stopPropagation>
      <h3>Delete Talking Point</h3>
      <p>Are you sure you want to delete the talking point "{confirmDeleteTalkingPoint.title}"?</p>
      <div class="modal-actions">
        <Button variant="danger" on:click={() => handleDeleteTalkingPoint(confirmDeleteTalkingPoint)}>Delete</Button>
        <Button variant="ghost" on:click={() => confirmDeleteTalkingPoint = null}>Cancel</Button>
      </div>
    </div>
  </div>
{/if}

{#if showResetConfirmation}
  <div class="modal-overlay" on:click={() => showResetConfirmation = false}>
    <div class="modal-content" on:click|stopPropagation>
      <h3>Reset to Defaults</h3>
      <p>Are you sure you want to reset all settings to their default values? This will remove all custom tags, templates, and talking points.</p>
      <div class="modal-actions">
        <Button variant="danger" on:click={handleResetToDefaults}>Reset</Button>
        <Button variant="ghost" on:click={() => showResetConfirmation = false}>Cancel</Button>
      </div>
    </div>
  </div>
{/if}

<style>
  .settings-tab-container {
    padding: 1.5rem;
    max-width: 100%;
    height: 100%;
    overflow-y: auto;
  }

  /* Adjust settings container when notes sidebar is active */
  :global(.notes-sidebar-active) .settings-tab-container {
    max-width: calc(100% - 420px); /* Account for sidebar width (400px) + margin (20px) */
  }

  .settings-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .header-title h1 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .header-title p {
    margin: 0;
    color: #6b7280;
  }

  .settings-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .tab-content {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .settings-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .section-header h2 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .section-header p {
    margin: 0;
    color: #6b7280;
  }

  .section-action-header {
    display: flex;
    justify-content: flex-end;
  }

  .add-form {
    background: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
  }

  .add-form h3 {
    margin: 0 0 1rem 0;
    color: #111827;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .form-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
  }

  .items-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .item-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .tag-preview {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .item-actions {
    display: flex;
    gap: 0.5rem;
  }

  .item-description {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
  }

  /* Template and Talking Point specific styles */
  .template-form {
    grid-template-columns: 1fr 1fr;
  }

  .template-info, .talking-point-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .template-title, .talking-point-title {
    font-weight: 600;
    color: #111827;
    font-size: 0.875rem;
  }

  .template-category, .talking-point-category {
    font-size: 0.75rem;
    color: var(--color-primary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .template-content, .talking-point-content {
    margin-top: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: pre-wrap;
  }

  .template-card, .talking-point-card {
    /* Inherit all item-card styles */
  }

  /* Form styles */
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .form-group .required {
    color: #ef4444;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.15s ease;
  }

  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
  }

  .form-group textarea {
    resize: vertical;
    min-height: 60px;
  }

  .form-group.full-width {
    grid-column: span 2;
  }

  /* Form grid adjustments for templates and talking points */
  .form-grid.template-form {
    grid-template-columns: 1fr 1fr;
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    max-width: 400px;
    width: 90%;
  }

  .modal-content h3 {
    margin: 0 0 1rem 0;
    color: #111827;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .modal-content p {
    margin: 0 0 1.5rem 0;
    color: #6b7280;
  }

  .modal-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .settings-tab-container {
      padding: 1rem;
    }
    
    /* Notes sidebar is forced to bottom on mobile, so no width adjustment needed */
    :global(.notes-sidebar-active) .settings-tab-container {
      max-width: 100%;
    }
    
    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }
    
    .form-grid {
      grid-template-columns: 1fr;
    }
    
    .form-group.full-width {
      grid-column: span 1;
    }
  }

  /* Force bottom mode on screens too narrow for sidebar (900px and below) */
  @media (max-width: 900px) {
    /* Notes sidebar is forced to bottom, so no width adjustment needed */
    :global(.notes-sidebar-active) .settings-tab-container {
      max-width: 100%;
    }
  }

  /* Tablet responsive adjustments (901px to 1024px) */
  @media (max-width: 1024px) and (min-width: 901px) {
    /* Notes sidebar is smaller on tablets (300px + 20px margin = 320px) */
    :global(.notes-sidebar-active) .settings-tab-container {
      max-width: calc(100% - 320px);
    }
  }
</style>
