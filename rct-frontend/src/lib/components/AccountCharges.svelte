<script lang="ts">
  import { updateAccountChargesData, currentInstanceData } from '$lib/stores';
  import OutputGroup from './OutputGroup.svelte';
  import FormGroup from './FormGroup.svelte';
  import Button from './Button.svelte';
  import DataTable from './DataTable.svelte';
  
  export let isFullView: boolean = false;
  
  // Local state for form inputs
  let grossWeeklyRent = 0;
  let rentComponent = 0;
  let nonUcServiceChargeFormula = '';
  let nonUcServiceChargeTotal = 0;
  
  // Track if gross weekly rent has been manually overridden
  let grossWeeklyRentOverridden = false;
  
  // Track formula notes visibility
  let showFormulaNotes = false;
  
  // Track reference information visibility
  let showReferenceInfo = false;
  
  // Track previous weekly rent to detect changes for clearing
  let previousWeeklyRent = 0;
  
  // DataTable rows (declared here to be assignable in reactive blocks)
  let entitlementsRows: any[] = [];
  
  
  // Synced data from Rent Calculator
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;
  $: weeklyRentFromCalculator = rentCalculatorData?.weeklyRent || 0;
  $: reference = rentCalculatorData?.reference || '';
  
  // Subscribe to current instance data for account charges
  $: if ($currentInstanceData) {
    const data = $currentInstanceData.accountCharges;
    grossWeeklyRent = data.grossWeeklyRent || weeklyRentFromCalculator;
    rentComponent = data.rentComponent;
    
    // Handle legacy data with separate fields or new formula field
    if (data.nonUcServiceChargeFormula !== undefined) {
      nonUcServiceChargeFormula = data.nonUcServiceChargeFormula;
      nonUcServiceChargeTotal = data.nonUcServiceChargeTotal || 0;
    } else {
      // Legacy: combine the three separate fields into formula
      const charge1 = data.nonUcServiceCharge1 || 0;
      const charge2 = data.nonUcServiceCharge2 || 0; 
      const charge3 = data.nonUcServiceCharge3 || 0;
      const hasCharges = charge1 > 0 || charge2 > 0 || charge3 > 0;
      nonUcServiceChargeFormula = hasCharges ? [charge1, charge2, charge3].filter(c => c > 0).join('+') : '';
      nonUcServiceChargeTotal = charge1 + charge2 + charge3;
    }
    
    grossWeeklyRentOverridden = data.grossWeeklyRentOverridden || false;
  }
  
  // Auto-sync gross weekly rent when weekly rent changes in calculator (only if not manually overridden)
  $: if (!grossWeeklyRentOverridden && weeklyRentFromCalculator !== grossWeeklyRent && weeklyRentFromCalculator > 0) {
    grossWeeklyRent = weeklyRentFromCalculator;
    handleInputChange();
  }
  
  // Clear service charges data when weekly rent changes (to prevent case data mixing)
  $: if (weeklyRentFromCalculator > 0 && previousWeeklyRent > 0 && weeklyRentFromCalculator !== previousWeeklyRent) {
    // Clear all service charges data when switching between cases
    rentComponent = 0;
    nonUcServiceChargeFormula = '';
    nonUcServiceChargeTotal = 0;
    grossWeeklyRentOverridden = false; // Reset override flag
    
    // Update the store with cleared data
    handleInputChange();
  }
  
  // Track previous weekly rent value for comparison
  $: if (weeklyRentFromCalculator > 0) {
    previousWeeklyRent = weeklyRentFromCalculator;
  }
  
  // Math evaluation function for formulas
  function evaluateMathExpression(value: string): number {
    // Remove leading/trailing spaces and commas
    const cleaned = value.trim().replace(/,/g, '');
    
    // If empty, return 0
    if (!cleaned) return 0;
    
    // If it's just a number, parse it directly
    if (/^\d*\.?\d+$/.test(cleaned)) {
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    // Try to evaluate as a mathematical expression
    try {
      // Only allow safe mathematical expressions (numbers, +, -, *, /, ., spaces, parentheses)
      if (!/^[\d+\-*/().\s]+$/.test(cleaned)) {
        // If it contains invalid characters, try parsing as regular number
        const parsed = parseFloat(cleaned);
        return isNaN(parsed) ? 0 : parsed;
      }
      
      // Use Function constructor to safely evaluate mathematical expressions
      const result = new Function('return ' + cleaned)();
      return isNaN(result) ? 0 : result;
    } catch (error) {
      // If evaluation fails, try parsing as regular number
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
  }

  // Reactive calculation of formula total - ensure non-negative
  $: nonUcServiceChargeTotal = Math.max(0, evaluateMathExpression(nonUcServiceChargeFormula));

  // Calculation functions
  
  // Calculation 18: Gross Monthly Rent
  function calculateGrossMonthlyRent(): number {
    return grossWeeklyRent * 52 / 12;
  }
  
  // Calculation 19: Total non-eligible (service charges)
  function calculateTotalNonEligible(): number {
    return nonUcServiceChargeTotal;
  }
  
  // Calculation 20: UC service charges (Eligible service charges) - Weekly
  function calculateEligibleServiceChargesWeekly(): number {
    return grossWeeklyRent - rentComponent - calculateTotalNonEligible();
  }
  
  // Calculation 21: Full entitlement (HB) - Weekly
  function calculateHbFullEntitlementWeekly(): number {
    return rentComponent + calculateEligibleServiceChargesWeekly();
  }
  
  // Calculation 22: Full entitlement (UC) - Monthly
  function calculateUcFullEntitlementMonthly(): number {
    return (rentComponent + calculateEligibleServiceChargesWeekly()) * 52 / 12;
  }
  
  // Calculation 23: Low under occupation (UC Entitlement) - Monthly
  function calculateUcLowUnderOccupationMonthly(): number {
    return calculateUcFullEntitlementMonthly() * 0.86;
  }
  
  // Calculation 24: High under occupation (UC Entitlement) - Monthly
  function calculateUcHighUnderOccupationMonthly(): number {
    return calculateUcFullEntitlementMonthly() * 0.75;
  }
  
  // Calculation 25: HB Shortfall - Weekly
  function calculateHbShortfallWeekly(): number {
    return Math.max(0, grossWeeklyRent - calculateHbFullEntitlementWeekly());
  }
  
  // Calculation 26: UC Full Entitlement Shortfall - Monthly
  function calculateUcFullEntitlementShortfallMonthly(): number {
    return Math.max(0, calculateGrossMonthlyRent() - calculateUcFullEntitlementMonthly());
  }
  
  // Calculation 27: UC Low Under Occupation Shortfall - Monthly
  function calculateUcLowUnderOccupationShortfallMonthly(): number {
    return Math.max(0, calculateGrossMonthlyRent() - calculateUcLowUnderOccupationMonthly());
  }
  
  // Calculation 28: UC High Under Occupation Shortfall - Monthly
  function calculateUcHighUnderOccupationShortfallMonthly(): number {
    return Math.max(0, calculateGrossMonthlyRent() - calculateUcHighUnderOccupationMonthly());
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function sanitizeNumericInput(value: string): number {
    return evaluateMathExpression(value);
  }
  
  function handleInputChange() {
    updateAccountChargesData({
      grossWeeklyRent,
      rentComponent,
      nonUcServiceChargeFormula,
      nonUcServiceChargeTotal,
      grossWeeklyRentOverridden
    });
  }
  
  function handleFormGroupInput(event: CustomEvent) {
    const { id, value } = event.detail;
    
    switch(id) {
      case 'grossWeeklyRent':
        grossWeeklyRent = sanitizeNumericInput(value);
        grossWeeklyRentOverridden = true; // Mark as manually overridden
        break;
      case 'rentComponent':
        rentComponent = sanitizeNumericInput(value);
        break;
      case 'nonUcServiceChargeFormula':
        nonUcServiceChargeFormula = value; // Keep as string for formula evaluation
        break;
    }
    
    handleInputChange();
  }
  
  
  function getShortfallColor(value: number): string {
    if (value > 0) return 'var(--color-red)'; // Shortfall is bad (red)
    return '#111827'; // No shortfall (balanced)
  }

  function getShortfallLabel(value: number): string {
    if (value > 0) return 'Shortfall';
    return 'No Shortfall';
  }

  // Data preparation for DataTable components
  $: entitlementsColumns = [
    { key: 'label', label: '', type: 'label' as const, align: 'left' as const, width: '1fr' },
    { key: 'entitlement', label: 'Full Entitlement', type: 'currency' as const, align: 'right' as const, width: '1fr' },
    { key: 'shortfall', label: 'Shortfall', type: 'currency' as const, align: 'right' as const, width: '1fr' }
  ];

  $: {
    // Explicitly reference all variables that affect entitlements calculations to ensure reactivity
    const deps = [
      grossWeeklyRent,
      rentComponent,
      nonUcServiceChargeTotal,
      showFormulaNotes
    ];
    
    entitlementsRows = [
      {
        label: 'HB (weekly):',
        entitlement: calculateHbFullEntitlementWeekly(),
        shortfall: calculateHbShortfallWeekly(),
        entitlement_html: showFormulaNotes 
          ? `<span class="formula-note">Gross Weekly Rent - sum(ineligible service charges)</span><div class="value" style="font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateHbFullEntitlementWeekly())}</div>`
          : undefined,
        shortfall_html: showFormulaNotes 
          ? `<span class="formula-note">Sum(Ineligible service charges)</span><div class="value" style="color: ${getShortfallColor(calculateHbShortfallWeekly())}; font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateHbShortfallWeekly())}</div>`
          : undefined,
        shortfall_style: showFormulaNotes ? '' : `color: ${getShortfallColor(calculateHbShortfallWeekly())}`
      },
      {
        label: 'UC (monthly):',
        entitlement: calculateUcFullEntitlementMonthly(),
        shortfall: calculateUcFullEntitlementShortfallMonthly(),
        entitlement_html: showFormulaNotes 
          ? `<span class="formula-note">(Gross Weekly Rent - sum(ineligible service charges)) × 52 ÷ 12</span><div class="value" style="font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateUcFullEntitlementMonthly())}</div>`
          : undefined,
        shortfall_html: showFormulaNotes 
          ? `<span class="formula-note">Sum(Ineligible service charges) × 52 ÷ 12</span><div class="value" style="color: ${getShortfallColor(calculateUcFullEntitlementShortfallMonthly())}; font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateUcFullEntitlementShortfallMonthly())}</div>`
          : undefined,
        shortfall_style: showFormulaNotes ? '' : `color: ${getShortfallColor(calculateUcFullEntitlementShortfallMonthly())}`
      },
      {
        label: 'Low under occupation (monthly):',
        entitlement: calculateUcLowUnderOccupationMonthly(),
        shortfall: calculateUcLowUnderOccupationShortfallMonthly(),
        entitlement_html: showFormulaNotes 
          ? `<span class="formula-note">((Gross Weekly Rent - sum(ineligible service charges)) × 52 ÷ 12) × 0.86</span><div class="value" style="font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateUcLowUnderOccupationMonthly())}</div>`
          : undefined,
        shortfall_html: showFormulaNotes 
          ? `<span class="formula-note">Gross Monthly Rent - Low under occupation (monthly)</span><div class="value" style="color: ${getShortfallColor(calculateUcLowUnderOccupationShortfallMonthly())}; font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateUcLowUnderOccupationShortfallMonthly())}</div>`
          : undefined,
        shortfall_style: showFormulaNotes ? '' : `color: ${getShortfallColor(calculateUcLowUnderOccupationShortfallMonthly())}`
      },
      {
        label: 'High under occupation (monthly):',
        entitlement: calculateUcHighUnderOccupationMonthly(),
        shortfall: calculateUcHighUnderOccupationShortfallMonthly(),
        entitlement_html: showFormulaNotes 
          ? `<span class="formula-note">((Gross Weekly Rent - sum(ineligible service charges)) × 52 ÷ 12) × 0.75</span><div class="value" style="font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateUcHighUnderOccupationMonthly())}</div>`
          : undefined,
        shortfall_html: showFormulaNotes 
          ? `<span class="formula-note">Gross Monthly Rent - High under occupation (monthly)</span><div class="value" style="color: ${getShortfallColor(calculateUcHighUnderOccupationShortfallMonthly())}; font-family: 'Martian Mono', 'Courier New', monospace; font-weight: 600;">${formatCurrency(calculateUcHighUnderOccupationShortfallMonthly())}</div>`
          : undefined,
        shortfall_style: showFormulaNotes ? '' : `color: ${getShortfallColor(calculateUcHighUnderOccupationShortfallMonthly())}`
      }
    ];
  }

  // Reference data tables - 2 columns each
  $: referenceColumns = [
    { key: 'label', label: '', type: 'label' as const, align: 'left' as const, width: '1fr' },
    { key: 'value', label: '', type: 'currency' as const, align: 'right' as const, width: 'auto' }
  ];

  $: commonDeductionsRows = [
    { label: 'UC non-dep (monthly):', value: '£93.02' },
    { label: 'HB overpayment deduction (weekly):', value: '£13.95' },
    { label: 'HB overpayment deduction (monthly):', value: '£47.54' }
  ];

  $: ucAllowancesRows = [
    { label: 'Single Under 25:', value: '£316.98' },
    { label: 'Single 25 or over:', value: '£400.14' },
    { label: 'Couple Both under 25:', value: '£497.55' },
    { label: 'Couple One or both 25 or over:', value: '£628.10' }
  ];

  $: tpdAmountsRows = [
    { label: 'Single Under 25:', value: '£31.70' },
    { label: 'Single 25 or over:', value: '£40.01' },
    { label: 'Couple Both under 25:', value: '£49.76' },
    { label: 'Couple One or both 25 or over:', value: '£62.81' }
  ];

  async function copyRentInfo() {
    const weeklyRentValue = rentComponent || 0;
    const eligibleServiceCharges = calculateEligibleServiceChargesWeekly() || 0;
    
    const copyText = `Weekly rent is £${weeklyRentValue.toFixed(2)} and eligible service charges are £${eligibleServiceCharges.toFixed(2)}`;
    
    try {
      await navigator.clipboard.writeText(copyText);
      console.log('Rent info copied to clipboard:', copyText);
      // You could add a toast notification here if needed
    } catch (error) {
      console.error('Failed to copy rent info to clipboard:', error);
      // Fallback method for older browsers
      try {
        const textArea = document.createElement('textarea');
        textArea.value = copyText;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        const result = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (result) {
          console.log('Rent info copied to clipboard via legacy method:', copyText);
        } else {
          console.error('Legacy copy method failed');
        }
      } catch (fallbackError) {
        console.error('Failed to copy rent info with fallback method:', fallbackError);
      }
    }
  }
</script>

<div class="account-charges">
  <div class="charges-grid">
    <!-- LEFT COLUMN -->
    <div class="charges-left">
      <!-- Left side: Input Fields and Basic Calculations -->
      <div class="charges-left-column">
        <!-- Input Fields -->
        <div class="input-section">
          <div class="section-header-with-button">
            <h4>Rent and Service Charges</h4>
            <button class="copy-info-btn" on:click={copyRentInfo} title="Copy rent and service charge info">
              <i class="fas fa-copy"></i>
              Copy Info
            </button>
          </div>
          <div class="input-grid">
            <div class="form-group-with-help">
              <FormGroup
                id="grossWeeklyRent"
                label="Gross Weekly Rent"
                value={grossWeeklyRent}
                placeholder="0.00"
                type="number"
                showCurrency={true}
                on:input={handleFormGroupInput}
              />
              <!-- <small class="input-help">Auto-synced from Rent Calculator, but can be overridden</small> -->
            </div>
            <FormGroup
              id="rentComponent"
              label="Rent Component"
              value={rentComponent}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
            <div>
              <FormGroup
                id="nonUcServiceChargeFormula"
                label="Non-eligible charges"
                value={nonUcServiceChargeFormula}
                type="text"
                placeholder="0.00"
                showCurrency={true}
                showFormula={true}
                on:input={handleFormGroupInput}
              />
              <!-- <small class="input-help">Enter a number or formula (e.g., 10+52+43). Negative values are not allowed.</small> -->
            </div>
            <OutputGroup 
              label="Eligible Service Charge"
              value={formatCurrency(calculateEligibleServiceChargesWeekly())}
              showCurrency={true}
            />
          </div>
        </div>
        <!-- Basic Calculations -->
        <div class="calculations-section">
          <h4>Basic Calculations</h4>
          <div class="basic-calc-grid">
            <OutputGroup 
              label="Gross Monthly Rent"
              value={formatCurrency(calculateGrossMonthlyRent())}
              showCurrency={true}
            />
            <div class="thresholds-container">
              <OutputGroup 
                label="APA Threshold:"
                value={formatCurrency(weeklyRentFromCalculator * 4)}
                showCurrency={true}
              />
              <OutputGroup 
                label="TPD Threshold:"
                value={formatCurrency(weeklyRentFromCalculator * 8)}
                showCurrency={true}
              />
            </div>
          </div>
        </div>
      </div>
      <!-- Right side: Entitlements and Shortfalls -->
      <div class="charges-right-column">
        <div class="entitlements-section">
          <div class="entitlements-header">
            <h4>Entitlements & Shortfalls</h4>
            <!-- <Button
              variant="outline"
              size="sm"
              icon={showFormulaNotes ? "fas fa-eye-slash" : "fas fa-eye"}
              on:click={() => showFormulaNotes = !showFormulaNotes}
            >
              {showFormulaNotes ? 'Hide' : 'Show'} Formulas (Admin)
            </Button> -->
          </div>
          <DataTable
            columns={entitlementsColumns}
            rows={entitlementsRows}
            showHeader={true}
            hoverEffect={true}
          />
        </div>
        <!-- Reference Information Section -->
      
      </div>
      <div class="info-section">
        <div class="reference-header">
          <h4>Reference Information</h4>
          <Button
            variant="outline"
            size="sm"
            icon={showReferenceInfo ? "fas fa-eye-slash" : "fas fa-eye"}
            on:click={() => showReferenceInfo = !showReferenceInfo}
          >
            {showReferenceInfo ? 'Hide' : 'Show'}
          </Button>
        </div>
        {#if showReferenceInfo}
          <div class="reference-grid">
            <!-- Universal Credit Monthly Standard Allowances -->
            <div class="reference-group">
              <h5>UC Monthly Standard Allowances</h5>
              <DataTable
                columns={referenceColumns}
                rows={ucAllowancesRows}
                showHeader={false}
                hoverEffect={false}
              />
              <div class="reference-link">
                <a href="https://www.benefitstraining.co.uk/wp-content/uploads/2025/04/btc_benefits_rates_poster_april_2025.pdf" target="_blank" rel="noopener noreferrer">
                  The Benefits Training Co. Benefits Rates 2025/2026
                </a>
              </div>
            </div>
            <!-- Minimum Expected TPD Amounts -->
            <div class="reference-group">
              <h5>Minimum Expected TPD Amounts</h5>
              <DataTable
                columns={referenceColumns}
                rows={tpdAmountsRows}
                showHeader={false}
                hoverEffect={false}
              />
            </div>
            <!-- Common Deductions -->
            <div class="reference-group">
              <h5>Common Deductions</h5>
              <DataTable
                columns={referenceColumns}
                rows={commonDeductionsRows}
                showHeader={false}
                hoverEffect={false}
              />
            </div>
          </div>
        {/if}
      </div>
    </div>

  </div>
</div>

<style>
  .account-charges {
    padding: calc(var(--gap) * 2);
    height: 100%;
    overflow-y: auto;
  }

  .info-section {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    padding: var(--gap);
    grid-column: span 2;
  }

  .charges-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--gap);
    align-items: flex-start;
    margin: 0 auto;
  }

  .charges-left {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap);
  }

  .charges-right {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .charges-left-column {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .charges-right-column {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .charges-content {
    display: none;
  }

  .charges-content h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
  }

  /* Input Section */
  .input-section {
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .section-header-with-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--gap);
  }

  .section-header-with-button h4 {
    margin: 0;
  }

  .copy-info-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .copy-info-btn:hover {
    background: color-mix(in srgb, var(--color-primary) 85%, black 15%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .copy-info-btn i {
    font-size: 0.875rem;
  }

  .input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--gap);
  }

  .form-group-with-help {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .input-help {
    color: #6b7280;
    font-size: 0.875rem;
    font-style: italic;
  }

  /* Calculations Section */
  .calculations-section {
    background-color: var(--color-primary-light);
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: var(--gap);
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .basic-calc-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .thresholds-container {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  /* Entitlements Section */
  .entitlements-section {
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .entitlements-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h4 {
      margin-bottom: 0;
    }
  }
  /* Old table styles removed - now using DataTable component */

  /* Ensure formula notes display properly within DataTable */
  :global(.data-table .formula-note) {
    font-size: 0.75rem;
    color: #6b7280;
    font-style: italic;
    line-height: 1.3;
    display: block;
    margin-bottom: 0.5rem;
  }

  :global(.data-table .value) {
    font-weight: 700;
    font-size: var(--value-size);
    color: #111827;
  }

  /* Reference Information Section */
  .reference-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-bottom: var(--gap); */
  }

  .reference-header h4 {
    margin: 0;
  }

  .reference-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--gap);
  }

  .reference-group h5 {
    color: var(--label-color);
    font-size: var(--label-size);
    font-weight: 600;
    padding: var(--cell-padding);
    margin-bottom: 0;
  }

  /* Reference tables use DataTable component styles */
  :global(.reference-group .data-table-container) {
    background: transparent;
  }

  .reference-link {
    padding: var(--cell-padding);
  }

  .reference-link a {
    color: var(--color-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s ease;
  }

  .reference-link a:hover {
    color: #0c4a6e;
    text-decoration: underline;
  }

  @media (max-width: 1024px) {
    .charges-grid {
      grid-template-columns: 1fr;
    }
    .charges-left, .charges-right {
      width: 100%;
    }
    .charges-left {
      grid-template-columns: 1fr;
    }
    .reference-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .account-charges {
      padding: 1rem;
    }

    .input-grid,
    .basic-calc-grid,
    .reference-grid {
      grid-template-columns: 1fr;
    }

    .entitlements-section {
      padding: 1rem;
    }

    .entitlements-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .section-header-with-button {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .copy-info-btn {
      align-self: flex-end;
      padding: 0.4rem 0.6rem;
      font-size: 0.8rem;
    }
  }
</style> 