<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { suggestedActions } from '$lib/stores';
  import { settings } from '$lib/stores/settings';
  
  const dispatch = createEventDispatcher();
  
  export let notesContent: string = '';
  export let verticalLayout: boolean = false;
  let searchQuery = '';
  
  // Debounce notes content processing for performance
  let debouncedNotesContent = '';
  let debounceTimer: number;
  
  // Scroll arrow state
  let scrollContainer: HTMLElement;
  let canScrollLeft = false;
  let canScrollRight = true;
  
  $: {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      debouncedNotesContent = notesContent;
    }, 300);
  }
  
  // Check scroll position and update arrow states
  function updateScrollArrows() {
    if (!scrollContainer) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
    canScrollLeft = scrollLeft > 0;
    canScrollRight = scrollLeft < scrollWidth - clientWidth - 1; // -1 for rounding
  }
  
  // Scroll left
  function scrollLeft() {
    if (!scrollContainer || !canScrollLeft) return;
    scrollContainer.scrollBy({ left: -240, behavior: 'smooth' });
  }
  
  // Scroll right
  function scrollRight() {
    if (!scrollContainer || !canScrollRight) return;
    scrollContainer.scrollBy({ left: 240, behavior: 'smooth' });
  }
  
  // Initialize scroll arrows when container is available
  function initScrollArrows(element: HTMLElement) {
    scrollContainer = element;
    updateScrollArrows();
    
    // Add scroll event listener
    element.addEventListener('scroll', updateScrollArrows);
    
    // Update on resize
    const resizeObserver = new ResizeObserver(updateScrollArrows);
    resizeObserver.observe(element);
    
    return {
      destroy() {
        element.removeEventListener('scroll', updateScrollArrows);
        resizeObserver.disconnect();
      }
    };
  }

  // Helper function to normalize words for singular/plural matching
  function normalizeWord(word: string): string {
    const lowerWord = word.toLowerCase().trim();
    
    // Remove common suffixes to get word stems
    if (lowerWord.endsWith('ies') && lowerWord.length > 4) {
      return lowerWord.slice(0, -3) + 'y'; // companies -> company
    }
    if (lowerWord.endsWith('es') && lowerWord.length > 3) {
      // Check for words ending in -ches, -shes, -xes, etc.
      if (lowerWord.endsWith('ches') || lowerWord.endsWith('shes') || lowerWord.endsWith('xes')) {
        return lowerWord.slice(0, -2);
      }
      return lowerWord.slice(0, -2); // boxes -> box
    }
    if (lowerWord.endsWith('s') && lowerWord.length > 3) {
      return lowerWord.slice(0, -1); // repairs -> repair
    }
    
    return lowerWord;
  }

  // Helper function to check if two words match (accounting for singular/plural)
  function wordsMatch(word1: string, word2: string): boolean {
    const normalized1 = normalizeWord(word1);
    const normalized2 = normalizeWord(word2);
    
    // Direct match
    if (word1.toLowerCase() === word2.toLowerCase()) return true;
    
    // Normalized match (singular/plural)
    if (normalized1 === normalized2) return true;
    
    // One contains the other (for partial matches)
    if (normalized1.length > 2 && normalized2.length > 2) {
      if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) return true;
    }
    
    return false;
  }

  // Relevance scoring algorithm
  function calculateRelevanceScore(template: any, notesText: string): number {
    if (!notesText || notesText.trim().length < 3) return 0;
    
    let score = 0;
    const lowerNotesText = notesText.toLowerCase();
    const notesWords = lowerNotesText.split(/\s+/).filter(word => word.length > 2);
    
    // Keyword matching (40 points max)
    if (template.keywords) {
      template.keywords.forEach((keyword: string) => {
        const lowerKeyword = keyword.toLowerCase();
        
        // Direct text inclusion (highest score)
        if (lowerNotesText.includes(lowerKeyword)) {
          score += 10;
          return;
        }
        
        // Check for singular/plural and word stem matches
        let foundMatch = false;
        notesWords.forEach(notesWord => {
          if (wordsMatch(notesWord, lowerKeyword)) {
            score += 8; // High score for normalized matches
            foundMatch = true;
          }
        });
        
        // If no normalized match, try partial matching
        if (!foundMatch) {
          notesWords.forEach(notesWord => {
            if (notesWord.includes(lowerKeyword) || lowerKeyword.includes(notesWord)) {
              if (notesWord.length > 2 && lowerKeyword.length > 2) {
                score += 3;
              }
            }
          });
        }
      });
    }
    
    // Title matching (20 points max)
    const lowerTitle = template.title.toLowerCase();
    if (lowerNotesText.includes(lowerTitle)) {
      score += 20;
    } else {
      // Enhanced title matching with singular/plural handling
      const titleWords = lowerTitle.split(/\s+/).filter((word: string) => word.length > 2);
      titleWords.forEach((titleWord: string) => {
        // Check for direct inclusion first
        if (lowerNotesText.includes(titleWord)) {
          score += 8;
          return;
        }
        
        // Check for singular/plural matches
        notesWords.forEach(notesWord => {
          if (wordsMatch(notesWord, titleWord)) {
            score += 6;
          }
        });
      });
    }
    
    // Category matching (20 points max)
    const lowerCategory = template.category.toLowerCase();
    if (lowerNotesText.includes(lowerCategory)) {
      score += 20;
    } else {
      // Enhanced category matching with singular/plural handling
      const categoryWords = lowerCategory.split(/\s+/).filter((word: string) => word.length > 2);
      categoryWords.forEach((categoryWord: string) => {
        // Check for direct inclusion first
        if (lowerNotesText.includes(categoryWord)) {
          score += 8;
          return;
        }
        
        // Check for singular/plural matches
        notesWords.forEach(notesWord => {
          if (wordsMatch(notesWord, categoryWord)) {
            score += 6;
          }
        });
      });
    }
    
    // Text content similarity (20 points max)
    if (template.text) {
      const lowerTemplateText = template.text.toLowerCase();
      const templateWords = lowerTemplateText.split(/\s+/).filter((word: string) => word.length > 3);
      
      let commonWords = 0;
      notesWords.forEach((notesWord: string) => {
        // Check for exact matches first
        if (templateWords.some((templateWord: string) => templateWord === notesWord)) {
          commonWords++;
          return;
        }
        
        // Check for singular/plural matches
        if (templateWords.some((templateWord: string) => wordsMatch(notesWord, templateWord))) {
          commonWords++;
          return;
        }
        
        // Check for partial matches as fallback
        if (templateWords.some((templateWord: string) => 
          templateWord.includes(notesWord) || notesWord.includes(templateWord)
        )) {
          commonWords += 0.5; // Lower score for partial matches
        }
      });
      
      if (notesWords.length > 0) {
        score += (commonWords / notesWords.length) * 20;
      }
    }
    
    return Math.min(Math.round(score), 100);
  }

  // Note templates for suggested actions
  const actionNoteTemplates = {
    'Arr1': '',
    'NSP': 'Notice of Seeking Possession required - arrears exceed 4 weeks rent. Tenant advised of legal proceedings risk if arrears not addressed.',
    'SCRF': 'Court referral being considered due to arrears exceeding 8 weeks rent. Legal action timeline explained to tenant.',
    'APA': 'Alternative Payment Arrangement requested with DWP. Tenant struggling with current payment method. Direct payments needed.',
    'TPD': 'Third Party Deduction application required - arrears exceed 8 weeks rent. Tenant unable to maintain payment arrangement.',
    'IRP': 'Incorrect rent payments identified. Tenant paying less than monthly rent due. Payment adjustment required.',
    'STTC': 'Static arrears - tenant paying full rent but arrears not reducing. Additional payment towards arrears needed.'
  };
  
  // Create dynamic suggested action responses
  $: suggestedActionResponses = $suggestedActions.map(action => ({
    id: `suggested_${action.code}`,
    category: 'Current Suggestions',
    title: `${action.code} (${action.priority}): ${action.title}`,
    text: actionNoteTemplates[action.code as keyof typeof actionNoteTemplates] || `${action.responseDescription}`,
    priority: action.priority,
    isCurrentSuggestion: true,
    keywords: [] // Suggested actions don't have keywords for now
  }));

  interface ResponseType {
    id: string;
    category: string;
    title: string;
    text: string;
    priority?: string;
    isCurrentSuggestion: boolean;
    keywords?: string[];
    relevanceScore: number;
    relevanceLevel: 'high' | 'medium' | 'low';
  }

  // Combine suggested actions with standard responses from settings and add relevance scores
  $: allResponsesWithRelevance = [
    ...suggestedActionResponses,
    ...$settings.responseTemplates.map(r => ({
      ...r,
      isCurrentSuggestion: false,
      priority: undefined,
    }))
  ].map((response): ResponseType => ({
    ...response,
    isCurrentSuggestion: response.isCurrentSuggestion ?? false,
    priority: response.priority ?? undefined,
    relevanceScore: calculateRelevanceScore(response, debouncedNotesContent),
    relevanceLevel: getRelevanceLevel(calculateRelevanceScore(response, debouncedNotesContent)),
  }));

  // Helper function to determine relevance level for styling
  function getRelevanceLevel(score: number): 'high' | 'medium' | 'low' {
    if (score >= 60) return 'high';
    if (score >= 30) return 'medium';
    return 'low';
  }

  // Filter responses based on search query
  $: filteredResponses = allResponsesWithRelevance.filter(response => 
    response.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    response.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
    response.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (response.keywords?.some(keyword => 
      keyword.toLowerCase().includes(searchQuery.toLowerCase())) ?? false)
  );

  // Flat, ordered list: Current Suggestions, then Relevant Suggestions, then the rest
  $: orderedResponses = (() => {
    const current = filteredResponses.filter(r => r.category === 'Current Suggestions');
    const relevant = filteredResponses.filter(r => r.category !== 'Current Suggestions' && r.relevanceScore >= 30 && debouncedNotesContent.trim().length >= 3);
    const rest = filteredResponses.filter(r => r.category !== 'Current Suggestions' && (r.relevanceScore < 30 || debouncedNotesContent.trim().length < 3));
    // Remove duplicates (by id)
    const seen = new Set();
    const deduped = [...current, ...relevant, ...rest].filter(r => {
      if (seen.has(r.id)) return false;
      seen.add(r.id);
      return true;
    });
    return deduped;
  })();

  function handleResponseClick(response: any) {
    dispatch('responseSelected', {
      text: response.text,
      title: response.title
    });
  }

  function clearSearch() {
    searchQuery = '';
  }
</script>

<div class="standard-responses" class:horizontal-bar={!verticalLayout} class:vertical-bar={verticalLayout}>
  <div class="search-container" class:search-bar-horizontal={!verticalLayout} class:search-bar-vertical={verticalLayout}>
    <div class="search-input-wrapper">
      <i class="fas fa-search search-icon"></i>
      <input
        type="text"
        placeholder="Search..."
        bind:value={searchQuery}
        class="search-input"
      />
      {#if searchQuery}
        <button class="clear-search" on:click={clearSearch}>
          <i class="fas fa-times"></i>
        </button>
      {/if}
    </div>
  </div>
  
  {#if !verticalLayout}
    <!-- Scroll arrows for horizontal layout only -->
    <button 
      class="scroll-arrow scroll-arrow-left"
      class:disabled={!canScrollLeft}
      on:click={scrollLeft}
      disabled={!canScrollLeft}
      title="Scroll left"
    >
      <i class="fas fa-chevron-left"></i>
    </button>
    
    <button 
      class="scroll-arrow scroll-arrow-right"
      class:disabled={!canScrollRight}
      on:click={scrollRight}
      disabled={!canScrollRight}
      title="Scroll right"
    >
      <i class="fas fa-chevron-right"></i>
    </button>
  {/if}

  <div class="responses-list" class:horizontal-list={!verticalLayout} class:vertical-list={verticalLayout} use:initScrollArrows>
    {#if orderedResponses.length > 0}
      {#each orderedResponses as response}
        <button 
          class="response-item"
          class:suggested-action={response.isCurrentSuggestion}
          class:priority-urgent={response.isCurrentSuggestion && response.priority === 'urgent'}
          class:priority-high={response.isCurrentSuggestion && response.priority === 'high'}
          class:priority-medium={response.isCurrentSuggestion && response.priority === 'medium'}
          class:priority-low={response.isCurrentSuggestion && response.priority === 'low'}
          class:relevance-high={response.relevanceLevel === 'high'}
          class:relevance-medium={response.relevanceLevel === 'medium'}
          class:relevance-low={response.relevanceLevel === 'low'}
          on:click={() => handleResponseClick(response)}
          title="Click to add to notes"
        >
          <div class="response-header">
            <div class="response-title">{response.title}</div>
            {#if response.relevanceScore > 30}
              <div class="relevance-indicator">
                {#if response.relevanceLevel === 'high'}
                  <span class="relevance-badge high">🎯 {response.relevanceScore}%</span>
                {:else if response.relevanceLevel === 'medium'}
                  <span class="relevance-badge medium">⚡ {response.relevanceScore}%</span>
                {/if}
              </div>
            {/if}
          </div>
          {#if response.text && response.text.trim().length > 0}
            <div class="response-preview">{response.text.substring(0, 80)}...</div>
          {/if}
        </button>
      {/each}
    {:else}
      <div class="no-results">
        <i class="fas fa-search"></i>
        <p>No responses found</p>
      </div>
    {/if}
  </div>
</div>

<style>
  .standard-responses.horizontal-bar {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    height: 150px;
    /* min-height: 120px; */
    /* max-height: 200px; */
    background: white;
    border: 1px solid var(--border);
    border-radius: 0.375rem 0.375rem 0 0;
    overflow: visible;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    padding: 0;
    position: relative;
  }
  .search-bar-horizontal {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -100%);
    min-width: 320px;
    max-width: 400px;
    width: 60%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    border: 1px solid var(--border);
    border-radius: 1rem 1rem 0 0;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    padding: .75rem;
    z-index: 10;
  }
  .search-input-wrapper {
    position: relative;
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
  }
  .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 1rem;
    z-index: 1;
    pointer-events: none;
  }
  .search-input {
    width: 100%;
    height: 100%;
    padding: 0.5rem 2.2rem 0.5rem 2.2rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
    background: #fff;
    box-sizing: border-box;
    transition: border-color 0.2s;
    outline: none;
    display: flex;
    align-items: center;
  }
  .search-input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary-light);
  }
  .clear-search {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    width: 28px;
  }
  .clear-search:hover {
    color: #374151;
    background: #f3f4f6;
  }
  .horizontal-list {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 0.5rem;
    overflow-x: auto;
    overflow-y: hidden;
    width: 100%;
    height: 100%;
    padding: 0.5rem 1rem 0.5rem 0.5rem;
    scrollbar-color: #cbd5e1 #f1f5f9;
    scrollbar-width: thin;
  }
  .horizontal-list::-webkit-scrollbar {
    height: 10px;
    background: #f1f5f9;
  }
  .horizontal-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
  }
  .horizontal-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
  .response-item {
    min-width: 240px;
    max-width: 320px;
    height: 110px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    white-space: normal;
    background: white;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  }
  .response-item:hover {
    border-color: var(--color-primary);
    background: var(--color-primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .response-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.125rem;
    width: 100%;
    align-items: center;
  }
  .response-title {
    font-weight: 600;
    color: #111827;
    font-size: 0.75rem;
    flex: 1;
  }
  .response-preview {
    font-size: 0.625rem;
    color: #6b7280;
    line-height: 1.3;
    font-weight: 400;
  }
  .relevance-indicator {
    flex-shrink: 0;
  }
  .relevance-badge {
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    gap: 0.125rem;
  }
  .relevance-badge.high {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
  }
  .relevance-badge.medium {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
  }
  .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    text-align: center;
    width: 100%;
    color: #6b7280;
  }
  .no-results i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
  }
  .no-results p {
    margin: 0.5rem 0;
    font-weight: 500;
  }
  .no-results small {
    opacity: 0.7;
  }
  .response-item.suggested-action {
    font-weight: 600;
    border-width: 2px;
  }
  .response-item.suggested-action.priority-urgent {
    border-color: var(--color-red);
    background: var(--color-red-light);
  }
  .response-item.suggested-action.priority-urgent:hover {
    border-color: var(--color-red);
    background: color-mix(in srgb, var(--color-red) 15%, white 85%);
  }
  .response-item.suggested-action.priority-high {
    border-color: var(--color-orange);
    background: var(--color-orange-light);
  }
  .response-item.suggested-action.priority-high:hover {
    border-color: var(--color-orange);
    background: color-mix(in srgb, var(--color-orange) 15%, white 85%);
  }
  .response-item.suggested-action.priority-medium {
    border-color: var(--color-yellow);
    background: var(--color-yellow-light);
  }
  .response-item.suggested-action.priority-medium:hover {
    border-color: var(--color-yellow);
    background: color-mix(in srgb, var(--color-yellow) 15%, white 85%);
  }
  .response-item.suggested-action.priority-low {
    border-color: var(--color-green);
    background: var(--color-green-light);
  }
  .response-item.suggested-action.priority-low:hover {
    border-color: var(--color-green);
    background: color-mix(in srgb, var(--color-green) 15%, white 85%);
  }
  
  /* Scroll arrows */
  .scroll-arrow {
    position: absolute;
    top: 42%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: white;
    color: var(--color-grey);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border);
    transition: all 0.2s ease;
    z-index: 5;
  }
  
  .scroll-arrow:hover:not(.disabled) {
    /* background: var(--color-primary); */
    color: black;
    /* border-color: var(--color-primary); */
    transform: translateY(-50%) scale(1.05);
  }
  
  .scroll-arrow.disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: #f9fafb;
    color: #9ca3af;
  }
  
  .scroll-arrow-left {
    left: 12px;
  }
  
  .scroll-arrow-right {
    right: 12px;
  }

  /* Vertical layout styles */
  .standard-responses.vertical-bar {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;
    padding: 0;
    position: relative;
  }

  .search-bar-vertical {
    width: 100%;
    padding: 0.75rem;
    background: #f8fafc;
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: 0;
    right: 400px;
    z-index: 10;
  }

  .responses-list.vertical-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .vertical-list .response-item {
    width: 100%;
    margin: 0;
    flex-shrink: 0;
    text-align: left;
    padding: 0.75rem;
    min-height: auto;
    height: auto;
  }

  .vertical-list .response-title {
    font-size: 0.875rem;
    white-space: normal;
    word-wrap: break-word;
  }

  .vertical-list .response-preview {
    font-size: 0.75rem;
    line-height: 1.3;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
  }

  .vertical-list .response-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .vertical-list .relevance-indicator {
    margin-top: 0.25rem;
  }

  .vertical-list .relevance-badge {
    font-size: 0.75rem;
  }
</style>