<script lang="ts">
  import { onMount } from 'svelte';
  import type { CaseDetailTab } from '$lib/stores';
  import type { CaseData } from '$lib/utils/api';
  import { authToken } from '$lib/stores';
  import { get } from 'svelte/store';
  
  export let tab: CaseDetailTab;
  
  let caseData: CaseData | null = null;
  let isLoading = false;
  let error: string | null = null;
  
  onMount(async () => {
    await loadCaseData();
  });
  
  async function loadCaseData() {
    if (!tab.caseId) {
      console.log('CaseDetailTab: No case ID provided');
      return;
    }
    
    isLoading = true;
    error = null;
    
    try {
      const { getCase } = await import('$lib/utils/api');
      const token = get(authToken);
      
      if (token) {
        console.log('CaseDetailTab: Loading case data for ID:', tab.caseId);
        caseData = await getCase(tab.caseId, token);
        console.log('CaseDetailTab: Loaded case data:', caseData);
      } else {
        console.log('CaseDetailTab: No auth token, using mock data');
        // Create mock case data for demo
        caseData = {
          id: tab.caseId,
          tenantId: 'mock-tenant-id',
          userId: 'mock-user-id',
          reference: tab.tenantReference,
          currentBalance: 250.75,
          weeklyRent: 85.50,
          tenantMonthlyPayment: 200.00,
          apaHbMonthlyPayment: 100.00,
          tpdMonthlyPayment: 0,
          tenantWeeklyPayment: 46.15,
          benefitsHbWeeklyPayment: 39.35,
          weeksToNextPay: 2,
          paymentDue: 171.00,
          timeframe: 12,
          useWeeklyFrequency: false,
          grossWeeklyRent: 85.50,
          rentComponent: 75.50,
          nonUcServiceChargeTotal: 10.00,
          grossWeeklyRentOverridden: false,
          notes: 'Initial contact made. Tenant explained financial difficulties due to job loss. Follow-up on payment plan needed. Previous calls on 15/01/2024 and 22/01/2024 - tenant cooperative but struggling with current circumstances.',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        };
      }
    } catch (err) {
      console.error('CaseDetailTab: Failed to load case data:', err);
      error = err instanceof Error ? err.message : 'Failed to load case data';
    } finally {
      isLoading = false;
    }
  }
  
  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  }
</script>

<div class="case-detail-tab">
  <div class="tab-header">
    <h3>Case Details: {tab.tenantReference}</h3>
    {#if tab.caseId}
      <span class="case-id">ID: {tab.caseId}</span>
    {/if}
  </div>
  
  {#if isLoading}
    <div class="loading">
      <div class="spinner"></div>
      <p>Loading case details...</p>
    </div>
  {:else if error}
    <div class="error">
      <i class="fas fa-exclamation-triangle"></i>
      <h4>Error Loading Case</h4>
      <p>{error}</p>
      <button on:click={loadCaseData} class="retry-btn">Retry</button>
    </div>
  {:else if caseData}
    <div class="case-content">
      <!-- Case Summary -->
      <div class="section">
        <h4>Case Summary</h4>
        <div class="summary-grid">
          <div class="summary-item">
            <label>Current Balance</label>
            <span class="balance" class:debt={caseData.currentBalance > 0} class:credit={caseData.currentBalance < 0}>
              {formatCurrency(caseData.currentBalance)}
            </span>
          </div>
          <div class="summary-item">
            <label>Weekly Rent</label>
            <span>{formatCurrency(caseData.weeklyRent)}</span>
          </div>
          <div class="summary-item">
            <label>Payment Due</label>
            <span>{formatCurrency(caseData.paymentDue)}</span>
          </div>
          <div class="summary-item">
            <label>Weeks to Next Pay</label>
            <span>{caseData.weeksToNextPay}</span>
          </div>
        </div>
      </div>
      
      <!-- Payment Details -->
      <div class="section">
        <h4>Payment Structure</h4>
        <div class="payment-grid">
          <div class="payment-item">
            <label>Tenant Monthly Payment</label>
            <span>{formatCurrency(caseData.tenantMonthlyPayment)}</span>
          </div>
          <div class="payment-item">
            <label>APA/HB Monthly Payment</label>
            <span>{formatCurrency(caseData.apaHbMonthlyPayment)}</span>
          </div>
          <div class="payment-item">
            <label>TPD Monthly Payment</label>
            <span>{formatCurrency(caseData.tpdMonthlyPayment)}</span>
          </div>
          <div class="payment-item">
            <label>Tenant Weekly Payment</label>
            <span>{formatCurrency(caseData.tenantWeeklyPayment)}</span>
          </div>
          <div class="payment-item">
            <label>Benefits/HB Weekly Payment</label>
            <span>{formatCurrency(caseData.benefitsHbWeeklyPayment)}</span>
          </div>
        </div>
      </div>
      
      <!-- Case Notes -->
      <div class="section">
        <h4>Case Notes</h4>
        {#if caseData.notes}
          <div class="notes-content">
            {caseData.notes}
          </div>
        {:else}
          <p class="no-notes">No notes recorded for this case.</p>
        {/if}
      </div>
      
      <!-- Case Timestamps -->
      <div class="section">
        <h4>Case Information</h4>
        <div class="timestamps">
          <div class="timestamp-item">
            <label>Created</label>
            <span>{formatDate(caseData.createdAt)}</span>
          </div>
          <div class="timestamp-item">
            <label>Last Updated</label>
            <span>{formatDate(caseData.updatedAt)}</span>
          </div>
        </div>
      </div>
    </div>
  {:else}
    <div class="no-case">
      <i class="fas fa-folder-open"></i>
      <h4>No Case Data</h4>
      <p>No case information available for {tab.tenantReference}</p>
    </div>
  {/if}
</div>

<style lang="less">
  .case-detail-tab {
    padding: 1.5rem;
    background: white;
    height: 100%;
    overflow-y: auto;
  }
  
  .tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border);
    
    h3 {
      margin: 0;
      color: var(--color-primary);
      font-size: 1.25rem;
      font-weight: 600;
    }
    
    .case-id {
      font-size: 0.875rem;
      color: #6b7280;
      background: #f3f4f6;
      padding: 0.25rem 0.75rem;
      border-radius: 0.375rem;
      font-family: 'Courier New', monospace;
    }
  }
  
  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    
    .spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid #f3f4f6;
      border-top: 3px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }
    
    p {
      color: #6b7280;
      margin: 0;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .error, .no-case {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    
    i {
      font-size: 2rem;
      color: #f59e0b;
      margin-bottom: 1rem;
    }
    
    h4 {
      margin: 0 0 0.5rem 0;
      color: #111827;
      font-size: 1.125rem;
      font-weight: 600;
    }
    
    p {
      color: #6b7280;
      margin: 0 0 1.5rem 0;
      line-height: 1.5;
    }
    
    .retry-btn {
      background: var(--color-primary);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.2s;
      
      &:hover {
        background: var(--color-secondary);
      }
    }
  }
  
  .case-content {
    .section {
      margin-bottom: 2rem;
      
      h4 {
        margin: 0 0 1rem 0;
        color: #374151;
        font-size: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid var(--border);
        padding-bottom: 0.5rem;
      }
    }
  }
  
  .summary-grid, .payment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .summary-item, .payment-item {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    
    label {
      display: block;
      font-size: 0.75rem;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    
    span {
      font-size: 1rem;
      font-weight: 600;
      color: #111827;
      
      &.balance {
        font-size: 1.125rem;
        
        &.debt {
          color: #dc2626;
        }
        
        &.credit {
          color: #059669;
        }
      }
    }
  }
  
  .notes-content {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    line-height: 1.6;
    color: #374151;
    white-space: pre-wrap;
  }
  
  .no-notes {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 2rem;
  }
  
  .timestamps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .timestamp-item {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    
    label {
      display: block;
      font-size: 0.75rem;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    
    span {
      font-size: 0.875rem;
      color: #374151;
      font-family: 'Courier New', monospace;
    }
  }
  
  /* Responsive design */
  @media (max-width: 768px) {
    .case-detail-tab {
      padding: 1rem;
    }
    
    .tab-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .summary-grid, .payment-grid {
      grid-template-columns: 1fr;
    }
    
    .timestamps {
      grid-template-columns: 1fr;
    }
  }
</style>