<script lang="ts">
  export interface Feature {
    title: string;
    description: string;
    icon: string;
    benefits?: string[];
    image?: string;
    screenshot?: string;
    screenshotFallback?: string;
    video?: string;
    videoWebM?: string;
  }
  
  export let features: Feature[];
  export let columns: number = 3;
  export let cardStyle: 'default' | 'minimal' | 'detailed' = 'default';
  export let showBenefits: boolean = false;
  export let layout: 'grid' | 'row' | 'column-pairs' = 'grid';
  
  $: gridColumns = columns === 2 ? 'repeat(2, 1fr)' : columns === 3 ? 'repeat(3, 1fr)' : 'repeat(auto-fit, minmax(300px, 1fr))';
  
  let lightboxOpen = false;
  let lightboxImage = '';
  let lightboxVideo = '';
  let lightboxTitle = '';
  let lightboxType: 'image' | 'video' = 'image';
  
  function openLightbox(screenshot: string | undefined, video: string | undefined, title: string) {
    if (!screenshot && !video) return;
    
    if (video) {
      lightboxVideo = video;
      lightboxType = 'video';
    } else {
      lightboxImage = screenshot || '';
      lightboxType = 'image';
    }
    
    lightboxTitle = title;
    lightboxOpen = true;
  }
  
  function closeLightbox() {
    lightboxOpen = false;
  }
</script>

<div class="feature-{layout}" style="{layout === 'grid' ? `grid-template-columns: ${gridColumns}` : layout === 'column-pairs' ? 'grid-template-columns: repeat(2, 1fr)' : ''}">
  {#each features as feature}
    <div class="feature-card style-{cardStyle} layout-{layout}">
      {#if (layout === 'row' || layout === 'column-pairs') && (feature.screenshot || feature.video)}
        <button class="feature-screenshot" on:click={() => openLightbox(feature.screenshot, feature.video, feature.title)} type="button" aria-label="View {feature.title} {feature.video ? 'video' : 'screenshot'}">
          {#if feature.video}
            <video 
              muted 
              loop 
              autoplay 
              playsinline 
              preload="none"
              loading="lazy"
              aria-label="{feature.title} demonstration video"
              poster="{feature.screenshot || '/Rent-Collection-Toolkit-Logo.svg'}"
            >
              {#if feature.videoWebM}
                <source src={feature.videoWebM} type="video/webm">
              {/if}
              <source src={feature.video} type="video/mp4">
              Your browser does not support the video tag.
            </video>
          {:else if feature.screenshot}
            <picture>
              <source 
                srcset="{feature.screenshot.replace('.webp', '-300w.webp')} 300w,
                        {feature.screenshot.replace('.webp', '-400w.webp')} 400w,
                        {feature.screenshot.replace('.webp', '-600w.webp')} 600w"
                sizes="(max-width: 768px) 300px, 400px"
                type="image/webp">
              <img 
                src={feature.screenshot.replace('.webp', '-400w.webp')} 
                alt="{feature.title} screenshot" 
                width="400" 
                height="300"
                loading="lazy"
                decoding="async"
                style="max-width: 100%; height: auto;" />
            </picture>
          {/if}
          <div class="screenshot-overlay">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="magnifying-glass">
              {#if feature.video}
                <polygon points="5,3 19,12 5,21"></polygon>
              {:else}
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              {/if}
            </svg>
          </div>
        </button>
      {/if}
      
      <div class="feature-main">
        <div class="feature-icon">
          <i class="fas {feature.icon}"></i>
        </div>
        
        <div class="feature-content">
          <h3 class="feature-title">{feature.title}</h3>
          <p class="feature-description">{feature.description}</p>
          
          {#if showBenefits && feature.benefits}
            <ul class="feature-benefits">
              {#each feature.benefits as benefit}
                <li><i class="fas fa-check"></i> {benefit}</li>
              {/each}
            </ul>
          {/if}
          
          {#if feature.image && layout === 'grid'}
            <div class="feature-image">
              <picture>
                <source 
                  srcset="{feature.image.replace('.webp', '-300w.webp')} 300w,
                          {feature.image.replace('.webp', '-400w.webp')} 400w"
                  sizes="300px"
                  type="image/webp">
                <img 
                  src={feature.image.replace('.webp', '-300w.webp')} 
                  alt={feature.title} 
                  width="300" 
                  height="200"
                  loading="lazy"
                  decoding="async"
                  style="max-width: 100%; height: auto;" />
              </picture>
            </div>
          {/if}
        </div>
      </div>
    </div>
  {/each}
</div>

{#if lightboxOpen}
  <div class="lightbox" on:click={closeLightbox} on:keydown={(e) => e.key === 'Escape' && closeLightbox()} role="dialog" aria-modal="true" aria-label="{lightboxType === 'video' ? 'Video' : 'Image'} lightbox">
    <div class="lightbox-content" on:click|stopPropagation={() => {}}>
      {#if lightboxType === 'video'}
        <video 
          controls 
          autoplay 
          muted 
          loop 
          playsinline 
          preload="metadata"
          aria-label="{lightboxTitle} demonstration video"
        >
          {#if lightboxVideo.replace('.mp4', '.webm')}
            <source src={lightboxVideo.replace('.mp4', '.webm')} type="video/webm">
          {/if}
          <source src={lightboxVideo} type="video/mp4">
          Your browser does not support the video tag.
        </video>
      {:else}
        <picture>
          <source 
            srcset="{lightboxImage.replace('.webp', '-600w.webp')} 600w,
                    {lightboxImage} 800w"
            sizes="(max-width: 768px) 600px, 800px"
            type="image/webp">
          <img 
            src={lightboxImage.replace('.webp', '-600w.webp')} 
            alt="{lightboxTitle} screenshot" 
            width="800" 
            height="600"
            loading="lazy"
            decoding="async"
            style="max-width: 100%; height: auto;" />
        </picture>
      {/if}
      <button class="lightbox-close" on:click={closeLightbox} aria-label="Close">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>
{/if}

<style>
  .feature-grid {
    display: grid;
    gap: 2rem;
    width: 100%;
  }

  .feature-row {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
  }

  .feature-column-pairs {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    width: 100%;
  }

  .feature-card {
    /* background: white; */
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .feature-card.layout-row {
    flex-direction: row;
    align-items: stretch;
    gap: 0;
  }

  .feature-card.layout-column-pairs {
    flex-direction: column;
    background: white;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
  }

  .feature-card:hover {
    transform: translateY(-4px);
  }

  /* Default card style */
  .style-default {
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #f3f4f6;
  }

  .style-default:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  }

  /* Minimal card style */
  .style-minimal {
    padding: 1.5rem;
    border: 2px solid #f3f4f6;
  }

  .style-minimal:hover {
    border-color: var(--color-primary);
    box-shadow: 0 4px 12px rgba(2, 85, 130, 0.1);
  }

  /* Detailed card style */
  .style-detailed {
    /* padding: 2.5rem; */
    /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08); */
    /* border: 1px solid #e5e7eb; */
  }

  /* .style-detailed:hover {
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
  } */

  .feature-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.75rem;
    flex-shrink: 0;
  }

  .style-minimal .feature-icon {
    width: 56px;
    height: 56px;
    font-size: 1.5rem;
  }

  .style-detailed .feature-icon {
    width: 72px;
    height: 72px;
    font-size: 2rem;
  }

  .feature-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-primary);
    margin: 0 0 1rem 0;
    line-height: 1.3;
  }

  .style-detailed .feature-title {
    font-size: 1.375rem;
  }

  .feature-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #6b7280;
    margin: 0 0 1.5rem 0;
    flex: 1;
  }

  .feature-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
  }

  .feature-benefits li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #374151;
  }

  .feature-benefits i {
    color: var(--color-green);
    font-size: 0.875rem;
    flex-shrink: 0;
  }

  .feature-image {
    margin-top: auto;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid #f3f4f6;
  }

  .feature-image img {
    width: 100%;
    height: auto;
    display: block;
  }

  .feature-screenshot {
    flex: 0 0 50%;
    min-height: 400px;
    /* background: #f8f9fa; */
    display: flex;
    padding: 1rem;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    /* border-right: 1px solid #e5e7eb; */
    position: relative;
    cursor: pointer;
    background: none;
    border: none;
    width: 100%;
  }

  .layout-column-pairs .feature-screenshot {
    flex: 0 0 auto;
    min-height: 250px;
    max-height: 300px;
    border-bottom: 1px solid #e5e7eb;
  }

  .feature-screenshot img,
  .feature-screenshot video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    /* padding: 2rem; */
    transition: all 0.3s ease;
  }
  
  .screenshot-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(2, 85, 130, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .magnifying-glass {
    width: 48px;
    height: 48px;
    color: white;
    transform: translateY(10px);
    transition: transform 0.3s ease;
  }
  
  .feature-screenshot:hover .screenshot-overlay {
    opacity: 1;
  }
  
  .feature-screenshot:hover .magnifying-glass {
    transform: translateY(0);
  }
  
  .feature-screenshot:hover img,
  .feature-screenshot:hover video {
    filter: brightness(0.8);
  }

  /* .feature-card.layout-row:hover .feature-screenshot img {
    transform: scale(1.02);
  } */

  .feature-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 3rem;
  }

  .layout-row .feature-main {
    flex-direction: row;
    gap: 2rem;
  }

  .layout-column-pairs .feature-main {
    flex-direction: row;
    gap: 1.5rem;
    padding: 2rem;
  }

  .layout-row .feature-icon {
    margin-bottom: 0;
  }

  .layout-row .feature-content {
    flex: 1;
  }

  .layout-column-pairs .feature-icon {
    margin-bottom: 0;
    align-self: flex-start;
  }

  .layout-column-pairs .feature-content {
    flex: 1;
  }

  .style-default.layout-row {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
  }

  .style-default.layout-row:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
  }

  .feature-card.layout-column-pairs:hover {
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }

  .style-detailed.layout-row .feature-title {
    font-size: 1.5rem;
    margin-bottom: 1.25rem;
  }

  .style-detailed.layout-row .feature-description {
    font-size: 1.05rem;
    margin-bottom: 2rem;
  }

  .style-detailed.layout-row .feature-benefits {
    font-size: 0.95rem;
  }

  .style-detailed.layout-row .feature-benefits li {
    padding: 0.625rem 0;
  }

  /* Mobile responsive */
  @media (max-width: 1024px) {
    .feature-grid {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    .feature-column-pairs {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .feature-card.layout-row {
      flex-direction: column;
    }

    .feature-screenshot {
      flex: 0 0 auto;
      min-height: 200px;
      max-height: 250px;
      border-right: none;
      border-bottom: none;
      margin-bottom: 1rem;
    }

    .feature-screenshot img,
    .feature-screenshot video {
      padding: 1rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .layout-row .feature-main {
      flex-direction: column;
      padding: 2rem 1.5rem;
    }

    .layout-row .feature-icon {
      margin-bottom: 1rem;
      align-self: flex-start;
    }

    .layout-row .feature-content {
      text-align: left;
    }

    .layout-row .feature-title {
      font-size: 1.25rem;
      margin-bottom: 0.75rem;
    }

    .layout-row .feature-description {
      font-size: 0.95rem;
      line-height: 1.5;
      margin-bottom: 1rem;
    }

    .layout-column-pairs .feature-main {
      flex-direction: column;
      padding: 1.5rem;
    }
    
    .screenshot-overlay {
      background: rgba(0, 0, 0, 0.3);
    }
    
    .magnifying-glass {
      width: 40px;
      height: 40px;
    }

    .layout-column-pairs .feature-icon {
      margin-bottom: 1rem;
      align-self: flex-start;
    }

    .layout-column-pairs .feature-screenshot {
      min-height: 180px;
      max-height: 220px;
    }
  }

  @media (max-width: 768px) {
    .feature-grid {
      grid-template-columns: 1fr !important;
      gap: 1.5rem;
    }

    .feature-column-pairs {
      grid-template-columns: 1fr !important;
      gap: 1.5rem;
    }

    .feature-row {
      gap: 1.5rem;
    }

    .feature-card.layout-row {
      background: white;
      border-radius: 1rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 1rem;
      overflow: hidden;
    }

    .style-default,
    .style-detailed {
      padding: 0;
    }

    .style-minimal {
      padding: 1.25rem;
    }

    .feature-icon {
      width: 48px;
      height: 48px;
      font-size: 1.25rem;
    }

    .style-detailed .feature-icon {
      width: 52px;
      height: 52px;
      font-size: 1.5rem;
    }

    .feature-title {
      font-size: 1.125rem;
    }

    .style-detailed .feature-title {
      font-size: 1.25rem;
    }
  }
  
  /* Lightbox styles */
  button.feature-screenshot:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  .lightbox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
    cursor: pointer;
    animation: fadeIn 0.3s ease;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .lightbox-content {
    position: relative;
    max-width: 80vw;
    max-height: 80vh;
    cursor: default;
  }
  
  .lightbox-content img,
  .lightbox-content video {
    width: 100%;
    max-width: 70vw;
    height: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 0.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: scaleIn 0.3s ease;
  }
  
  @keyframes scaleIn {
    from { transform: scale(0.9); }
    to { transform: scale(1); }
  }
  
  .lightbox-close {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 50px;
    height: 50px;
    background: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .lightbox-close:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(2, 85, 130, 0.4);
  }
  
  .lightbox-close svg {
    width: 24px;
    height: 24px;
    color: #374151;
  }
  
  @media (max-width: 768px) {
    .lightbox {
      padding: 1rem;
    }
    
    .lightbox-close {
      top: 10px;
      right: 10px;
      width: 45px;
      height: 45px;
    }
    
    .lightbox-close svg {
      width: 22px;
      height: 22px;
    }
  }
</style>