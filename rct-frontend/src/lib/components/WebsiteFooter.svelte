<script>
  // Get current year dynamically
  const currentYear = new Date().getFullYear();
</script>

<footer class="website-footer">
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-section">
        <div class="footer-brand">
          <img src="/Rent-Collection-Toolkit-Logo-White.svg" alt="Rent Collection Toolkit" class="footer-logo" width="64" height="48" on:error={(e) => {
            const img = e.target;
            img.src = '/favicon.png';
          }}>
          <p class="footer-description">
            Empowering social landlords with efficient rent collection tools designed for modern housing management.
          </p>
        </div>
      </div>
      
<!--       
      <div class="footer-section">
        <h3>Contact</h3>
        <ul>
          <li>
            <span class="contact-label">Email:</span>
            <br><EMAIL>
          </li>
          <li>
            <span class="contact-label">Phone:</span>
            <br>0800 123 4567
          </li>
        </ul>
      </div> -->
    </div>
    
    <div class="footer-bottom">
      <div class="footer-legal">
        <p>&copy; {currentYear} Rent Collection Toolkit. All rights reserved.</p>
        <div class="legal-links">
          <a href="/privacy">Privacy Policy</a>
          <a href="/terms">Terms of Service</a>
          <a href="/accessibility">Accessibility</a>
        </div>
      </div>
    </div>
  </div>
</footer>

<style>
  .website-footer {
    background: var(--color-primary);
    color: #ffffff;
    margin-top: 4rem;
  }

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 2rem 1rem;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .footer-brand {
    max-width: 320px;
  }

  .footer-logo {
    height: 48px;
    width: auto;
    margin-bottom: 1rem;
  }

  .footer-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    line-height: 1.6;
    margin: 0;
  }

  .footer-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: #ffffff;
  }

  .footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-section li {
    margin-bottom: 0.5rem;
  }

  .footer-section a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
  }

  .footer-section a:hover {
    color: #ffffff;
  }

  .contact-label {
    font-size: 0.75rem;
    opacity: 0.6;
    font-weight: 400;
  }


  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 1.5rem;
  }

  .footer-legal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
  }

  .footer-legal p {
    margin: 0;
  }

  .legal-links {
    display: flex;
    gap: 1.5rem;
  }

  .legal-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .legal-links a:hover {
    color: #ffffff;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .footer-container {
      padding: 2rem 1rem 1rem;
    }
    
    .footer-content {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .footer-brand {
      max-width: none;
    }
    
    .footer-legal {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
    
    .legal-links {
      justify-content: center;
    }
  }
</style>