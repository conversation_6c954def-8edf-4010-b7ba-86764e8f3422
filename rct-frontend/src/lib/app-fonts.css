/* App-specific font declarations - loaded only for app routes */

@font-face {
  font-family: 'Martian Grotesk Standard';
  src: url('/fonts/MartianGrotesk-StdRg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Martian Grotesk Condensed';
  src: url('/fonts/MartianGrotesk-CnRg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Martian Mono';
  src: url('/fonts/MartianMono-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Martian Mono';
  src: url('/fonts/MartianMono-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Martian Mono';
  src: url('/fonts/MartianMono-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* App-specific utility classes */
.numeric-value, .currency-value, .percentage-value {
  font-family: 'Martian Mono', 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
  }
}