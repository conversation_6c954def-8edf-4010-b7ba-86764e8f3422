import type { CaseData } from './api';

export interface TenantAnalytics {
  tenantReference: string;
  totalContacts: number;
  firstContactDate: Date;
  lastContactDate: Date;
  currentBalance: number;
  initialBalance: number;
  balanceChange: number;
  percentageChange: number;
  daysSinceFirstContact: number;
  daysSinceLastContact: number;
  averageContactFrequency: number;
  tags: string[];
  lastContactInstanceType?: 'inbound' | 'outbound';
  paymentBehavior: 'improving' | 'stable' | 'declining' | 'unknown';
  escalationLevel: 'NONE' | 'CONTACT_REQUIRED' | 'NSP' | 'LEGAL';
  riskCategory: 'low' | 'medium' | 'high' | 'critical';
}

export interface PortfolioMetrics {
  totalCases: number;
  totalContacts: number;
  averageContactsPerCase: number;
  totalDebt: number;
  averageDebt: number;
  totalBalanceChange: number;
  percentageOfImproving: number;
  averageDaysSinceLastContact: number;
  urgentAttentionCount: number;
  mostActiveDay: string;
  contactTrends: ContactTrend[];
  casesByRisk: Record<string, number>;
  casesByEscalation: Record<string, number>;
  portfolioHealthScore: number;
}

export interface ContactEffectivenessMetrics {
  tenantReference: string;
  effectivenessScore: number;
  totalContacts: number;
  balanceReduction: number;
  reductionPerContact: number;
  avgDaysBetweenContacts: number;
  lastContactType: 'inbound' | 'outbound' | 'unknown';
  responseRate: number;
}

export interface TimeSeriesData {
  date: string;
  totalContacts: number;
  totalDebt: number;
  balanceChange: number;
  uniqueCases: number;
}

export interface ContactTrend {
  day: string;
  contacts: number;
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP'
  }).format(amount);
}

export function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`;
}

function getRiskCategory(balance: number, weeklyRent: number): 'low' | 'medium' | 'high' | 'critical' {
  const weeksInArrears = balance / weeklyRent;
  if (weeksInArrears >= 8) return 'critical';
  if (weeksInArrears >= 4) return 'high';
  if (weeksInArrears >= 2) return 'medium';
  return 'low';
}

function getEscalationLevel(balance: number, weeklyRent: number): 'NONE' | 'CONTACT_REQUIRED' | 'NSP' | 'LEGAL' {
  const weeksInArrears = balance / weeklyRent;
  if (weeksInArrears >= 12) return 'LEGAL';
  if (weeksInArrears >= 8) return 'NSP';
  if (weeksInArrears >= 4) return 'CONTACT_REQUIRED';
  return 'NONE';
}

function getPaymentBehavior(currentBalance: number): 'improving' | 'stable' | 'declining' | 'unknown' {
  // Since we don't have historical data in the current implementation,
  // we'll make educated guesses based on balance
  if (currentBalance <= 0) return 'improving';
  if (currentBalance < 100) return 'stable';
  if (currentBalance < 500) return 'declining';
  return 'declining';
}

export function calculatePortfolioMetrics(cases: CaseData[]): PortfolioMetrics {
  if (!cases || cases.length === 0) {
    return {
      totalCases: 0,
      totalContacts: 0,
      averageContactsPerCase: 0,
      totalDebt: 0,
      averageDebt: 0,
      totalBalanceChange: 0,
      percentageOfImproving: 0,
      averageDaysSinceLastContact: 0,
      urgentAttentionCount: 0,
      mostActiveDay: '',
      contactTrends: [],
      casesByRisk: {},
      casesByEscalation: {},
      portfolioHealthScore: 0
    };
  }

  const totalCases = cases.length;
  const totalDebt = cases.reduce((sum, c) => sum + Math.max(0, c.currentBalance), 0);
  const averageDebt = totalDebt / totalCases;
  
  // Estimate contacts based on case activity (mock data)
  const totalContacts = cases.length * 3; // Assume 3 contacts per case on average
  const averageContactsPerCase = totalContacts / totalCases;

  // Risk categorization
  const casesByRisk = cases.reduce((acc, c) => {
    const risk = getRiskCategory(c.currentBalance, c.weeklyRent);
    acc[risk] = (acc[risk] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Escalation categorization
  const casesByEscalation = cases.reduce((acc, c) => {
    const escalation = getEscalationLevel(c.currentBalance, c.weeklyRent);
    acc[escalation] = (acc[escalation] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Portfolio health score (higher is better)
  const lowRiskCases = casesByRisk.low || 0;
  const criticalCases = casesByRisk.critical || 0;
  const portfolioHealthScore = Math.round(((lowRiskCases / totalCases) * 100) - ((criticalCases / totalCases) * 50));

  return {
    totalCases,
    totalContacts,
    averageContactsPerCase,
    totalDebt,
    averageDebt,
    totalBalanceChange: 0, // Would need historical data
    percentageOfImproving: 0, // Would need historical data
    averageDaysSinceLastContact: 5, // Mock estimate
    urgentAttentionCount: criticalCases,
    mostActiveDay: 'Monday', // Mock data
    contactTrends: [], // Would need historical data
    casesByRisk,
    casesByEscalation,
    portfolioHealthScore: Math.max(0, Math.min(100, portfolioHealthScore))
  };
}

export function analyzeContactEffectiveness(cases: CaseData[]): ContactEffectivenessMetrics[] {
  return cases.map(c => {
    // Mock contact effectiveness metrics based on case data
    const estimatedContacts = Math.floor(Math.random() * 10) + 1;
    const balanceReduction = Math.random() * 200 - 100; // Random between -100 and 100
    const reductionPerContact = balanceReduction / estimatedContacts;
    const responseRate = Math.random() * 0.8 + 0.2; // Between 20% and 100%

    return {
      tenantReference: c.reference,
      effectivenessScore: Math.round((responseRate * 50) + (Math.max(0, balanceReduction) / 10)),
      totalContacts: estimatedContacts,
      balanceReduction,
      reductionPerContact,
      avgDaysBetweenContacts: Math.floor(Math.random() * 14) + 1,
      lastContactType: Math.random() > 0.5 ? 'outbound' : 'inbound',
      responseRate
    };
  });
}

export function getUrgentAttentionCases(cases: CaseData[]): TenantAnalytics[] {
  return cases
    .filter(c => c.currentBalance > 0)
    .map(c => {
      const risk = getRiskCategory(c.currentBalance, c.weeklyRent);
      const escalation = getEscalationLevel(c.currentBalance, c.weeklyRent);
      const daysSinceUpdate = Math.floor((Date.now() - new Date(c.updatedAt).getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        tenantReference: c.reference,
        totalContacts: Math.floor(Math.random() * 5) + 1,
        firstContactDate: new Date(c.createdAt),
        lastContactDate: new Date(c.updatedAt),
        currentBalance: c.currentBalance,
        initialBalance: c.currentBalance + (Math.random() * 100), // Estimate
        balanceChange: -(Math.random() * 100),
        percentageChange: -10,
        daysSinceFirstContact: Math.floor((Date.now() - new Date(c.createdAt).getTime()) / (1000 * 60 * 60 * 24)),
        daysSinceLastContact: daysSinceUpdate,
        averageContactFrequency: 7,
        tags: [],
        paymentBehavior: getPaymentBehavior(c.currentBalance),
        escalationLevel: escalation,
        riskCategory: risk
      };
    })
    .filter(t => t.riskCategory === 'critical' || t.riskCategory === 'high' || t.daysSinceLastContact > 7)
    .sort((a, b) => {
      // Sort by risk (critical first) then by days since last contact
      const riskOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      if (riskOrder[a.riskCategory] !== riskOrder[b.riskCategory]) {
        return riskOrder[b.riskCategory] - riskOrder[a.riskCategory];
      }
      return b.daysSinceLastContact - a.daysSinceLastContact;
    });
}

export function generateTimeSeriesData(cases: CaseData[], days: number): TimeSeriesData[] {
  const data: TimeSeriesData[] = [];
  const totalDebt = cases.reduce((sum, c) => sum + Math.max(0, c.currentBalance), 0);
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Mock time series data with some variation
    const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
    const contacts = Math.floor(cases.length * 0.3 * (1 + variation)); // ~30% of cases contacted per day
    const debtVariation = totalDebt * variation;
    
    data.push({
      date: date.toISOString().split('T')[0],
      totalContacts: Math.max(0, contacts),
      totalDebt: Math.max(0, totalDebt + debtVariation),
      balanceChange: Math.random() * 200 - 100, // Random between -100 and 100
      uniqueCases: Math.floor(cases.length * 0.4 * (1 + variation))
    });
  }
  
  return data;
}