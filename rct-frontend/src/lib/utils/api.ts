export const API_BASE_URL = import.meta.env.VITE_API_URL || 
  (import.meta.env.DEV ? 'https://localhost:7114' : 'https://api.rentcollectiontoolkit.com');

export async function apiCall<T>(
  endpoint: string,
  options: RequestInit = {},
  token?: string
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>)
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // Handle NoContent responses (204) which have no body
    if (response.status === 204) {
      return null as T;
    }

    const data = await response.json();
    console.log(`API response from ${endpoint}:`, data);
    return data;
  } catch (error) {
    console.error(`API error for ${endpoint}:`, error);
    throw error;
  }
}

// Authentication API calls
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  error?: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface RegisterResponse {
  success: boolean;
  token?: string;
  error?: string;
}

export enum UserType {
  Superuser = 1,
  Admin = 2,
  TeamMember = 3
}

export interface User {
  id: string;
  email: string;
  name: string;
  firstName: string;
  lastName: string;
  organisationId: string;
  organisationName: string;
  role: string;
  userType: UserType;
  jobTitle?: string;
  department?: string;
}

export interface CaseData {
  id: string;
  tenantId: string;
  userId: string;
  reference: string;
  name?: string;
  currentBalance: number;
  weeklyRent: number;
  tenantMonthlyPayment: number;
  apaHbMonthlyPayment: number;
  tpdMonthlyPayment: number;
  tenantWeeklyPayment: number;
  benefitsHbWeeklyPayment: number;
  weeksToNextPay: number;
  paymentDue: number;
  timeframe: number;
  useWeeklyFrequency: boolean;
  grossWeeklyRent: number;
  rentComponent: number;
  nonUcServiceChargeFormula?: string;
  nonUcServiceChargeTotal: number;
  grossWeeklyRentOverridden: boolean;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCaseRequest {
  reference: string;
  name?: string;
  currentBalance: number;
  weeklyRent: number;
  tenantMonthlyPayment: number;
  apaHbMonthlyPayment: number;
  tpdMonthlyPayment: number;
  tenantWeeklyPayment: number;
  benefitsHbWeeklyPayment: number;
  weeksToNextPay: number;
  paymentDue: number;
  timeframe: number;
  grossWeeklyRent: number;
  rentComponent: number;
  nonUcServiceChargeFormula?: string;
  nonUcServiceChargeTotal: number;
  grossWeeklyRentOverridden: boolean;
  notes: string;
}

export interface UpdateCaseRequest {
  reference?: string;
  name?: string;
  currentBalance?: number;
  weeklyRent?: number;
  tenantMonthlyPayment?: number;
  apaHbMonthlyPayment?: number;
  tpdMonthlyPayment?: number;
  tenantWeeklyPayment?: number;
  benefitsHbWeeklyPayment?: number;
  weeksToNextPay?: number;
  paymentDue?: number;
  timeframe?: number;
  useWeeklyFrequency?: boolean;
  grossWeeklyRent?: number;
  rentComponent?: number;
  nonUcServiceChargeFormula?: string;
  nonUcServiceChargeTotal?: number;
  grossWeeklyRentOverridden?: boolean;
  notes?: string;
}

// Authentication functions
export async function login(request: LoginRequest): Promise<LoginResponse> {
  const url = `${API_BASE_URL}/Auth/Login`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    // For login, we want to handle both success and failure responses
    const data = await response.json();
    console.log('Login API response:', data);
    
    // If the HTTP request failed but we got a response, check if it's an auth error
    if (!response.ok) {
      return {
        success: false,
        error: data.message || data.error || `Login failed: ${response.status} ${response.statusText}`
      };
    }
    
    return data;
  } catch (error) {
    console.error('Login network error:', error);
    return {
      success: false,
      error: 'Network error. Please check your connection and try again.'
    };
  }
}

export async function register(request: RegisterRequest): Promise<RegisterResponse> {
  const url = `${API_BASE_URL}/Auth/Register`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    // For register, we want to handle both success and failure responses
    const data = await response.json();
    console.log('Register API response:', data);
    
    // If the HTTP request failed but we got a response, check if it's an auth error
    if (!response.ok) {
      return {
        success: false,
        error: data.message || data.error || `Registration failed: ${response.status} ${response.statusText}`
      };
    }
    
    return data;
  } catch (error) {
    console.error('Register network error:', error);
    return {
      success: false,
      error: 'Network error. Please check your connection and try again.'
    };
  }
}

export async function getUser(token: string): Promise<User> {
  return await apiCall<User>('/User/GetUser', {
    method: 'GET'
  }, token);
}

export interface UpdateUserRequest {
  firstName: string;
  lastName: string;
  jobTitle?: string;
  department?: string;
}

export async function updateUser(request: UpdateUserRequest, token: string): Promise<User> {
  return await apiCall<User>('/User/UpdateUser', {
    method: 'PUT',
    body: JSON.stringify(request)
  }, token);
}

// Case management functions
export async function getCases(token: string): Promise<CaseData[]> {
  return await apiCall<CaseData[]>('/Case/GetCases', {
    method: 'GET'
  }, token);
}

export async function getCase(id: string, token: string): Promise<CaseData> {
  return await apiCall<CaseData>(`/Case/GetCase/${id}`, {
    method: 'GET'
  }, token);
}

export async function createCase(request: CreateCaseRequest, token: string): Promise<CaseData> {
  return await apiCall<CaseData>('/Case/CreateCase', {
    method: 'POST',
    body: JSON.stringify(request)
  }, token);
}

export async function updateCase(id: string, request: UpdateCaseRequest, token: string): Promise<void> {
  return await apiCall<void>(`/Case/UpdateCase/${id}`, {
    method: 'PUT',
    body: JSON.stringify(request)
  }, token);
}

export async function deleteCase(id: string, token: string): Promise<void> {
  return await apiCall<void>(`/Case/DeleteCase/${id}`, {
    method: 'DELETE'
  }, token);
}

// Case Notes update interface and function
export interface UpdateCaseNotesRequest {
  notes: string;
}

export async function updateCaseNotes(id: string, request: UpdateCaseNotesRequest, token: string): Promise<void> {
  return await apiCall<void>(`/Case/UpdateCaseNotes/${id}`, {
    method: 'PUT',
    body: JSON.stringify(request)
  }, token);
}

export async function searchCaseByReference(reference: string, token: string): Promise<CaseData> {
  return await apiCall<CaseData>(`/Case/SearchByReference/${encodeURIComponent(reference)}`, {
    method: 'GET'
  }, token);
}

// Tenant management functions
export interface TenantSummary {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  userCount: number;
  caseCount: number;
}

export interface TenantData {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  userCount: number;
  caseCount: number;
  users: UserSummary[];
}

export interface PaginatedTenants {
  tenants: TenantData[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface UserSummary {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: UserType;
  jobTitle: string;
  department: string;
}

export async function getTenantsSummary(token: string): Promise<TenantSummary[]> {
  return await apiCall<TenantSummary[]>('/Tenant/summary', {
    method: 'GET'
  }, token);
}

export async function getTenants(token: string, page: number = 1, pageSize: number = 20): Promise<PaginatedTenants> {
  return await apiCall<PaginatedTenants>(`/Tenant?page=${page}&pageSize=${pageSize}`, {
    method: 'GET'
  }, token);
}

export async function getTenant(id: string, token: string): Promise<TenantData> {
  return await apiCall<TenantData>(`/Tenant/${id}`, {
    method: 'GET'
  }, token);
}