import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit()],
	build: {
		cssCodeSplit: true,
		rollupOptions: {
			output: {
				// Optimize chunking to reduce unused JavaScript
				manualChunks(id) {
					// Vendor libraries
					if (id.includes('node_modules')) {
						if (id.includes('lucide-svelte')) {
							return 'vendor-icons';
						}
						return 'vendor';
					}

					// Group footer and below-fold components for lazy loading
					if (id.includes('WebsiteFooter') || id.includes('FAQItem')) {
						return 'below-fold';
					}

					// Group feature components that aren't critical for LCP
					if (id.includes('FeatureGrid') || id.includes('AnalyticsDashboard')) {
						return 'features';
					}

					// Group admin/app components separately from landing page
					if (id.includes('/app/') || id.includes('/admin/')) {
						return 'app-components';
					}
				},
				// Optimize asset naming for better caching
				assetFileNames: (assetInfo) => {
					const info = assetInfo.name.split('.');
					const ext = info[info.length - 1];
					if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
						return `assets/images/[name]-[hash][extname]`;
					}
					if (/css/i.test(ext)) {
						return `assets/css/[name]-[hash][extname]`;
					}
					return `assets/[name]-[hash][extname]`;
				},
				chunkFileNames: 'assets/js/[name]-[hash].js',
				entryFileNames: 'assets/js/[name]-[hash].js'
			}
		},
		// Tree shake unused code more aggressively
		minify: 'terser',
		terserOptions: {
			compress: {
				drop_console: true,
				drop_debugger: true,
				pure_funcs: ['console.log'],
				// Remove unused code more aggressively
				dead_code: true,
				unused: true,
				// Optimize for size
				passes: 2
			},
			mangle: {
				// Mangle property names for better compression
				properties: {
					regex: /^_/
				}
			}
		},
		// Enable source maps for production debugging if needed
		sourcemap: false,
		// Optimize chunk size
		chunkSizeWarningLimit: 1000
	},
	// Optimize dev server for better performance
	server: {
		fs: {
			// Allow serving files from one level up to the project root
			allow: ['..']
		}
	}
});
