import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit()],
	build: {
		cssCodeSplit: true,
		rollupOptions: {
			output: {
				// Optimize chunking to reduce unused JavaScript
				manualChunks(id) {
					// Vendor libraries
					if (id.includes('node_modules')) {
						if (id.includes('lucide-svelte')) {
							return 'vendor-icons';
						}
						return 'vendor';
					}
					
					// Group footer and below-fold components
					if (id.includes('WebsiteFooter') || id.includes('FAQItem')) {
						return 'below-fold';
					}
					
					// Group feature components that aren't critical
					if (id.includes('FeatureGrid') || id.includes('AnalyticsDashboard')) {
						return 'features';
					}
					
					// Group admin/app components separately from landing page
					if (id.includes('/app/') || id.includes('/admin/')) {
						return 'app-components';
					}
				}
			}
		},
		// Tree shake unused code more aggressively
		minify: 'terser',
		terserOptions: {
			compress: {
				drop_console: true,
				drop_debugger: true,
				pure_funcs: ['console.log']
			}
		}
	}
});
