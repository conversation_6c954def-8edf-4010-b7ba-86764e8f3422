# Video Optimization Guide

## Current Video File Sizes
- Hero video: 2.0MB (`rent-collection-toolkit-video.mp4`)
- Feature video: 7.7MB (`real-time-data-sync.mp4`)

## Recommended Video Compression Settings

### For Hero Background Video
Since this is a background video, you can be more aggressive with compression:

```bash
# Using ffmpeg to compress the hero video
ffmpeg -i rent-collection-toolkit-video.mp4 \
  -c:v libx264 \
  -preset slow \
  -crf 28 \
  -vf "scale=1280:-2" \
  -movflags +faststart \
  -an \
  rent-collection-toolkit-video-compressed.mp4
```

### For Feature Demonstration Videos
These need better quality since users view them in detail:

```bash
# Compress feature videos while maintaining quality
ffmpeg -i real-time-data-sync.mp4 \
  -c:v libx264 \
  -preset slow \
  -crf 23 \
  -vf "scale=1920:-2" \
  -movflags +faststart \
  real-time-data-sync-compressed.mp4
```

### Create WebM Versions for Better Browser Support
```bash
# Convert to WebM for modern browsers
ffmpeg -i input.mp4 \
  -c:v libvpx-vp9 \
  -crf 30 \
  -b:v 0 \
  output.webm
```

## Additional Optimization Techniques Implemented

1. **Lazy Loading**: Videos now load only when visible using Intersection Observer
2. **Preload="none"**: Prevents automatic video download on page load
3. **Poster Images**: Uses logo as placeholder while video loads
4. **data-src Attributes**: Video sources are loaded dynamically

## Further Recommendations

1. **Use a CDN**: Host videos on a CDN for faster global delivery
2. **Create Multiple Resolutions**: Serve different video sizes based on device
3. **Implement Adaptive Bitrate**: Use HLS or DASH for streaming
4. **Optimize Poster Images**: Create optimized poster images instead of using logos

## Target File Sizes
- Hero video: < 500KB (background, can be lower quality)
- Feature videos: < 2MB each (need good quality for demonstrations)