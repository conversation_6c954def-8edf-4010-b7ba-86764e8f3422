<script lang="ts">
  import { updateRentCalculatorData, currentInstanceData, highestPriorityAction, suggestedActions } from '$lib/stores';
  import NotesSection from './NotesSection.svelte';
  import OutputGroup from './OutputGroup.svelte';
  import FormGroup from './FormGroup.svelte';
  import DataTable from './DataTable.svelte';
  
  export const isFullView: boolean = false;
  
  // Local state for form inputs
  let currentBalance = 0;
  let weeklyRent = 0;
  let tenantMonthlyPaymentFormula = ''; // Formula string for display
  let apaHbMonthlyPayment = 0;
  let tpdMonthlyPayment = 0;
  let tenantWeeklyPayment = 0;
  let benefitsHbWeeklyPayment = 0;
  
  // Reactive data arrays for tables
  let mainCalculationsRows: any[] = [];
  let arrearsRows: any[] = [];
  
  // Subscribe to current instance data
  $: if ($currentInstanceData) {
    const data = $currentInstanceData.rentCalculator;
    currentBalance = data.currentBalance;
    weeklyRent = data.weeklyRent;
    tenantMonthlyPaymentFormula = data.tenantMonthlyPaymentFormula || (data.tenantMonthlyPayment > 0 ? data.tenantMonthlyPayment.toString() : '');
    apaHbMonthlyPayment = data.apaHbMonthlyPayment;
    tpdMonthlyPayment = data.tpdMonthlyPayment;
    tenantWeeklyPayment = data.tenantWeeklyPayment;
    benefitsHbWeeklyPayment = data.benefitsHbWeeklyPayment;
  }
  
  // Get ArrangementPlanner data for arrears calculation
  $: arrangementData = $currentInstanceData?.arrangementPlanner;
  $: weeksToNextPay = arrangementData?.weeksToNextPay || 0;
  $: paymentDue = arrangementData?.paymentDue || 0;

  // Action/Contact required logic
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;
  $: actionRequired = rentCalculatorData?.currentBalance > 0;
  
  // Calculation functions
  function calculateMonthlyRent(): number {
    return weeklyRent * 52 / 12;
  }
  
  function calculateTotalMonthlyPayments(): number {
    const tenantMonthlyValue = evaluateMathExpression(tenantMonthlyPaymentFormula);
    return tenantMonthlyValue + apaHbMonthlyPayment + tpdMonthlyPayment + 
           (tenantWeeklyPayment * 52 / 12) + (benefitsHbWeeklyPayment * 52 / 12);
  }
  
  function calculateTotalWeeklyPayments(): number {
    const tenantMonthlyValue = evaluateMathExpression(tenantMonthlyPaymentFormula);
    return tenantWeeklyPayment + benefitsHbWeeklyPayment + 
           (tenantMonthlyValue * 12 / 52) + (apaHbMonthlyPayment * 12 / 52) + 
           (tpdMonthlyPayment * 12 / 52);
  }
  
  function calculateMonthlyShortfallSurplus(): number {
    return calculateTotalMonthlyPayments() - calculateMonthlyRent();
  }
  
  function calculateWeeklyShortfallSurplus(): number {
    return calculateTotalWeeklyPayments() - weeklyRent;
  }
  
  function calculateTenantContributionMonthly(): number {
    const tenantMonthlyValue = evaluateMathExpression(tenantMonthlyPaymentFormula);
    return tenantMonthlyValue + (tenantWeeklyPayment * 52 / 12);
  }
  
  function calculateTenantContributionWeekly(): number {
    const tenantMonthlyValue = evaluateMathExpression(tenantMonthlyPaymentFormula);
    return tenantWeeklyPayment + (tenantMonthlyValue * 12 / 52);
  }
  
  function calculateBenefitsContributionMonthly(): number {
    return apaHbMonthlyPayment + tpdMonthlyPayment + (benefitsHbWeeklyPayment * 52 / 12);
  }
  
  function calculateBenefitsContributionWeekly(): number {
    return benefitsHbWeeklyPayment + ((apaHbMonthlyPayment + tpdMonthlyPayment) * 12 / 52);
  }

  function calculateTotalPaidWeekly(): number {
    return calculateTenantContributionWeekly() + calculateBenefitsContributionWeekly();
  }

  function calculateTotalPaidMonthly(): number {
    return calculateTenantContributionMonthly() + calculateBenefitsContributionMonthly();
  }
  
  // Calculate arrears after next payment (same as ArrangementPlanner)
  function calculateArrearsAfterNextPayment(): number {
    return currentBalance + (weeklyRent * weeksToNextPay) - paymentDue;
  }
  
  function calculateArrearsClearDate(): string {
    const monthlyShortfall = calculateMonthlyShortfallSurplus();
    const arrearsAfterNext = calculateArrearsAfterNextPayment();
    
    if (monthlyShortfall <= 0 || arrearsAfterNext <= 0) {
      return 'N/A';
    }
    
    const monthsToClearing = Math.ceil(arrearsAfterNext / monthlyShortfall);
    const clearingDate = new Date();
    clearingDate.setMonth(clearingDate.getMonth() + monthsToClearing);
    
    return clearingDate.toLocaleDateString('en-GB', { 
      month: 'long', 
      year: 'numeric' 
    });
  }
  
  function calculate4WeeksArrears(): number {
    return weeklyRent * 4;
  }
  
  function calculate8WeeksArrears(): number {
    return weeklyRent * 8;
  }
  
  function calculateRefund(): number {
    if (currentBalance >= 0) return 0;
    return Math.abs(currentBalance) - weeklyRent;
  }

  // ============================================================================
  // RESOLUTION SUGGESTION LOGIC
  // ============================================================================
  // This function determines which resolution(s) to suggest based on tenant situation
  // EASY TO MODIFY: Update the conditions below to match exact business rules
  // ============================================================================
  function getSuggestedResolutions(): string[] {
    const resolutions = [];
    const balance = currentBalance;
    const fourWeeksRent = calculate4WeeksArrears();
    const eightWeeksRent = calculate8WeeksArrears();
    
    // ========================================
    // BUSINESS LOGIC FOR RESOLUTIONS
    // Updated to use only current balance and weekly rent
    // ========================================
    
    // NSP (No Significant Payment) - Low debt
    if (balance > 0 && balance < fourWeeksRent) {
      resolutions.push('NSP');
    }
    
    // SCRF (Social Care Referral Fund) - Medium debt
    if (balance >= fourWeeksRent && balance < eightWeeksRent) {
      resolutions.push('SCRF');
    }
    
    // APA (Alternative Payment Arrangement) - High debt
    if (balance >= eightWeeksRent && balance < weeklyRent * 12) {
      resolutions.push('APA');
    }
    
    // TPD (Third Party Deduction) - Very high debt
    if (balance >= weeklyRent * 12) {
      resolutions.push('TPD');
    }
    
    return resolutions;
  }
  
  // Helper function to get resolution descriptions for clarity
  function getResolutionDescription(resolution: string): string {
    switch (resolution) {
      case 'NSP': return 'No Significant Payment - Low debt (under 4 weeks rent)';
      case 'SCRF': return 'Social Care Referral Fund - Medium debt (4-8 weeks rent)';
      case 'APA': return 'Alternative Payment Arrangement - High debt (8-12 weeks rent)';
      case 'TPD': return 'Third Party Deduction - Very high debt (over 12 weeks rent)';
      default: return '';
    }
  }
  
  
  function getShortfallSurplusColor(value: number): string {
    if (value > 0) return 'var(--color-green)'; // Surplus is good (green)
    if (value < 0) return 'var(--color-red)';   // Shortfall is bad (red)
    return '#111827';
  }

  function getShortfallSurplusLabel(value: number): string {
    if (value > 0) return 'Surplus';
    if (value < 0) return 'Shortfall';
    return 'Balance';
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function evaluateMathExpression(value: string): number {
    // Remove leading/trailing spaces and commas
    const cleaned = value.trim().replace(/,/g, '');
    
    // If it's just a number, parse it directly
    if (/^\d*\.?\d+$/.test(cleaned)) {
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    // Try to evaluate as a mathematical expression
    try {
      // Only allow safe mathematical expressions (numbers, +, -, *, /, ., spaces, parentheses)
      if (!/^[\d+\-*/().\s]+$/.test(cleaned)) {
        // If it contains invalid characters, try parsing as regular number
        const parsed = parseFloat(cleaned);
        return isNaN(parsed) ? 0 : parsed;
      }
      
      // Use Function constructor to safely evaluate mathematical expressions
      const result = new Function('return ' + cleaned)();
      return isNaN(result) ? 0 : result;
    } catch (error) {
      // If evaluation fails, try parsing as regular number
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
  }
  
  function sanitizeNumericInput(value: string): number {
    return evaluateMathExpression(value);
  }
  
  function handleInputChange() {
    console.log('RentCalculator: handleInputChange called', {
      currentBalance,
      weeklyRent,
      tenantMonthlyPayment: evaluateMathExpression(tenantMonthlyPaymentFormula),
      tenantMonthlyPaymentFormula,
      apaHbMonthlyPayment,
      tpdMonthlyPayment,
      tenantWeeklyPayment,
      benefitsHbWeeklyPayment
    });
    updateRentCalculatorData({
      currentBalance,
      weeklyRent,
      tenantMonthlyPayment: evaluateMathExpression(tenantMonthlyPaymentFormula),
      tenantMonthlyPaymentFormula,
      apaHbMonthlyPayment,
      tpdMonthlyPayment,
      tenantWeeklyPayment,
      benefitsHbWeeklyPayment
    });
  }
  
  function handleFormGroupInput(event: CustomEvent) {
    const { id, value } = event.detail;
    
    switch(id) {
      case 'currentBalance':
        currentBalance = sanitizeNumericInput(value);
        break;
      case 'weeklyRent':
        weeklyRent = sanitizeNumericInput(value);
        break;
      case 'tenantMonthlyPayment':
        tenantMonthlyPaymentFormula = value; // Store raw value for formula support
        break;
      case 'apaHbMonthlyPayment':
        apaHbMonthlyPayment = sanitizeNumericInput(value);
        break;
      case 'tpdMonthlyPayment':
        tpdMonthlyPayment = sanitizeNumericInput(value);
        break;
      case 'tenantWeeklyPayment':
        tenantWeeklyPayment = sanitizeNumericInput(value);
        break;
      case 'benefitsHbWeeklyPayment':
        benefitsHbWeeklyPayment = sanitizeNumericInput(value);
        break;
    }
    
    handleInputChange();
  }
  
  async function handleRentButton() {
    // Copy preset text as per original specification
    const presetText = `Weekly rent is ${weeklyRent.toFixed(2)}, monthly rent is ${calculateMonthlyRent().toFixed(2)}`;
    try {
      await navigator.clipboard.writeText(presetText);
      console.log('Rent information copied to clipboard');
    } catch (error) {
      console.error('Failed to copy rent information:', error);
    }
  }

  // Data preparation for DataTable components
  $: mainCalculationsColumns = [
    { key: 'label', label: '', type: 'label' as const, align: 'left' as const, width: '1fr' },
    { key: 'weekly', label: 'Weekly', type: 'currency' as const, align: 'right' as const, width: '1fr' },
    { key: 'monthly', label: 'Monthly', type: 'currency' as const, align: 'right' as const, width: '1fr' }
  ];

  $: {
    // Explicitly reference all variables to ensure reactivity
    const deps = [
      weeklyRent,
      currentBalance,
      tenantMonthlyPaymentFormula,
      apaHbMonthlyPayment,
      tpdMonthlyPayment,
      tenantWeeklyPayment,
      benefitsHbWeeklyPayment,
      weeksToNextPay,
      paymentDue
    ];
    
    console.log('RentCalculator: Reactive calculations updating', {
      weeklyRent,
      currentBalance,
      tenantMonthlyPaymentFormula,
      apaHbMonthlyPayment,
      tpdMonthlyPayment,
      tenantWeeklyPayment,
      benefitsHbWeeklyPayment,
      weeklyShortfall: calculateWeeklyShortfallSurplus(),
      monthlyShortfall: calculateMonthlyShortfallSurplus()
    });
    
    mainCalculationsRows = [
      {
        label: getShortfallSurplusLabel(calculateWeeklyShortfallSurplus()),
        weekly: calculateWeeklyShortfallSurplus(),
        monthly: calculateMonthlyShortfallSurplus(),
        weekly_style: `color: ${getShortfallSurplusColor(calculateWeeklyShortfallSurplus())}`,
        monthly_style: `color: ${getShortfallSurplusColor(calculateMonthlyShortfallSurplus())}`
      },
      {
        label: 'Tenant Contribution',
        weekly: calculateTenantContributionWeekly(),
        monthly: calculateTenantContributionMonthly()
      },
      {
        label: 'Benefits Contribution',
        weekly: calculateBenefitsContributionWeekly(),
        monthly: calculateBenefitsContributionMonthly()
      },
      {
        label: 'Total Paid',
        weekly: calculateTotalPaidWeekly(),
        monthly: calculateTotalPaidMonthly()
      }
    ];
  }

  $: arrearsColumns = [
    { key: 'label', label: '', type: 'label' as const, align: 'left' as const, width: '1fr' },
    { key: 'value1', label: '4 Weeks', type: 'currency' as const, align: 'right' as const, width: '1fr' },
    { key: 'value2', label: '8 Weeks', type: 'currency' as const, align: 'right' as const, width: '1fr' }
  ];

  $: {
    // Explicitly reference all variables that affect arrears calculations
    const arrearsDeps = [
      currentBalance,
      weeklyRent,
      tenantMonthlyPaymentFormula,
      apaHbMonthlyPayment,
      tpdMonthlyPayment,
      tenantWeeklyPayment,
      benefitsHbWeeklyPayment,
      weeksToNextPay,
      paymentDue
    ];
    
    arrearsRows = [
      {
        label: 'Arrears Value',
        value1: calculate4WeeksArrears(),
        value2: calculate8WeeksArrears()
      },
      currentBalance < 0 ? {
        label: 'Refund',
        value1: calculateRefund(),
        value2_html: '',
        value1_style: 'color: var(--color-green); grid-column: span 2;'
      } : {
        label: 'Arrears Will Clear',
        value1_html: calculateArrearsClearDate(),
        value1_style: 'grid-column: span 2;',
        value2_html: ''
      }
    ];
  }
</script>

<div class="rent-calculator">
  <div class="calculator-grid">
    <!-- LEFT COLUMN: Inputs + Action/Contact Banner -->
    <div class="calculator-left">
      <!-- Table 1: Input Focus -->
      <div class="calculator-table">
        <div class="table-grid input-table">
          <!-- Row 1: Weekly Rent | Monthly Rent -->
          <div class="table-cell input-cell">
            <FormGroup
              id="weeklyRent"
              label="Weekly Rent"
              value={weeklyRent}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>
          <OutputGroup 
            label="Monthly Rent"
            value={formatCurrency(calculateMonthlyRent())}
            className="table-cell"
            showCurrency={true}
          />

          <!-- Row 2: Current Balance |  Tenant (Monthly) -->
          <div class="table-cell input-cell">
            <FormGroup
              id="currentBalance"
              label="Current Balance"
              value={currentBalance}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>
          <div class="table-cell input-cell">
            <FormGroup
              id="tenantMonthlyPayment"
              label="Tenant (Monthly)"
              value={tenantMonthlyPaymentFormula}
              placeholder="0.00"
              type="text"
              showCurrency={true}
              showFormula={true}
              on:input={handleFormGroupInput}
            />
          </div>

          <!-- Row 3: Tenant (Weekly) | APA (Monthly) -->
          <div class="table-cell input-cell">
            <FormGroup
              id="tenantWeeklyPayment"
              label="Tenant (Weekly)"
              value={tenantWeeklyPayment}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>
          <div class="table-cell input-cell">
            <FormGroup
              id="apaHbMonthlyPayment"
              label="APA (Monthly)"
              value={apaHbMonthlyPayment}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>

          <!-- Row 4: Tenant (Weekly) | TPD (Monthly) -->
          <div class="table-cell input-cell">
            <FormGroup
              id="benefitsHbWeeklyPayment"
              label="HB (Weekly)"
              value={benefitsHbWeeklyPayment}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>
          <div class="table-cell input-cell">
            <FormGroup
              id="tpdMonthlyPayment"
              label="TPD (Monthly)"
              value={tpdMonthlyPayment}
              placeholder="0.00"
              type="number"
              showCurrency={true}
              on:input={handleFormGroupInput}
            />
          </div>
        </div>
      </div>

      <!-- Action Required / Contact Required Section -->
      {#if actionRequired && $suggestedActions.length > 0}
        <div class="action-required-header priority-{$highestPriorityAction}">
          <div class="action-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="action-content">
            <h4>Action Required</h4>
            <p>Arrears of £{(rentCalculatorData?.currentBalance || 0).toFixed(2)} requires immediate contact and action</p>
          </div>
        </div>
      {:else if actionRequired}
        <div class="contact-required-header">
          <div class="contact-icon">
            <i class="fas fa-phone"></i>
          </div>
          <div class="contact-content">
            <h4>Contact Required</h4>
            <p>Balance of £{(rentCalculatorData?.currentBalance || 0).toFixed(2)} requires tenant contact</p>
          </div>
        </div>
      {/if}
    </div>

    <!-- RIGHT COLUMN: Calculations + Recommended Actions -->
    <div class="calculator-right">
      <div class="calculations-table-container">
        <!-- Primary calculations table -->
        <DataTable
          columns={mainCalculationsColumns}
          rows={mainCalculationsRows}
          showHeader={true}
          hoverEffect={true}
        />

        <!-- Additional calculations table -->
        <DataTable
          columns={arrearsColumns}
          rows={arrearsRows}
          showHeader={true}
          hoverEffect={true}
        />
      </div>

      <!-- Recommended Actions Section -->
      {#if $suggestedActions.length > 0}
        <div class="recommended-actions-section">
          <div class="actions-header">
            <h4>Recommended Actions</h4>
          </div>
          <div class="compact-action-cards">
            {#each $suggestedActions as action}
              <div class="compact-action-card priority-{action.priority}">
                <div class="compact-action-header">
                  <span class="compact-action-code">{action.code}</span>
                  <span class="compact-action-priority priority-{action.priority}">
                    {action.priority.toUpperCase()}
                  </span>
                </div>
                <h6 class="compact-action-title">{action.title}</h6>
                <p class="compact-action-description">{action.description}</p>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<style lang="less">
  .rent-calculator {
    padding: var(--gap);
    height: 100%;
    overflow-y: auto;
  }

  .calculator-grid {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: var(--gap);
    align-items: flex-start;
  }

  .calculator-left {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .calculator-right {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .calculator-content {
    display: none;
  }

  /* Calculator Tables */
  .calculator-table {
    /* background-color: white; */
    /* border: 1px solid var(--border); */
    /* border-radius: 0.5rem; */
    // padding: .5rem;

    // margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  /* Table Grid Layout */
  .table-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap);
  }

  /* Calculations Grid Styling */
  .calculations-table-container {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }











  /* Action Required Header Styles */
  .action-required-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    padding: var(--gap);
    border-radius: 12px;
    
    h4 {
      color: white;
    }
  }

  /* Priority-based background colors for Action Required header */
  .action-required-header.priority-urgent {
    background: var(--color-red);
  }

  .action-required-header.priority-high {
    background: var(--color-orange);
  }

  .action-required-header.priority-medium {
    background: var(--color-yellow);
  }

  .action-required-header.priority-low {
    background: var(--color-green);
  }

  .action-icon {
    font-size: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    color: white;
  }

  .action-content {
    display: flex;
    flex-direction: column;
    gap: calc(var(--gap) / 3);
    h4 {
      margin-bottom: 0;
    }
  }

  .action-content p {
    margin: 0;
    font-size: var(--subheader-size);
    opacity: 0.95;
  }

  /* Contact Required Header Styles */
  .contact-required-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--color-secondary);
    color: white;
    padding: var(--gap);
    border-radius: 12px;
    
    h4 {
      color: white;
    }
  }

  .contact-icon {
    font-size: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    color: white;
  }

  .contact-content p {
    margin: 0;
    font-size: 0.95rem;
    opacity: 0.95;
    font-weight: 500;
  }


  /* Recommended Actions Section */
  .recommended-actions-section {
    background: #f8fafc;
    border: 2px solid var(--border);
    border-radius: 12px;
    padding: var(--gap);
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  .actions-header h4 {
    margin: 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
  }

  /* Compact Action Cards - 2 per row */
  .compact-action-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .compact-action-card {
    background: white;
    border-radius: 8px;
    padding: 0.75rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    border-left: 3px solid;
    position: relative;
  }

  .compact-action-card.priority-urgent {
    border-left-color: var(--color-red);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, white 100%);
  }

  .compact-action-card.priority-high {
    border-left-color: var(--color-orange);
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, white 100%);
  }

  .compact-action-card.priority-medium {
    border-left-color: var(--color-yellow);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, white 100%);
  }

  .compact-action-card.priority-low {
    border-left-color: var(--color-green);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, white 100%);
  }

  .compact-action-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .compact-action-code {
    font-family: 'Martian Mono', 'Courier New', monospace;
    font-weight: 700;
    font-size: 0.75rem;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    letter-spacing: 0.05em;
  }

  .compact-action-card.priority-urgent .compact-action-code {
    background: var(--color-red);
  }

  .compact-action-card.priority-high .compact-action-code {
    background: var(--color-orange);
  }

  .compact-action-card.priority-medium .compact-action-code {
    background: var(--color-yellow);
  }

  .compact-action-card.priority-low .compact-action-code {
    background: var(--color-green);
  }

  .compact-action-priority {
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    letter-spacing: 0.05em;
  }

  .compact-action-priority.priority-urgent {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-red);
  }

  .compact-action-priority.priority-high {
    background: rgba(249, 115, 22, 0.1);
    color: var(--color-orange);
  }

  .compact-action-priority.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-yellow);
  }

  .compact-action-priority.priority-low {
    background: rgba(34, 197, 94, 0.1);
    color: var(--color-green);
  }

  .compact-action-title {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
  }

  .compact-action-description {
    margin: 0;
    font-size: 0.75rem;
    color: #475569;
    line-height: 1.4;
  }

  /* Responsive Design */
  @media (max-width: 900px) {
    .calculator-grid {
      grid-template-columns: 1fr;
    }
    .calculator-left, .calculator-right {
      width: 100%;
    }
  }

</style> 