# Intelligent Notes System

The Rent Collection Toolkit features an intelligent response template system that automatically prioritizes and highlights relevant Quick Responses based on what you type in the Notes input field.

## How It Works

### Real-Time Content Analysis
As you type in the Notes textarea, the system continuously analyzes your content and scores each response template for relevance. Templates with meaningful relevance (30%+ score) are automatically moved to a special "Relevant Suggestions" category at the top of the list, making them immediately visible.

### Smart Word Matching
The system uses advanced text matching that handles:
- **Singular/Plural variations**: "repair" matches templates containing "repairs"
- **Word stemming**: "companies" matches "company" 
- **Partial matches**: "pay" matches "payment" and "payments"
- **Common suffixes**: Handles -s, -es, -ies endings intelligently

## Relevance Scoring Algorithm

Each template is scored out of 100 points based on four factors:

### 1. Keyword Matching (40 points max)
- **Exact keyword match**: 10 points
- **Normalized match** (singular/plural): 8 points
- **Partial match**: 3 points

### 2. Title Matching (20 points max)
- **Full title in notes**: 20 points
- **Title word matches**: 8 points (direct), 6 points (normalized)

### 3. Category Matching (20 points max)
- **Full category in notes**: 20 points
- **Category word matches**: 8 points (direct), 6 points (normalized)

### 4. Content Similarity (20 points max)
- Compares notes text with template content
- **Exact word matches**: Full points
- **Normalized matches**: Full points
- **Partial matches**: Half points

## Category Organization

### Relevant Suggestions (30%+ score)
Templates with meaningful relevance are automatically moved to a special "Relevant Suggestions" category that appears at the top of the list, making them immediately visible and easily accessible.

### Current Suggestions
System-generated suggestions based on account data (arrears levels, payment patterns) always appear first when available.

### Standard Categories
Templates below the relevance threshold remain in their original categories (Benefits, Payment Plans, Legal Action, etc.) for easy browsing.

## Visual Indicators

### High Relevance (60%+ score)
- **🎯 Green border** with gradient background
- **"🎯 X%" badge** showing exact relevance percentage
- **Enhanced styling** with prominent shadow
- **Moved to "Relevant Suggestions" category**

### Medium Relevance (30-59% score)
- **⚡ Orange border** with subtle background
- **"⚡ X%" badge** showing relevance percentage
- **Subtle highlighting** to draw attention
- **Moved to "Relevant Suggestions" category**

### Low Relevance (<30% score)
- **Standard appearance** - no special styling
- **Remains in original category** for discoverability

## Usage Examples

### Benefits-Related Notes
**Type:** `tenant reports UC delay`
**Result:** Templates like "Benefit Delay Issue" and "Universal Credit Changes" are moved to the "Relevant Suggestions" category at the top with 🎯 badges.

### Payment Arrangements
**Type:** `agreed weekly payment plan`
**Result:** "Payment Arrangement Offer" template is moved to "Relevant Suggestions" category with high relevance score.

### Repair Issues
**Type:** `multiple repair outstanding`
**Result:** "Repair Issues Affecting Payment" template appears in "Relevant Suggestions" at the top, matching both "repair" (singular/plural) and "issues".

### Court Action
**Type:** `considering legal action`
**Result:** "Court Action Warning" and related legal templates are moved to "Relevant Suggestions" category for immediate access.

## Technical Features

### Performance Optimizations
- **300ms debounce delay** prevents lag while typing
- **Efficient scoring algorithm** processes in real-time
- **Minimal performance impact** on the interface

### Smart Processing
- **Case-insensitive matching** - works regardless of capitalization
- **Word boundary detection** - avoids false matches in partial words
- **Minimum word length** filtering (>2 characters) for accuracy

### Integration
- **Works alongside search** - manual search filters across all categories including "Relevant Suggestions"
- **Maintains discoverability** - all templates remain visible, relevant ones are promoted to the top
- **Real-time updates** - category placement and scores update as you type without manual refresh
- **Dynamic categorization** - templates automatically move in and out of "Relevant Suggestions" based on content

## Benefits for Rent Collection Agents

### Increased Efficiency
- **Contextual suggestions** appear automatically as you work
- **Reduced scrolling** through long template lists
- **Faster note creation** with relevant templates at the top

### Improved Accuracy
- **Context-aware recommendations** reduce template selection errors
- **Comprehensive coverage** ensures relevant templates aren't missed
- **Visual feedback** makes high-relevance matches obvious

### Enhanced User Experience
- **Intuitive operation** requires no training or manual intervention
- **Progressive enhancement** - enhances existing workflow without disruption
- **Flexible scoring** adapts to different writing styles and terminology

## Template Keywords

The system relies on carefully curated keywords in each response template. Common keywords include:

- **Benefits**: `UC`, `universal credit`, `housing benefit`, `HB`, `DWP`, `delayed`, `late`
- **Payments**: `plan`, `schedule`, `agreement`, `arrears`, `installment`, `weekly`, `monthly`
- **Legal**: `court`, `legal`, `NSP`, `NOSP`, `notice`, `possession`, `proceedings`
- **Circumstances**: `job`, `work`, `employment`, `hardship`, `struggling`, `debt`
- **Property**: `repair`, `maintenance`, `disrepair`, `condition`

## Future Enhancements

The intelligent notes system is designed to learn and improve:
- **Machine learning integration** for personalized relevance scoring
- **Usage analytics** to optimize keyword weightings
- **Context awareness** based on tenant history and case patterns
- **Predictive suggestions** based on common note-writing patterns

---

*The Intelligent Notes System transforms the Quick Responses from a static template library into a dynamic, context-aware assistant that adapts to your workflow in real-time.*