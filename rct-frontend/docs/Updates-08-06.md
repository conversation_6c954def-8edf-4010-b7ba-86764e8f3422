General
o Ideally, notes section would be more accessible, alongside other inputs. Perhaps this can be
part of the Full view?
o I imagine for the full view it would have the calculator in the top left, arrangements bottom
left, notes top right and service charges + benefits calculator etc bottom right. Something
like the sketch below.
o The case log will be user specific so make that obvious.
Calculator Tab
o I like that the output info is clearly not rewritable, but it definitely needs compacting for the
compact view.
o Can you move the shortfall/surplus output up one row, it is the more important info so best
to keep it higher.
o Escalation suggestions should be based on current balance and weekly rent alone, just like
account status on arrangements tab.
o We do not need the note stating ‘account is in debt i.e. positive balance’. I only added that
to my notes to give you context for the examples I used in the spec.
Arrangements
o [Months to clear] on arrangement tab and [Arrears will clear in] on Calculator tab should be
based on the output figure in [Arrears after next payment] on arrangement tab.
o Projection chart – love the switch between list and chart functions, and the context banner
at the top e.g. “Projected balances for successive Mondays (Tenant Weekly: £0.00, Benefits
HB Weekly: £0.00)”. But can we:
o Change X axis labels to (dd mmm)
o On hover over pop-up, show date “(dd/mm/yy)” on one line and then “Balance:
£XXX” on the second line

o The Timeframe input shouldn’t be labelled as weekly. It is more likely to be used as monthly
but should probably be left undefined as the scale could be used interchangeably.
o Would it be possible to allow multiple inputs to be calculated within a single input field, i.e. if
in [Payment Due] I were to put 625 + 38 + 27, so that the field will accept that as 690? If so,
we could also lose the second and third ineligible charge input on the service charges tab.
o In my prototype, arrears after next payment takes the current weekly payments into
account. Yours does not, but I think that your way might be better.
Service charges
o Gross weekly rent can be overridden but the calculations do not update, they remain
based on the figure pulled from the calculator.
o Change the labels “Non-UC service charge” to “Non-eligible service charge” and “UC
service charges (Eligible service charges)  - Weekly” to “Eligible service charge  - Weekly”.
Best not to be too specific there as they actually apply to housing benefit as well as UC.